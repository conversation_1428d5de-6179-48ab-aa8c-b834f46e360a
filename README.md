# 移动端 H5 中台系统

该项目是一个轻量级移动端 H5 中台系统，用于对接其他应用中的 H5 页面，提供统一的接口和服务。

## 技术架构

- Vue 2.x
- Vuex
- Vue Router
- Axios
- Vant 2.x 移动端 UI 框架
- Less
- PostCSS

## 开发环境

```
node >= 10.0.0
npm >= 6.0.0
```

## 项目安装

```bash
npm install --registry=http://registry.npmmirror.com --legacy-peer-deps
```

## 开发

```bash
# 启动开发服务器
npm run dev

# 或者
npm run serve
```

## 构建

```bash
# 构建测试环境
npm run build:test

# 构建生产环境
npm run build:prod
```

## 项目结构

```
├── public             # 静态资源目录
├── src                # 源代码
│   ├── api            # 接口请求模块
│   ├── assets         # 资源文件
│   ├── components     # 公共组件
│   │   └── OfflineNotice.vue # 离线提示组件
│   ├── router         # 路由配置
│   │   ├── index.js   # 路由配置
│   │   └── permission.js # 路由权限控制
│   ├── store          # 状态管理
│   │   ├── index.js   # store配置
│   │   ├── getters.js # 全局getters
│   │   └── modules    # 模块化store
│   ├── styles         # 样式文件
│   │   ├── variables.less # 样式变量
│   │   └── transition.less # 过渡动画样式
│   ├── utils          # 工具函数
│   │   ├── auth.js    # 认证相关工具
│   │   ├── request.js # 请求封装
│   │   ├── network.js # 网络状态监测
│   │   ├── flexible.js # 移动端适配
│   │   └── vconsole.js # 移动端调试工具
│   ├── views          # 页面组件
│   │   └── login      # 登录页面
│   ├── App.vue        # 入口页面
│   └── main.js        # 入口文件
├── .env.development   # 开发环境配置
├── .env.production    # 生产环境配置
├── .env.test          # 测试环境配置
├── .eslintrc.js       # ESLint配置
├── .prettierrc.js     # Prettier配置
├── .vscode            # VSCode配置
│   └── settings.json  # 项目级VSCode设置
├── babel.config.js    # Babel配置
├── postcss.config.js  # PostCSS配置
└── vue.config.js      # Vue CLI配置
```

## 特性

- 多环境配置（开发、测试、生产）
- 移动端适配（viewport 方案）
  - 基于 postcss-px-to-viewport 插件实现
  - 适配多种屏幕尺寸，自动转换像素单位
- 统一的接口请求封装
  - 请求/响应拦截
  - 统一错误处理
  - 请求去重防止重复提交
  - 请求自动重试机制
  - 加载状态自动管理
- 用户认证与授权
  - 基于 token 的认证
  - 路由权限控制
- 状态管理（Vuex 模块化）
- 路由管理
  - 页面权限控制
  - 路由动画过渡效果
- 移动端优化
  - 移动端调试工具（vConsole）
  - 网络状态监测与离线提示
  - 图片资源优化
  - Gzip 压缩
- 代码规范与风格统一
  - ESLint + Prettier 配置
  - 编辑器统一配置
- 按需引入组件，减小打包体积

## 移动端适配方案

本项目使用 `postcss-px-to-viewport` 插件实现移动端适配，自动将 px 单位转换为 vw 单位，实现不同屏幕尺寸的自适应。

### 设计稿尺寸

- 设计稿宽度：750px
- 设计稿高度：1334px（参考值，实际开发仅需关注宽度）

### 开发方式

1. **直接使用设计稿标注的 px 值**：

   ```css
   .example {
     width: 200px; /* 设计稿上是多少px，就写多少 */
     height: 100px;
     font-size: 32px;
     margin: 20px;
   }
   ```

2. **自动转换**：构建时会按照以下公式自动转换为 vw 单位

   ```
   vw = px / 设计稿宽度(750) * 100
   ```

3. **实际示例**：

   ```css
   /* 以下写法 */
   .example {
     width: 200px; /* 设计稿上标注为 200px */
     height: 100px; /* 设计稿上标注为 100px */
     font-size: 32px; /* 设计稿上标注为 32px */
   }

   /* 编译后自动转换为 */
   .example {
     width: 26.67vw; /* 200 ÷ 750 × 100 = 26.67vw */
     height: 13.33vw; /* 100 ÷ 750 × 100 = 13.33vw */
     font-size: 4.27vw; /* 32 ÷ 750 × 100 = 4.27vw */
   }
   ```

### 特殊情况处理

1. **不需要转换的元素**：添加 `.ignore` 或 `.hairlines` 类名

   ```html
   <div class="ignore">这个元素的px不会被转换</div>
   ```

2. **1px 问题**：小于等于 1px 的值不会被转换，保持原样

   ```css
   .border {
     border: 1px solid #000; /* 不会被转换，保持 1px */
   }
   ```

3. **第三方组件**：`node_modules` 目录下的文件不会被转换

### 配置说明

完整配置位于 `postcss.config.js` 文件中：

```js
'postcss-px-to-viewport': {
  viewportWidth: 750, // 设计稿宽度
  viewportHeight: 1334, // 设计稿高度（可选）
  unitPrecision: 5, // 转换后小数点位数
  viewportUnit: 'vw', // 转换后的单位
  selectorBlackList: ['.ignore', '.hairlines'], // 不转换的类名
  minPixelValue: 1, // 小于等于 1px 不转换
  mediaQuery: false, // 是否转换媒体查询中的 px
  exclude: [/node_modules/i] // 忽略的文件路径
}
```

## 移动端调试工具 (vConsole)

本项目集成了 vConsole 调试工具，方便在移动设备上进行调试。

### 配置说明

vConsole 配置位于 `src/utils/vconsole.js`：

```js
import VConsole from 'vconsole';

// 是否为开发环境
const isDev = process.env.NODE_ENV === 'development';
// 是否为测试环境
const isTest = process.env.VUE_APP_ENV === 'test';

// 仅在开发或测试环境中初始化vConsole
export default function initVConsole() {
  if (isDev || isTest) {
    new VConsole();
  }
}
```

### 使用方法

vConsole 会在开发环境和测试环境自动启用，生产环境不会加载此工具。可以通过手机浏览器访问页面，点击右下角的 vConsole 按钮打开调试面板。

## 页面过渡动画

本项目实现了页面切换过渡动画，提升用户体验。

### 动画类型

- `slide-left`: 从右向左划入（前进）
- `slide-right`: 从左向右划入（后退）
- `fade`: 淡入淡出

### 配置说明

过渡动画样式定义在 `src/styles/transition.less`：

```less
// 右侧滑入
.slide-right-enter {
  transform: translate3d(-100%, 0, 0);
}
.slide-right-leave-active {
  transform: translate3d(100%, 0, 0);
}

// 左侧滑入
.slide-left-enter {
  transform: translate3d(100%, 0, 0);
}
.slide-left-leave-active {
  transform: translate3d(-100%, 0, 0);
}

// 淡入淡出
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-active {
  opacity: 0;
}
```

在 `App.vue` 中根据路由深度判断动画方向：

```js
watch: {
  $route(to, from) {
    // 根据路由深度判断是前进还是后退
    const toDepth = to.path.split('/').length;
    const fromDepth = from.path.split('/').length;
    this.transitionName = toDepth < fromDepth ? 'slide-right' : 'slide-left';
  },
}
```

## 网络状态监测

本项目实现了网络状态监测功能，在网络异常时提供友好的用户提示。

### 功能特性

- 网络状态实时监测
- 弱网环境检测与提示
- 离线状态处理
- 网络恢复自动提示

### 配置说明

网络监测工具定义在 `src/utils/network.js`：

```js
// 获取当前网络类型
export const getNetworkType = () => {
  if (navigator.connection && navigator.connection.effectiveType) {
    return navigator.connection.effectiveType; // 'slow-2g', '2g', '3g', '4g'
  }
  return navigator.onLine ? '4g' : 'offline';
};

// 检查是否是弱网环境
export const isSlowNetwork = () => {
  const networkType = getNetworkType();
  return networkType === 'slow-2g' || networkType === '2g';
};

// 初始化网络监听
export const initNetworkMonitor = () => {
  // 初始检查和设置监听器的代码...
};
```

## 资源优化

### 图片资源优化

本项目使用 `image-webpack-loader` 实现图片压缩和优化，显著减小图片体积，提高页面加载速度。

#### 安装与配置

首先需要安装相关依赖：

```bash
npm install image-webpack-loader -D --legacy-peer-deps
```

配置位于 `vue.config.js`：

```js
// 图片优化配置
config.module
  .rule('images')
  .use('image-webpack-loader')
  .loader('image-webpack-loader')
  .options({
    bypassOnDebug: process.env.NODE_ENV !== 'production', // 开发环境下不启用压缩
    mozjpeg: {
      progressive: true, // 创建渐进式jpeg图片
      quality: 65, // 压缩质量，范围0-100
    },
    optipng: {
      enabled: true, // 启用optipng优化器
    },
    pngquant: {
      quality: [0.65, 0.9], // 压缩质量范围，格式为[min, max]
      speed: 4, // 优化速度，1-10，数字越小压缩效果越好，但时间越长
    },
    gifsicle: {
      interlaced: false, // 是否隔行扫描
    },
    webp: {
      quality: 75, // webp格式压缩质量
    },
  })
  .end();
```

#### 参数详细说明

- **bypassOnDebug**: 在开发环境下跳过图片压缩，加快构建速度
- **mozjpeg**: JPEG 图片优化器参数
  - `progressive`: 创建渐进式 JPEG，使图片在加载过程中逐渐清晰
  - `quality`: 压缩质量，0-100，值越低压缩率越高但质量越差
- **optipng**: PNG 图片优化器参数
  - `enabled`: 是否启用 optipng 优化
- **pngquant**: 另一个 PNG 图片优化器参数
  - `quality`: 压缩质量范围[最小值, 最大值]，如[0.65, 0.9]表示保持 65%-90%质量
  - `speed`: 压缩速度，1-10，1 为最慢但压缩效果最好，10 为最快但压缩效果较差
- **gifsicle**: GIF 图片优化器参数
  - `interlaced`: 是否使用隔行扫描，可以实现渐进式加载
- **webp**: WebP 图片转换参数
  - `quality`: WebP 图片质量，0-100

#### 使用建议

1. **根据项目需求调整质量参数**：

   - 照片类图片适合使用 65-80 的质量设置
   - 图标和 UI 元素可以使用更低的质量设置

2. **使用适合的图片格式**：

   - 照片和复杂图像使用 JPEG
   - 需要透明度的图像使用 PNG
   - 动画使用 GIF
   - 考虑使用 WebP 作为现代浏览器的选择

3. **其他优化策略**：

   - 关键图片预加载：`<link rel="preload" href="critical.jpg" as="image">`
   - 非关键图片懒加载：使用 Vant 的 Lazyload 组件
   - 响应式图片：使用`srcset`和`sizes`属性提供不同分辨率的图片

4. **检测优化效果**：
   - 构建前后对比图片大小
   - 使用浏览器开发工具的网络面板监控图片加载时间

### Gzip 压缩

本项目使用 `compression-webpack-plugin` 实现生产环境的静态资源 Gzip 压缩。

配置位于 `vue.config.js`：

```js
// 生产环境启用Gzip压缩
if (isProduction) {
  config.plugins.push(
    new CompressionPlugin({
      filename: '[path][base].gz',
      algorithm: 'gzip',
      test: /\.js$|\.css$|\.html$|\.json$|\.svg$|\.woff$|\.ttf$/,
      threshold: 10240, // 只有大小大于10kb的资源会被处理
      minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
    }),
  );
}
```

## 代码规范与风格统一

本项目使用 ESLint 和 Prettier 进行代码规范和风格的统一管理。

### ESLint 配置

主要配置位于 `.eslintrc.js`：

```js
module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: ['plugin:vue/essential', 'eslint:recommended'],
  parserOptions: {
    parser: 'babel-eslint',
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    quotes: ['error', 'single'],
    semi: ['error', 'always'],
    'comma-dangle': ['error', 'always-multiline'],
  },
};
```

### Prettier 配置

格式化规则位于 `.prettierrc.js`：

```js
module.exports = {
  printWidth: 100, // 每行代码长度
  tabWidth: 2, // 每个tab相当于多少个空格
  useTabs: false, // 是否使用tab进行缩进
  semi: true, // 声明结尾使用分号
  singleQuote: true, // 使用单引号
  trailingComma: 'all', // 多行使用拖尾逗号
  bracketSpacing: true, // 对象字面量的大括号间使用空格
  jsxBracketSameLine: false, // 多行JSX中的>放置在最后一行的结尾，而不是另起一行
  arrowParens: 'avoid', // 只有一个参数的箭头函数的参数是否带圆括号
  htmlWhitespaceSensitivity: 'ignore', // HTML空格敏感度
};
```

### VSCode 编辑器配置

为确保团队统一的开发体验，项目包含了 VSCode 配置文件 `.vscode/settings.json`：

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": ["javascript", "javascriptreact", "vue"],
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "vetur.format.defaultFormatter.html": "prettier",
  "vetur.format.defaultFormatter.js": "prettier"
}
```

## 请求重试机制

为了提高请求的可靠性，特别是在网络不稳定的移动环境下，系统实现了可配置的请求重试机制。

### 功能特点

- 可配置的重试次数
- 可自定义重试延迟时间
- 支持重试提示控制
- 弱网环境自动优化
- 支持全局默认配置和请求级别配置

### 使用方式

在发起请求时，可以通过配置以下参数来控制重试行为：

```javascript
// 示例：登录请求with重试配置
request({
  url: '/user/login',
  method: 'post',
  data: {
    username: 'admin',
    password: '123456',
  },
  // 重试配置
  retry: 2, // 重试次数，0表示不重试
  retryDelay: 1500, // 重试间隔时间（毫秒）
  showRetryTip: true, // 是否显示重试提示
  timeout: 15000, // 请求超时时间
});

// 示例：不显示重试提示的请求
request({
  url: '/api/data',
  method: 'get',
  retry: 1,
  showRetryTip: false, // 静默重试
});
```

### 配置说明

| 参数         | 类型    | 默认值 | 说明                 |
| ------------ | ------- | ------ | -------------------- |
| retry        | number  | 0      | 请求重试次数         |
| retryDelay   | number  | 1000   | 重试间隔时间（毫秒） |
| showRetryTip | boolean | false  | 是否显示重试提示     |
| timeout      | number  | 10000  | 请求超时时间（毫秒） |

### 弱网环境优化

在检测到弱网环境时，系统会自动：

- 延长请求超时时间至 30 秒
- 减少重试次数，避免过多重试
- 显示网络状态提示（可通过 silent 参数控制）

### 注意事项

1. 重试机制默认是关闭的（retry: 0），需要手动配置开启
2. 建议根据接口的重要程度来配置重试次数
3. 对于 POST 等修改数据的请求，需要谨慎使用重试机制
4. 如果接口本身有幂等性保证，可以放心使用重试功能

## Html2Canvas 使用示例

本项目通过 CDN 方式引入了`html2canvas`库，可用于将 DOM 元素转换为 Canvas 图像，适用于截图、保存图片等场景。

### 基本用法

```javascript
// 基本截图示例
function captureScreenshot() {
  const element = document.querySelector('.time-album-content'); // 要截图的DOM元素

  html2canvas(element, {
    scale: window.devicePixelRatio, // 设置截图的缩放比例，保证清晰度
    useCORS: true, // 允许跨域图片
    allowTaint: false, // 不允许污染画布
    backgroundColor: '#ffffff', // 设置背景色
  }).then((canvas) => {
    // 转换为图片并显示/下载
    const imgUrl = canvas.toDataURL('image/png');

    // 方式1: 在页面中显示
    const img = new Image();
    img.src = imgUrl;
    document.body.appendChild(img);

    // 方式2: 触发下载
    const link = document.createElement('a');
    link.download = '时光相册截图.png';
    link.href = imgUrl;
    link.click();
  });
}
```

### 在 Vue 组件中使用

```javascript
// 在Vue组件中使用html2canvas
export default {
  methods: {
    async captureElement() {
      try {
        // 等待图片加载完成
        await this.$nextTick();

        const element = this.$refs.captureTarget;
        const canvas = await html2canvas(element, {
          scale: 2, // 高清截图
          useCORS: true,
          logging: false, // 关闭日志
          scrollX: 0,
          scrollY: 0,
        });

        return canvas.toDataURL('image/jpeg', 0.9); // 使用JPEG格式，90%质量
      } catch (error) {
        console.error('截图失败:', error);
        return null;
      }
    },

    async shareScreenshot() {
      const imgUrl = await this.captureElement();
      if (!imgUrl) return;

      // 分享图片到微信
      if (window.wx && wx.miniProgram) {
        wx.miniProgram.postMessage({
          data: {
            type: 'shareImage',
            imgUrl: imgUrl,
          },
        });
      }
    },
  },
};
```

### 常见应用场景

#### 1. 生成分享卡片

```javascript
// 生成分享卡片
async function generateShareCard() {
  // 1. 准备分享卡片内容
  const cardElement = document.createElement('div');
  cardElement.className = 'share-card';
  cardElement.innerHTML = `
    <div class="card-header">
      <img src="https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png" alt="头像">
      <h3>学员王小明</h3>
    </div>
    <div class="card-content">
      <h2>时光相册精彩瞬间</h2>
      <p>这是时光相册的文案内容，记录学习的点滴...</p>
      <div class="card-image">
        <img src="https://tg-prod.oss-cn-beijing.aliyuncs.com/58b47a94-d95f-4cbd-af9c-f521a73d7a10.jpg" alt="相册图片">
      </div>
    </div>
    <div class="card-footer">
      <img src="/static/images/qrcode.png" alt="二维码">
      <p>扫码查看完整相册</p>
    </div>
  `;

  // 临时添加到页面(但不显示)
  cardElement.style.position = 'absolute';
  cardElement.style.left = '-9999px';
  document.body.appendChild(cardElement);

  // 2. 等待图片加载
  await new Promise((resolve) => setTimeout(resolve, 100));

  // 3. 截图
  const canvas = await html2canvas(cardElement, {
    scale: 2,
    useCORS: true,
    backgroundColor: null,
  });

  // 4. 转换为图片
  const imgUrl = canvas.toDataURL('image/png');

  // 5. 清理DOM
  document.body.removeChild(cardElement);

  return imgUrl;
}
```

#### 2. 长页面截图（分块截图）

```javascript
// 长页面截图方案
async function captureLongPage() {
  const pageElement = document.querySelector('.time-album-content');
  const pageHeight = pageElement.scrollHeight;
  const viewportHeight = window.innerHeight;
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  // 设置画布大小为页面实际大小
  canvas.width = pageElement.scrollWidth * 2; // 乘以2提高清晰度
  canvas.height = pageHeight * 2;

  // 分块截图
  const totalSegments = Math.ceil(pageHeight / viewportHeight);

  for (let i = 0; i < totalSegments; i++) {
    // 滚动到指定位置
    window.scrollTo(0, i * viewportHeight);

    // 等待重绘
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 截取当前可视区域
    const segmentCanvas = await html2canvas(pageElement, {
      scale: 2,
      useCORS: true,
      scrollY: -i * viewportHeight,
      height: Math.min(viewportHeight, pageHeight - i * viewportHeight),
      windowHeight: Math.min(viewportHeight, pageHeight - i * viewportHeight),
    });

    // 绘制到主画布
    ctx.drawImage(
      segmentCanvas,
      0,
      i * viewportHeight * 2, // 位置需要考虑缩放因子
      segmentCanvas.width,
      segmentCanvas.height,
    );
  }

  // 恢复滚动位置
  window.scrollTo(0, 0);

  return canvas.toDataURL('image/png');
}
```

#### 3. 生成海报

```javascript
// 生成海报（带背景和文字内容）
async function generatePoster(albumData) {
  // 1. 创建海报元素
  const posterElement = document.createElement('div');
  posterElement.className = 'time-album-poster';
  posterElement.style.width = '750px';
  posterElement.style.height = '1334px';
  posterElement.style.position = 'absolute';
  posterElement.style.left = '-9999px';
  posterElement.style.background = 'linear-gradient(to bottom, #ffb32f, #ff8411)';
  posterElement.style.color = '#fff';
  posterElement.style.fontFamily = 'PingFang SC';
  posterElement.style.padding = '40px';
  posterElement.style.boxSizing = 'border-box';

  // 2. 添加内容
  posterElement.innerHTML = `
    <div style="text-align:center;padding:30px 0;">
      <img src="https://tg-prod.oss-cn-beijing.aliyuncs.com/72679394-5024-44b6-82b1-8defc5c7f68e.png" style="width:320px;">
      <h1 style="font-size:48px;margin:20px 0;">${albumData.title || '我的时光相册'}</h1>
      <p style="font-size:28px;">${albumData.description || '记录成长的点滴时光'}</p>
    </div>
    
    <div style="display:flex;justify-content:space-between;flex-wrap:wrap;margin-top:40px;">
      ${albumData.images
        .map(
          (img, index) => `
        <div style="width:48%;margin-bottom:20px;position:relative;">
          <img src="${
            img.url
          }" style="width:100%;height:320px;object-fit:cover;border-radius:12px;">
          <p style="position:absolute;bottom:10px;left:10px;background:rgba(0,0,0,0.5);padding:5px 10px;border-radius:5px;font-size:24px;">${
            img.title || `精彩瞬间${index + 1}`
          }</p>
        </div>
      `,
        )
        .join('')}
    </div>
    
    <div style="position:absolute;bottom:40px;left:0;width:100%;text-align:center;">
      <p style="font-size:28px;margin-bottom:20px;">扫描下方二维码，查看完整相册</p>
      <img src="/static/images/qrcode.png" style="width:200px;height:200px;">
    </div>
  `;

  // 3. 添加到DOM
  document.body.appendChild(posterElement);

  // 4. 等待图片加载
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 5. 生成海报图片
  try {
    const canvas = await html2canvas(posterElement, {
      scale: 1,
      useCORS: true,
      allowTaint: true,
      backgroundColor: null,
    });

    const posterUrl = canvas.toDataURL('image/jpeg', 0.9);

    // 6. 清理DOM
    document.body.removeChild(posterElement);

    return posterUrl;
  } catch (error) {
    console.error('生成海报失败:', error);
    document.body.removeChild(posterElement);
    return null;
  }
}

// 使用示例
const albumData = {
  title: '我的2025年度相册',
  description: '记录学习成长的每一个精彩瞬间',
  images: [
    {
      url: 'https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png',
      title: '春游活动',
    },
    {
      url: 'https://tg-prod.oss-cn-beijing.aliyuncs.com/58b47a94-d95f-4cbd-af9c-f521a73d7a10.jpg',
      title: '知识竞赛',
    },
    {
      url: 'https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png',
      title: '实验课程',
    },
    {
      url: 'https://tg-prod.oss-cn-beijing.aliyuncs.com/58b47a94-d95f-4cbd-af9c-f521a73d7a10.jpg',
      title: '户外拓展',
    },
  ],
};

generatePoster(albumData).then((url) => {
  if (url) {
    // 显示或保存海报
    const img = new Image();
    img.src = url;
    document.getElementById('poster-container').appendChild(img);
  }
});
```

### 常见问题与解决方案

1. **跨域图片无法截取**

   ```javascript
   // 解决方案：配置CORS属性并确保图片允许跨域访问
   html2canvas(element, {
     useCORS: true, // 使用CORS加载图片
     allowTaint: false,
   });

   // 还可以为图片添加crossorigin属性
   document.querySelectorAll('img').forEach((img) => {
     img.crossOrigin = 'anonymous';
   });
   ```

2. **截图质量不高/模糊**

   ```javascript
   // 解决方案：增加缩放比例
   html2canvas(element, {
     scale: window.devicePixelRatio * 2, // 使用更高的缩放比例
     imageTimeout: 0, // 不限制图片加载时间
   });
   ```

3. **截图不完整**

   ```javascript
   // 解决方案：等待内容完全加载
   async function captureComplete() {
     // 等待图片加载
     await Promise.all(
       Array.from(document.images)
         .filter((img) => !img.complete)
         .map(
           (img) =>
             new Promise((resolve) => {
               img.onload = img.onerror = resolve;
             }),
         ),
     );

     // 额外等待一段时间确保渲染完成
     await new Promise((resolve) => setTimeout(resolve, 500));

     // 进行截图
     return html2canvas(element, {
       /* 配置项 */
     });
   }
   ```

4. **截图中文字渲染问题**
   ```javascript
   // 解决方案：指定字体渲染方式
   html2canvas(element, {
     letterRendering: true, // 逐字渲染
     useCORS: true,
   });
   ```
5. **截图无法显示圆角的问题**
   ```javascript
   html2canvas(element, {
     backgroundColor: null, // 设置背景为null或者transparent,显示圆角图片
   });
   ```

### 性能优化

对于复杂页面，html2canvas 可能会影响性能，以下是一些优化建议：

```javascript
// 1. 仅截取必要内容
const targetNode = document.querySelector('.specific-content');
html2canvas(targetNode, {
  /* 配置 */
});

// 2. 降低截图尺寸
html2canvas(element, {
  width: element.offsetWidth / 2,
  height: element.offsetHeight / 2,
  scale: 1, // 使用较低的缩放比
});

// 3. 关闭调试信息
html2canvas(element, {
  logging: false,
});

// 4. 在用户操作后延迟执行
document.getElementById('capture-btn').addEventListener('click', () => {
  // 显示加载提示
  showLoading('正在生成截图...');

  // 延迟执行以不阻塞UI
  setTimeout(async () => {
    const imgUrl = await captureElement();
    hideLoading();
    showResult(imgUrl);
  }, 100);
});
```
