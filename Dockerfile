FROM node:lts-buster AS build-dist

# 替换为 Debian archive 源（适用于 Debian 10）
RUN sed -i 's/deb.debian.org/archive.debian.org/g' /etc/apt/sources.list && \
    sed -i 's|security.debian.org/debian-security|archive.debian.org/debian|g' /etc/apt/sources.list && \
    sed -i 's|security.debian.org|archive.debian.org|g' /etc/apt/sources.list && \
    sed -i '/buster\/updates/d' /etc/apt/sources.list 

RUN apt-get update

ADD ./ /workspace

WORKDIR workspace

RUN npm -v

RUN node -v

RUN npm install --registry=http://registry.npmmirror.com --legacy-peer-deps

#RUN npm install --registry=http://nexus.prd.estargo.com.cn:8081/repository/npm/ --legacy-peer-deps

# RUN npx browserslist@latest --update-db
RUN npm run build:env

FROM nginx:stable-alpine
LABEL maintainer="eStarGo <<EMAIL>>"
MAINTAINER eStarGo <<EMAIL>>
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache -U tzdata
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai  /etc/localtime
COPY --from=build-dist /workspace/dist /etc/nginx/html
COPY --from=build-dist /workspace/nginx.conf /etc/nginx/nginx.conf



