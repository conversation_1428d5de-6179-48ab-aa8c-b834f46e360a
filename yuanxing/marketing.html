<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统 - 市场专员看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            background: #000;
            padding: 2px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 38px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8fafc;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .time-filter-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .role-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .metric-card {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }
        .metric-card:hover::before {
            left: 100%;
        }
        .heat-indicator {
            position: relative;
            overflow: hidden;
        }
        .heat-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-2 bg-white rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 头部信息 -->
                <div class="gradient-bg p-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-xl font-bold">聂道人力动态驱动系统</h1>
                            <p class="text-sm opacity-90">才佳林 · 市场专员</p>
                        </div>
                        <!-- 校区切换按钮 -->
                        <div class="relative">
                            <button id="campusSelector" class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl px-4 py-2 flex items-center gap-2 text-sm font-medium hover:bg-opacity-30 transition-all duration-200">
                                <i class="fas fa-building text-sm"></i>
                                <span>北京刘家窑</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- 下拉菜单 -->
                            <div id="campusDropdown" class="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-32 z-50 hidden">
                                <div class="py-2">
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京刘家窑">北京刘家窑</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京德胜门">北京德胜门</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京军博">北京军博</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京双井">北京双井</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

           

                <!-- 时间筛选模块 -->
                <div class="p-4 bg-white border-t border-gray-100">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fas fa-calendar-alt text-blue-500"></i>
                        <span class="font-semibold text-gray-800">时间筛选</span>
                    </div>
                    <div class="flex gap-2 mb-3 overflow-x-auto">
                        <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                    </div>
                    <div class="flex gap-2">
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                        <span class="self-center text-gray-400">至</span>
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                    </div>
                </div>

                <!-- 目标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-bullseye text-red-500"></i>
                        <span class="font-semibold text-gray-800">目标</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <!-- 表单数量 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-file-alt text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">156</div>
                                    <div class="text-xs text-gray-500">/ 200</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">表单数量</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 78%"></div>
                                </div>
                                <span class="text-xs font-medium text-blue-600">78%</span>
                            </div>
                        </div>

                        <!-- 正课新招人数 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user-plus text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">42</div>
                                    <div class="text-xs text-gray-500">/ 50</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">正课新招人数</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 84%"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">84%</span>
                            </div>
                        </div>

                        <!-- 当月到店试听人数 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-headphones text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">68</div>
                                    <div class="text-xs text-gray-500">/ 80</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">当月到店试听人数</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-600">85%</span>
                            </div>
                        </div>

                        <!-- 有效客户信息数量 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">285</div>
                                    <div class="text-xs text-gray-500">/ 350</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">有效客户信息数量</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-orange-500 h-1.5 rounded-full" style="width: 81.4%"></div>
                                </div>
                                <span class="text-xs font-medium text-orange-600">81.4%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 过程指标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-chart-pie text-purple-500"></i>
                        <span class="font-semibold text-gray-800">过程指标</span>
                    </div>
                    
                    <!-- Canvas漏斗图 -->
                    <div class="glass-card p-4 rounded-xl mb-4 relative overflow-hidden">
                        <!-- 背景装饰 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-orange-50/30 rounded-xl"></div>
                        <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-2xl"></div>
                        <div class="absolute bottom-2 left-2 w-16 h-16 bg-gradient-to-br from-green-200/20 to-orange-200/20 rounded-full blur-xl"></div>
                        
                        <!-- Canvas漏斗图容器 -->
                        <div class="relative z-10" style="width: 100%; height: 280px;">
                            <canvas id="funnelCanvas" width="345" height="280" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 热力值模块 -->
                <div class="p-4 pb-6">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-fire text-red-500"></i>
                        <span class="font-semibold text-gray-800">热力值</span>
                    </div>
                    
                    <!-- 实际热力值汇总 -->
                    <div class="glass-card p-4 rounded-xl relative overflow-hidden mb-3">
                        <!-- 背景装饰 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-orange-50/50 via-red-50/30 to-pink-50/50 rounded-xl"></div>
                        <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full blur-2xl"></div>
                        
                        <div class="relative z-10">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-14 h-14 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl flex items-center justify-center">
                                    <i class="fas fa-fire text-white text-xl"></i>
                                </div>
                                <div>
                                     <div class="text-sm text-gray-600">本月实际热力值</div>
                                     <div class="text-3xl font-bold text-gray-800 heat-value">¥6,580</div>
                                 </div>
                            </div>
                            
                            <!-- 热力值组成明细 -->
                            <div class="space-y-3 text-xs">
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-white/60 p-3 rounded-lg border border-orange-100">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-gray-600">采单热力值</span>
                                            <span class="font-bold text-red-700">¥2,303</span>
                                        </div>
                                        <div class="text-xs text-gray-500">占比 35%</div>
                                    </div>
                                    <div class="bg-white/60 p-3 rounded-lg border border-orange-100">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-gray-600">拉访热力值</span>
                                            <span class="font-bold text-blue-700">¥1,974</span>
                                        </div>
                                        <div class="text-xs text-gray-500">占比 30%</div>
                                    </div>
                                </div>
                                <div class="bg-white/60 p-3 rounded-lg border border-orange-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">系统课热力值</span>
                                        <span class="font-bold text-green-700">¥2,303</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 35%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 校区选择器交互
        document.addEventListener('DOMContentLoaded', function() {
            const campusSelector = document.getElementById('campusSelector');
            const campusDropdown = document.getElementById('campusDropdown');
            const campusOptions = document.querySelectorAll('.campus-option');

            campusSelector.addEventListener('click', function() {
                campusDropdown.classList.toggle('hidden');
            });

            campusOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const selectedCampus = this.getAttribute('data-campus');
                    campusSelector.querySelector('span').textContent = selectedCampus;
                    campusDropdown.classList.add('hidden');
                });
            });

            document.addEventListener('click', function(e) {
                if (!campusSelector.contains(e.target)) {
                    campusDropdown.classList.add('hidden');
                }
            });

            // 初始化Canvas漏斗图
            setTimeout(() => {
                initCanvasFunnelChart();
            }, 100);
        });

        // 初始化Canvas漏斗图
        function initCanvasFunnelChart() {
            const canvas = document.getElementById('funnelCanvas');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // 高DPI支持
            const dpr = window.devicePixelRatio || 1;
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            ctx.scale(dpr, dpr);
            
            // 漏斗数据
            const funnelData = [
                {
                    name: '表单有效率',
                    current: 156,
                    target: 200,
                    value: 78,
                    colors: ['#6366f1', '#4f46e5', '#3730a3']
                },
                {
                    name: '邀约率',
                    current: 68,
                    target: 80,
                    value: 85,
                    colors: ['#10b981', '#059669', '#047857']
                },
                {
                    name: '渠道转化率',
                    current: 42,
                    target: 50,
                    value: 84,
                    colors: ['#8b5cf6', '#7c3aed', '#5b21b6']
                },
                {
                    name: '试听转化率',
                    current: 35,
                    target: 42,
                    value: 83,
                    colors: ['#f97316', '#ea580c', '#c2410c']
                }
            ];
            
            // 绘制参数
            const canvasWidth = rect.width;
            const canvasHeight = rect.height;
            const padding = 20;
            const funnelWidth = canvasWidth - padding * 2;
            const funnelHeight = canvasHeight - padding * 2;
            const layerHeight = funnelHeight / funnelData.length;
            const gap = 6;
            
            let animationProgress = 0;
            let hoveredIndex = -1;
            
            // 创建渐变
            function createGradient(ctx, colors, x, y, width, height) {
                const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
                gradient.addColorStop(0, colors[0]);
                gradient.addColorStop(0.5, colors[1]);
                gradient.addColorStop(1, colors[2]);
                return gradient;
            }
            
            // 绘制梯形
            function drawTrapezoid(ctx, x, y, topWidth, bottomWidth, height, colors, index) {
                const leftOffset = (funnelWidth - topWidth) / 2;
                const rightOffset = (funnelWidth - bottomWidth) / 2;
                
                // 计算动画进度
                const progress = Math.min(1, Math.max(0, (animationProgress - index * 0.2) / 0.8));
                const animatedHeight = height * progress;
                const animatedTopWidth = topWidth * progress;
                const animatedBottomWidth = bottomWidth * progress;
                const animatedLeftOffset = (funnelWidth - animatedTopWidth) / 2;
                const animatedRightOffset = (funnelWidth - animatedBottomWidth) / 2;
                
                ctx.save();
                
                // 阴影效果
                if (hoveredIndex === index) {
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    ctx.shadowBlur = 20;
                    ctx.shadowOffsetY = 10;
                } else {
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.15)';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetY = 5;
                }
                
                // 绘制梯形路径
                ctx.beginPath();
                ctx.moveTo(x + animatedLeftOffset, y);
                ctx.lineTo(x + animatedLeftOffset + animatedTopWidth, y);
                ctx.lineTo(x + animatedRightOffset + animatedBottomWidth, y + animatedHeight);
                ctx.lineTo(x + animatedRightOffset, y + animatedHeight);
                ctx.closePath();
                
                // 填充渐变
                const gradient = createGradient(ctx, colors, x, y, funnelWidth, animatedHeight);
                ctx.fillStyle = gradient;
                ctx.fill();
                
                // 边框
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.restore();
                
                return {
                    x: x + animatedLeftOffset,
                    y: y,
                    width: animatedTopWidth,
                    height: animatedHeight,
                    centerX: x + funnelWidth / 2,
                    centerY: y + animatedHeight / 2
                };
            }
            
            // 绘制文本
            function drawText(ctx, data, rect) {
                if (animationProgress < 0.5) return;
                
                ctx.save();
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // 标题
                ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC"';
                ctx.fillText(data.name, rect.centerX, rect.centerY - 10);
                
                // 数据
                ctx.font = '11px -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC"';
                const dataText = `${data.current}/${data.target} (${data.value}%)`;
                ctx.fillText(dataText, rect.centerX, rect.centerY + 8);
                
                ctx.restore();
            }
            
            // 绘制整个漏斗
            function drawFunnel() {
                ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                
                const layerRects = [];
                
                funnelData.forEach((data, index) => {
                    const y = padding + index * (layerHeight - gap);
                    const height = layerHeight - gap;
                    
                    // 计算宽度（基于数值创建漏斗效果）
                    const maxWidth = funnelWidth * 0.9;
                    const minWidth = funnelWidth * 0.3;
                    const topWidth = maxWidth - (index * (maxWidth - minWidth) / (funnelData.length - 1));
                    const bottomWidth = maxWidth - ((index + 1) * (maxWidth - minWidth) / (funnelData.length - 1));
                    
                    const rect = drawTrapezoid(ctx, padding, y, topWidth, bottomWidth, height, data.colors, index);
                    layerRects.push({ ...rect, index, data });
                    
                    drawText(ctx, data, rect);
                });
                
                return layerRects;
            }
            
            // 动画函数
            function animate() {
                animationProgress += 0.02;
                if (animationProgress >= 1.5) {
                    animationProgress = 1.5;
                }
                
                const layerRects = drawFunnel();
                
                if (animationProgress < 1.5) {
                    requestAnimationFrame(animate);
                }
                
                return layerRects;
            }
            
            // 鼠标交互
            let layerRects = [];
            
            canvas.addEventListener('mousemove', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                let newHoveredIndex = -1;
                
                layerRects.forEach((layerRect) => {
                    if (x >= layerRect.x && x <= layerRect.x + layerRect.width && 
                        y >= layerRect.y && y <= layerRect.y + layerRect.height) {
                        newHoveredIndex = layerRect.index;
                    }
                });
                
                if (newHoveredIndex !== hoveredIndex) {
                    hoveredIndex = newHoveredIndex;
                    canvas.style.cursor = hoveredIndex >= 0 ? 'pointer' : 'default';
                    layerRects = drawFunnel();
                }
            });
            
            canvas.addEventListener('mouseleave', () => {
                if (hoveredIndex >= 0) {
                    hoveredIndex = -1;
                    canvas.style.cursor = 'default';
                    layerRects = drawFunnel();
                }
            });
            
            // 开始动画
            layerRects = animate();
        }
    </script>
</body>
</html>