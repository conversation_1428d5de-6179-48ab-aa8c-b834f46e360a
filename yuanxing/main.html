<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统 - 业务演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .demo-container {
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .demo-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-content {
            flex: 1;
            display: flex;
            gap: 16px;
            min-height: 900px;
            overflow-x: auto;
            overflow-y: hidden;
            padding-bottom: 10px;
        }
        
        .demo-content::-webkit-scrollbar {
            height: 8px;
        }
        
        .demo-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .demo-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        .demo-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .scroll-hint {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            padding: 8px 12px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 10;
        }
        
        .scroll-hint.show {
            opacity: 1;
        }

        .iframe-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 16px;
            overflow: hidden;
            position: relative;
            flex-shrink: 0;
            width: 420px;
            min-width: 420px;
        }

        .iframe-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            margin-bottom: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .iframe-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #334155;
        }

        .iframe-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #10b981;
        }

        .iframe-wrapper {
            width: 100%;
            height: calc(100% - 60px);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .iframe-wrapper iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 12px;
        }

        .loading-overlay {
            position: absolute;
            top: 72px;
            left: 16px;
            right: 16px;
            bottom: 16px;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #64748b;
            z-index: 10;
            transition: opacity 0.5s ease-out;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 16px;
            margin-top: 20px;
        }

        .control-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 10px 20px;
            border-radius: 10px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .refresh-btn {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
        }

        .fullscreen-btn {
            background: linear-gradient(135deg, #10b981, #047857);
            color: white;
        }

        .sync-btn {
            background: linear-gradient(135deg, #8b5cf6, #5b21b6);
            color: white;
        }

        .admin-btn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .demo-content {
                gap: 12px;
            }
            
            .iframe-container {
                width: 380px;
                min-width: 380px;
            }
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 10px;
            }
            
            .demo-header {
                padding: 16px;
            }
            
            .iframe-container {
                width: 340px;
                min-width: 340px;
                height: 500px;
            }
            
            .demo-content {
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .iframe-container {
                width: 300px;
                min-width: 300px;
                height: 450px;
            }
        }

        /* 全屏模式 */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-mode iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .fullscreen-exit {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #333;
            transition: all 0.3s ease;
        }

        .fullscreen-exit:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示页面头部 -->
        <div class="demo-header">
            <div class="flex items-center justify-center gap-4 mb-4">
                <div class="w-16 h-16 rounded-2xl overflow-hidden shadow-lg">
                    <img src="logo.png" alt="聂道人力动态驱动系统" class="w-full h-full object-cover">
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">聂道人力动态驱动系统</h1>
                    <p class="text-gray-600">五角色绩效管理看板演示</p>
                </div>
            </div>
            
            <div class="flex items-center justify-center gap-4 text-sm text-gray-600 flex-wrap">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span>校长看板</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span>市场专员看板</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>课程顾问看板</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span>教务看板</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>授课老师看板</span>
                </div>
            </div>
            
            <!-- 滚动提示 -->
            <div class="scroll-hint" id="scroll-hint">
                <div class="flex items-center gap-2 text-white text-sm">
                    <i class="fas fa-arrows-alt-h"></i>
                    <span>左右滑动查看更多看板</span>
                </div>
            </div>
        </div>

        <!-- 五页面展示区域 -->
        <div class="relative">
            <div class="demo-content" id="demo-content">
            <!-- 校长看板 -->
            <div class="iframe-container" id="principal-container">
                <div class="iframe-header">
                    <div class="iframe-title">
                        <i class="fas fa-crown text-red-600"></i>
                        <span>校长绩效看板</span>
                    </div>
                    <div class="iframe-status">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="iframe-wrapper">
                    <div class="loading-overlay" id="principal-loading">
                        <div class="loading-spinner"></div>
                        <div class="text-sm font-medium">加载校长看板...</div>
                    </div>
                    <iframe 
                        src="principal.html" 
                        title="校长绩效看板"
                        id="principal-iframe"
                        onload="hideLoading('principal')"
                        style="opacity: 0; transition: opacity 0.5s ease-in;">
                    </iframe>
                </div>
            </div>

            <!-- 市场专员看板 -->
            <div class="iframe-container" id="marketing-container">
                <div class="iframe-header">
                    <div class="iframe-title">
                        <i class="fas fa-bullhorn text-orange-600"></i>
                        <span>市场专员绩效看板</span>
                    </div>
                    <div class="iframe-status">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="iframe-wrapper">
                    <div class="loading-overlay" id="marketing-loading">
                        <div class="loading-spinner"></div>
                        <div class="text-sm font-medium">加载市场专员看板...</div>
                    </div>
                    <iframe 
                        src="marketing.html" 
                        title="市场专员绩效看板"
                        id="marketing-iframe"
                        onload="hideLoading('marketing')"
                        style="opacity: 0; transition: opacity 0.5s ease-in;">
                    </iframe>
                </div>
            </div>

            <!-- 课程顾问看板 -->
            <div class="iframe-container" id="advisor-container">
                <div class="iframe-header">
                    <div class="iframe-title">
                        <i class="fas fa-user-tie text-blue-600"></i>
                        <span>课程顾问绩效看板</span>
                    </div>
                    <div class="iframe-status">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="iframe-wrapper">
                    <div class="loading-overlay" id="advisor-loading">
                        <div class="loading-spinner"></div>
                        <div class="text-sm font-medium">加载课程顾问看板...</div>
                    </div>
                    <iframe 
                        src="index.html" 
                        title="课程顾问绩效看板"
                        id="advisor-iframe"
                        onload="hideLoading('advisor')"
                        style="opacity: 0; transition: opacity 0.5s ease-in;">
                    </iframe>
                </div>
            </div>

            <!-- 教务看板 -->
            <div class="iframe-container" id="educational-container">
                <div class="iframe-header">
                    <div class="iframe-title">
                        <i class="fas fa-user-graduate text-purple-600"></i>
                        <span>教务绩效看板</span>
                    </div>
                    <div class="iframe-status">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="iframe-wrapper">
                    <div class="loading-overlay" id="educational-loading">
                        <div class="loading-spinner"></div>
                        <div class="text-sm font-medium">加载教务看板...</div>
                    </div>
                    <iframe 
                        src="educational.html" 
                        title="教务绩效看板"
                        id="educational-iframe"
                        onload="hideLoading('educational')"
                        style="opacity: 0; transition: opacity 0.5s ease-in;">
                    </iframe>
                </div>
            </div>

            <!-- 授课老师看板 -->
            <div class="iframe-container" id="teacher-container">
                <div class="iframe-header">
                    <div class="iframe-title">
                        <i class="fas fa-chalkboard-teacher text-green-600"></i>
                        <span>授课老师绩效看板</span>
                    </div>
                    <div class="iframe-status">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>在线</span>
                    </div>
                </div>
                <div class="iframe-wrapper">
                    <div class="loading-overlay" id="teacher-loading">
                        <div class="loading-spinner"></div>
                        <div class="text-sm font-medium">加载授课老师看板...</div>
                    </div>
                    <iframe 
                        src="teacher.html" 
                        title="授课老师绩效看板"
                        id="teacher-iframe"
                        onload="hideLoading('teacher')"
                        style="opacity: 0; transition: opacity 0.5s ease-in;">
                    </iframe>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="text-center mb-3">
                <span class="text-sm font-medium text-gray-600">演示控制</span>
            </div>
            <div class="control-buttons">
                <button class="control-btn refresh-btn" onclick="refreshAll()">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新全部</span>
                </button>
                <button class="control-btn admin-btn" onclick="openAdminDashboard()">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>管理后台</span>
                </button>
                <button class="control-btn fullscreen-btn" onclick="openFullscreen('principal')">
                    <i class="fas fa-expand"></i>
                    <span>校长全屏</span>
                </button>
                <button class="control-btn fullscreen-btn" onclick="openFullscreen('marketing')">
                    <i class="fas fa-expand"></i>
                    <span>市场全屏</span>
                </button>
                <button class="control-btn fullscreen-btn" onclick="openFullscreen('advisor')">
                    <i class="fas fa-expand"></i>
                    <span>顾问全屏</span>
                </button>
                <button class="control-btn fullscreen-btn" onclick="openFullscreen('educational')">
                    <i class="fas fa-expand"></i>
                    <span>教务全屏</span>
                </button>
                <button class="control-btn fullscreen-btn" onclick="openFullscreen('teacher')">
                    <i class="fas fa-expand"></i>
                    <span>老师全屏</span>
                </button>
                <button class="control-btn sync-btn" onclick="syncScrolling()">
                    <i class="fas fa-link"></i>
                    <span>同步滚动</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 全屏容器 -->
    <div id="fullscreen-container" style="display: none;"></div>

    <script>
        let isScrollSynced = false;
        let scrollSyncListeners = [];

        // 隐藏加载状态
        function hideLoading(type) {
            const loadingOverlay = document.getElementById(`${type}-loading`);
            const iframe = document.getElementById(`${type}-iframe`);
            
            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                iframe.style.opacity = '1';
                
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 500);
            }, 800); // 稍微延迟一下，让用户看到加载效果
        }

        // 刷新所有页面
        function refreshAll() {
            const principalIframe = document.getElementById('principal-iframe');
            const marketingIframe = document.getElementById('marketing-iframe');
            const advisorIframe = document.getElementById('advisor-iframe');
            const teacherIframe = document.getElementById('teacher-iframe');
            const educationalIframe = document.getElementById('educational-iframe');
            const principalLoading = document.getElementById('principal-loading');
            const marketingLoading = document.getElementById('marketing-loading');
            const advisorLoading = document.getElementById('advisor-loading');
            const teacherLoading = document.getElementById('teacher-loading');
            const educationalLoading = document.getElementById('educational-loading');

            // 显示加载状态
            principalLoading.style.display = 'flex';
            principalLoading.style.opacity = '1';
            marketingLoading.style.display = 'flex';
            marketingLoading.style.opacity = '1';
            advisorLoading.style.display = 'flex';
            advisorLoading.style.opacity = '1';
            teacherLoading.style.display = 'flex';
            teacherLoading.style.opacity = '1';
            educationalLoading.style.display = 'flex';
            educationalLoading.style.opacity = '1';
            
            // 隐藏iframe
            principalIframe.style.opacity = '0';
            marketingIframe.style.opacity = '0';
            advisorIframe.style.opacity = '0';
            teacherIframe.style.opacity = '0';
            educationalIframe.style.opacity = '0';

            // 刷新iframe
            setTimeout(() => {
                principalIframe.src = principalIframe.src;
                marketingIframe.src = marketingIframe.src;
                advisorIframe.src = advisorIframe.src;
                teacherIframe.src = teacherIframe.src;
                educationalIframe.src = educationalIframe.src;
            }, 300);
        }

        // 全屏显示
        function openFullscreen(type) {
            const iframe = document.getElementById(`${type}-iframe`);
            const container = document.getElementById('fullscreen-container');
            
            container.innerHTML = `
                <button class="fullscreen-exit" onclick="closeFullscreen()">
                    <i class="fas fa-times"></i>
                </button>
                <iframe src="${iframe.src}" style="width: 100%; height: 100%; border: none;"></iframe>
            `;
            
            container.style.display = 'flex';
            container.classList.add('fullscreen-mode');
            document.body.style.overflow = 'hidden';
        }

        // 关闭全屏
        function closeFullscreen() {
            const container = document.getElementById('fullscreen-container');
            container.style.display = 'none';
            container.classList.remove('fullscreen-mode');
            document.body.style.overflow = 'auto';
        }

        // 打开管理后台
        function openAdminDashboard() {
            window.open('admin-dashboard.html', '_blank');
        }

        // 同步滚动
        function syncScrolling() {
            isScrollSynced = !isScrollSynced;
            const syncBtn = event.target.closest('.sync-btn');
            
            if (isScrollSynced) {
                syncBtn.innerHTML = '<i class="fas fa-unlink"></i><span>取消同步</span>';
                syncBtn.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
                
                // 启用滚动同步
                enableScrollSync();
                console.log('✅ 滚动同步已启用');
            } else {
                syncBtn.innerHTML = '<i class="fas fa-link"></i><span>同步滚动</span>';
                syncBtn.style.background = 'linear-gradient(135deg, #8b5cf6, #5b21b6)';
                
                // 禁用滚动同步
                disableScrollSync();
                console.log('❌ 滚动同步已禁用');
            }
        }

        // 启用滚动同步
        function enableScrollSync() {
            const iframeContainers = document.querySelectorAll('.iframe-container');
            let isScrolling = false;
            
            // 使用鼠标滚轮事件来实现同步滚动
            const wheelSyncHandler = function(e) {
                if (isScrolling) return;
                
                // 检查鼠标是否在某个iframe容器上
                const hoveredContainer = Array.from(iframeContainers).find(container => {
                    const rect = container.getBoundingClientRect();
                    return e.clientX >= rect.left && e.clientX <= rect.right &&
                           e.clientY >= rect.top && e.clientY <= rect.bottom;
                });
                
                if (hoveredContainer) {
                    e.preventDefault();
                    isScrolling = true;
                    
                    const scrollAmount = e.deltaY * 0.5; // 控制滚动速度
                    
                    // 同步滚动所有容器
                    iframeContainers.forEach(container => {
                        const iframe = container.querySelector('iframe');
                        if (iframe) {
                            try {
                                // 发送滚动消息到iframe
                                iframe.contentWindow.postMessage({
                                    type: 'scroll',
                                    deltaY: scrollAmount
                                }, '*');
                            } catch (e) {
                                // 如果无法访问iframe内容，则滚动容器本身
                                const wrapper = container.querySelector('.iframe-wrapper');
                                if (wrapper) {
                                    wrapper.scrollTop += scrollAmount;
                                }
                            }
                        }
                    });
                    
                    setTimeout(() => {
                        isScrolling = false;
                    }, 16); // 约60fps的更新频率
                }
            };
            
            // 添加鼠标滚轮监听
            document.addEventListener('wheel', wheelSyncHandler, { passive: false });
            scrollSyncListeners.push({ element: document, handler: wheelSyncHandler, event: 'wheel' });
            
            // 显示同步提示
            showSyncNotification('滚动同步已启用，鼠标滚轮将同步所有看板');
        }

        // 禁用滚动同步
        function disableScrollSync() {
            scrollSyncListeners.forEach(({ element, handler, event }) => {
                element.removeEventListener(event || 'scroll', handler);
            });
            scrollSyncListeners = [];
            showSyncNotification('滚动同步已禁用');
        }

        // 显示同步通知
        function showSyncNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                transition: all 0.3s ease;
                opacity: 0;
                transform: translateX(100%);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFullscreen();
            } else if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshAll();
            } else if (e.key === 'F11') {
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('聂道人力动态驱动系统演示页面已加载');
            
            // 添加一些交互反馈
            const controlBtns = document.querySelectorAll('.control-btn');
            controlBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-2px)';
                    }, 150);
                });
            });
            
            // 初始化滚动提示
            initScrollHint();
        });

        // 初始化滚动提示功能
        function initScrollHint() {
            const demoContent = document.getElementById('demo-content');
            const scrollHint = document.getElementById('scroll-hint');
            
            function checkScrollable() {
                const canScroll = demoContent.scrollWidth > demoContent.clientWidth;
                if (canScroll) {
                    // 显示滚动提示3秒
                    scrollHint.classList.add('show');
                    setTimeout(() => {
                        scrollHint.classList.remove('show');
                    }, 3000);
                }
            }
            
            // 检查是否需要滚动
            setTimeout(checkScrollable, 1000);
            
            // 滚动时隐藏提示
            demoContent.addEventListener('scroll', () => {
                scrollHint.classList.remove('show');
            });
            
            // 窗口大小改变时重新检查
            window.addEventListener('resize', () => {
                setTimeout(checkScrollable, 500);
            });
        }

        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('页面加载错误:', e);
        });

        // iframe错误处理
        document.getElementById('principal-iframe').onerror = function() {
            document.getElementById('principal-loading').innerHTML = `
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-3"></i>
                <div class="text-sm font-medium text-red-600">校长看板加载失败</div>
                <button onclick="refreshAll()" class="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg text-xs">重试</button>
            `;
        };

        document.getElementById('advisor-iframe').onerror = function() {
            document.getElementById('advisor-loading').innerHTML = `
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-3"></i>
                <div class="text-sm font-medium text-red-600">课程顾问看板加载失败</div>
                <button onclick="refreshAll()" class="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg text-xs">重试</button>
            `;
        };

        document.getElementById('teacher-iframe').onerror = function() {
            document.getElementById('teacher-loading').innerHTML = `
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-3"></i>
                <div class="text-sm font-medium text-red-600">老师看板加载失败</div>
                <button onclick="refreshAll()" class="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg text-xs">重试</button>
            `;
        };

        document.getElementById('educational-iframe').onerror = function() {
            document.getElementById('educational-loading').innerHTML = `
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-3"></i>
                <div class="text-sm font-medium text-red-600">教务看板加载失败</div>
                <button onclick="refreshAll()" class="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg text-xs">重试</button>
            `;
        };
    </script>
</body>
</html>