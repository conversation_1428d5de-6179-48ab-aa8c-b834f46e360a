<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统 - 校长看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            background: #000;
            padding: 2px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 38px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8fafc;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .time-filter-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .role-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .metric-card {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }
        .metric-card:hover::before {
            left: 100%;
        }
        .health-indicator {
            position: relative;
            overflow: hidden;
        }
        .health-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            animation: shimmer 2s infinite;
        }
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-2 bg-white rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 头部信息 -->
                <div class="gradient-bg p-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-xl font-bold">聂道人力动态驱动系统</h1>
                            <p class="text-sm opacity-90">才佳林 · 校长</p>
                        </div>
                        <!-- 校区切换按钮 -->
                        <div class="relative">
                            <button id="campusSelector" class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl px-4 py-2 flex items-center gap-2 text-sm font-medium hover:bg-opacity-30 transition-all duration-200">
                                <i class="fas fa-building text-sm"></i>
                                <span>北京刘家窑</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- 下拉菜单 -->
                            <div id="campusDropdown" class="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-32 z-50 hidden">
                                <div class="py-2">
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京刘家窑">北京刘家窑</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京德胜门">北京德胜门</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京军博">北京军博</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京双井">北京双井</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色切换模块 -->
                <div class="p-4 bg-white">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fas fa-users-cog text-purple-500"></i>
                        <span class="font-semibold text-gray-800">角色切换</span>
                    </div>
                    <div class="flex gap-2 overflow-x-auto">
                        <button class="role-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap role-btn" data-role="principal">校长</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap role-btn" data-role="market">市场</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap role-btn" data-role="advisor">课程顾问</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap role-btn" data-role="educational">教务</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap role-btn" data-role="teacher">老师</button>
                    </div>
                </div>

                <!-- 时间筛选模块 -->
                <div class="p-4 bg-white border-t border-gray-100">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fas fa-calendar-alt text-blue-500"></i>
                        <span class="font-semibold text-gray-800">时间筛选</span>
                    </div>
                    <div class="flex gap-2 mb-3 overflow-x-auto">
                        <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                    </div>
                    <div class="flex gap-2">
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                        <span class="self-center text-gray-400">至</span>
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                    </div>
                </div>

                <!-- 目标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-bullseye text-red-500"></i>
                        <span class="font-semibold text-gray-800">目标</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3 mb-3">
                        <!-- 现金流 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-coins text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">¥485K</div>
                                    <div class="text-xs text-gray-500">/ ¥600K</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">现金流</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 80.8%"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">80.8%</span>
                            </div>
                        </div>

                        <!-- 利润 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">¥125K</div>
                                    <div class="text-xs text-gray-500">/ ¥150K</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">利润</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 83.3%"></div>
                                </div>
                                <span class="text-xs font-medium text-blue-600">83.3%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 人数 - 占据一行 -->
                    <div class="glass-card p-3 rounded-xl stat-card">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div>
                                    <div class="text-xl font-bold text-gray-800">285</div>
                                    <div class="text-sm text-gray-600">人数</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500 mb-1">目标: 350人</div>
                                <div class="flex items-center gap-2">
                                    <div class="w-20 bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-purple-500 h-1.5 rounded-full" style="width: 81.4%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-purple-600">81.4%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预算完成度模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-calculator text-orange-500"></i>
                        <span class="font-semibold text-gray-800">预算完成度</span>
                    </div>
                    
                    <!-- 业绩 -->
                    <div class="glass-card p-4 rounded-xl metric-card mb-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-trophy text-white text-lg"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-xl font-bold text-gray-800">¥680K</div>
                                <div class="text-xs text-gray-500">/ ¥800K</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">业绩</div>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                            <span class="text-xs font-medium text-orange-600">85%</span>
                        </div>
                        
                        <!-- 业绩分支 -->
                        <div class="grid grid-cols-3 gap-2 text-xs">
                            <div class="bg-white/60 p-2 rounded-lg border border-orange-100">
                                <div class="text-gray-600 mb-1">新签</div>
                                <div class="font-bold text-blue-700">¥320K</div>
                                <div class="text-gray-500">47%</div>
                            </div>
                            <div class="bg-white/60 p-2 rounded-lg border border-orange-100">
                                <div class="text-gray-600 mb-1">续费</div>
                                <div class="font-bold text-green-700">¥285K</div>
                                <div class="text-gray-500">42%</div>
                            </div>
                            <div class="bg-white/60 p-2 rounded-lg border border-orange-100">
                                <div class="text-gray-600 mb-1">其他</div>
                                <div class="font-bold text-purple-700">¥75K</div>
                                <div class="text-gray-500">11%</div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-3">
                        <!-- 营收（税后） -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-hand-holding-usd text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">营收（税后）</div>
                            <div class="text-lg font-bold text-gray-800">¥578K</div>
                            <div class="text-xs text-gray-500 mb-2">/ ¥680K</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-teal-500 h-1.5 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-xs font-medium text-teal-600">85%</span>
                            </div>
                        </div>

                        <!-- 成本 -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-rose-500 to-pink-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-receipt text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">成本</div>
                            <div class="text-lg font-bold text-gray-800">¥453K</div>
                            <div class="text-xs text-gray-500 mb-2">/ ¥530K</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-rose-500 h-1.5 rounded-full" style="width: 85.5%"></div>
                                </div>
                                <span class="text-xs font-medium text-rose-600">85.5%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 校区健康度模块 -->
                <div class="p-4 pb-6">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-heartbeat text-pink-500"></i>
                        <span class="font-semibold text-gray-800">校区健康度</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mb-3">
                        <!-- 净现金流 -->
                        <div class="glass-card p-3 rounded-xl health-indicator">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-water text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-emerald-600">+¥125K</div>
                                    <div class="text-xs text-emerald-500">健康</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">净现金流</div>
                            <div class="flex items-center">
                                <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-500">流入 > 流出</span>
                            </div>
                        </div>

                        <!-- 净利润 -->
                        <div class="glass-card p-3 rounded-xl health-indicator">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-pie text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-blue-600">+¥98K</div>
                                    <div class="text-xs text-blue-500">良好</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">净利润</div>
                            <div class="flex items-center">
                                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-500">利润率 16.9%</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 人数净增长 - 占据一行 -->
                    <div class="glass-card p-3 rounded-xl health-indicator">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user-plus text-white"></i>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-purple-600">+42人</div>
                                    <div class="text-sm text-gray-600">人数净增长</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-purple-500 mb-1">环比 +18.5%</div>
                                <div class="flex items-center gap-2">
                                    <div class="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                                    <span class="text-xs text-gray-500">新增 > 流失</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 时间筛选功能
            const timeButtons = document.querySelectorAll('button');
            
            timeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.length === 1 || ['周', '月', '季', '半年', '年'].includes(this.textContent)) {
                        // 移除所有按钮的活跃状态
                        timeButtons.forEach(btn => {
                            btn.classList.remove('time-filter-active');
                            btn.classList.add('bg-gray-100', 'text-gray-600');
                        });
                        
                        // 为当前按钮添加活跃状态
                        this.classList.add('time-filter-active');
                        this.classList.remove('bg-gray-100', 'text-gray-600');
                    }
                });
            });

            // 角色切换功能
            const roleButtons = document.querySelectorAll('.role-btn');
            
            roleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的活跃状态
                    roleButtons.forEach(btn => {
                        btn.classList.remove('role-active');
                        btn.classList.add('bg-gray-100', 'text-gray-600');
                    });
                    
                    // 为当前按钮添加活跃状态
                    this.classList.add('role-active');
                    this.classList.remove('bg-gray-100', 'text-gray-600');
                    
                    // 这里可以添加切换到不同角色页面的逻辑
                    const role = this.getAttribute('data-role');
                    console.log('切换到角色:', role);
                });
            });

            // 校区切换功能
            const campusSelector = document.getElementById('campusSelector');
            const campusDropdown = document.getElementById('campusDropdown');
            const campusOptions = document.querySelectorAll('.campus-option');
            
            if (campusSelector && campusDropdown) {
                // 点击按钮显示/隐藏下拉菜单
                campusSelector.addEventListener('click', function(e) {
                    e.stopPropagation();
                    campusDropdown.classList.toggle('hidden');
                });
                
                // 选择校区
                campusOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const selectedCampus = this.getAttribute('data-campus');
                        campusSelector.querySelector('span').textContent = selectedCampus;
                        campusDropdown.classList.add('hidden');
                    });
                });
                
                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function() {
                    campusDropdown.classList.add('hidden');
                });
            }

            // 卡片悬停动画
            const statCards = document.querySelectorAll('.stat-card, .metric-card, .health-indicator');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 数字计数动画
            const numbers = document.querySelectorAll('.text-3xl, .text-2xl, .text-xl, .text-lg');
            numbers.forEach(number => {
                const text = number.textContent;
                if (text.includes('¥')) {
                    let target;
                    if (text.includes('K')) {
                        // 处理包含K的金额，如¥485K
                        target = parseInt(text.replace(/[^\d]/g, '')) * 1000;
                    } else {
                        // 处理普通金额
                        target = parseInt(text.replace(/[^\d]/g, ''));
                    }
                    
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 40; // 40帧动画
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            
                            if (text.includes('K')) {
                                number.textContent = '¥' + Math.floor(current/1000) + 'K';
                            } else {
                                number.textContent = '¥' + Math.floor(current).toLocaleString();
                            }
                        }, 50);
                    }
                } else if (text.includes('%')) {
                    const target = parseFloat(text.replace('%', ''));
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 30;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            number.textContent = current.toFixed(1) + '%';
                        }, 50);
                    }
                } else {
                    const target = parseInt(text.replace(/[^\d]/g, ''));
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 30;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            number.textContent = Math.floor(current);
                        }, 50);
                    }
                }
            });

            // 进度条动画
            const progressBars = document.querySelectorAll('[style*="width:"]');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 1.2s ease-out';
                    bar.style.width = width;
                }, index * 150 + 600);
            });

            // 健康度指标动画
            const healthCards = document.querySelectorAll('.health-indicator');
            healthCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease-out';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200 + 800);
            });

            // 监听来自父页面的滚动同步消息
            window.addEventListener('message', function(event) {
                if (event.data.type === 'scroll') {
                    const contentArea = document.querySelector('.content-area');
                    if (contentArea) {
                        contentArea.scrollTop += event.data.deltaY;
                    }
                }
            });
        });
    </script>
</body>
</html>