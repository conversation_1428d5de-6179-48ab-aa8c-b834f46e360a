<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统 - 老师看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            background: #000;
            padding: 2px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 38px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8fafc;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .time-filter-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-2 bg-white rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 头部信息 -->
                <div class="gradient-bg p-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-xl font-bold">聂道人力动态驱动系统</h1>
                            <p class="text-sm opacity-90">才佳林 · 老师</p>
                        </div>
                        <!-- 校区切换按钮 -->
                        <div class="relative">
                            <button id="campusSelector" class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl px-4 py-2 flex items-center gap-2 text-sm font-medium hover:bg-opacity-30 transition-all duration-200">
                                <i class="fas fa-building text-sm"></i>
                                <span>北京刘家窑</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- 下拉菜单 -->
                            <div id="campusDropdown" class="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-32 z-50 hidden">
                                <div class="py-2">
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京刘家窑">北京刘家窑</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京德胜门">北京德胜门</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京军博">北京军博</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京双井">北京双井</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间筛选模块 -->
                <div class="p-4 bg-white">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fas fa-calendar-alt text-blue-500"></i>
                        <span class="font-semibold text-gray-800">时间筛选</span>
                    </div>
                    <div class="flex gap-2 mb-3 overflow-x-auto">
                        <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                    </div>
                    <div class="flex gap-2">
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                        <span class="self-center text-gray-400">至</span>
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                    </div>
                </div>

                <!-- 目标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-bullseye text-red-500"></i>
                        <span class="font-semibold text-gray-800">目标</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <!-- 应出勤人数 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user-check text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">85</div>
                                    <div class="text-xs text-gray-500">人</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">应出勤人数</div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-500">天工实际排课人数</span>
                            </div>
                        </div>

                        <!-- 试听课应转化人数 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-exchange-alt text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">28</div>
                                    <div class="text-xs text-gray-500">人</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">试听课应转化人数</div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-xs text-gray-500">天工中实际排试听课人数</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 过程指标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-chart-line text-blue-500"></i>
                        <span class="font-semibold text-gray-800">过程指标</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <!-- 实际出勤人数 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-users text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">实际出勤人数</div>
                            <div class="text-2xl font-bold text-gray-800">78</div>
                            <div class="text-xs text-gray-500">人</div>
                        </div>

                        <!-- 已完成试听转化人数 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-check-circle text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">已完成试听转化</div>
                            <div class="text-2xl font-bold text-gray-800">22</div>
                            <div class="text-xs text-gray-500">人</div>
                        </div>

                        <!-- 流失人数 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-user-times text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">流失人数</div>
                            <div class="text-2xl font-bold text-gray-800">7</div>
                            <div class="text-xs text-gray-500">人</div>
                        </div>

                        <!-- 课程总结&作业 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-clipboard-check text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">课程总结&作业</div>
                            <div class="text-2xl font-bold text-gray-800">15</div>
                            <div class="text-xs text-gray-500">份</div>
                        </div>
                    </div>

                    <!-- 过程指标汇总卡片 -->
                   
                </div>

                <!-- 热力值模块 -->
                <div class="p-4 pb-6">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-coins text-yellow-500"></i>
                        <span class="font-semibold text-gray-800">热力值</span>
                    </div>
                    
                    <!-- 实际热力值汇总 -->
                    <div class="glass-card p-4 rounded-xl relative overflow-hidden mb-3">
                        <!-- 背景装饰 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/30 to-purple-50/50 rounded-xl"></div>
                        <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-2xl"></div>
                        
                        <div class="relative z-10">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-14 h-14 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center">
                                    <i class="fas fa-fire text-white text-xl"></i>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600">本月实际热力值</div>
                                    <div class="text-3xl font-bold text-gray-800">¥4,840</div>
                                </div>
                            </div>
                            
                            <!-- 热力值组成明细 -->
                            <div class="grid grid-cols-2 gap-3 text-xs">
                                <div class="bg-white/60 p-3 rounded-lg border border-blue-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">课消热力值</span>
                                        <span class="font-bold text-blue-700">¥3,280</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 67.77%</div>
                                </div>
                                <div class="bg-white/60 p-3 rounded-lg border border-blue-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">试听课热力值</span>
                                        <span class="font-bold text-emerald-700">¥1,560</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 32.23%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 时间筛选功能
        document.addEventListener('DOMContentLoaded', function() {
            const timeButtons = document.querySelectorAll('button');
            
            timeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.length === 1 || ['周', '月', '季', '半年', '年'].includes(this.textContent)) {
                        // 移除所有按钮的活跃状态
                        timeButtons.forEach(btn => {
                            btn.classList.remove('time-filter-active');
                            btn.classList.add('bg-gray-100', 'text-gray-600');
                        });
                        
                        // 为当前按钮添加活跃状态
                        this.classList.add('time-filter-active');
                        this.classList.remove('bg-gray-100', 'text-gray-600');
                    }
                });
            });

            // 校区切换功能
            const campusSelector = document.getElementById('campusSelector');
            const campusDropdown = document.getElementById('campusDropdown');
            const campusOptions = document.querySelectorAll('.campus-option');
            
            if (campusSelector && campusDropdown) {
                // 点击按钮显示/隐藏下拉菜单
                campusSelector.addEventListener('click', function(e) {
                    e.stopPropagation();
                    campusDropdown.classList.toggle('hidden');
                });
                
                // 选择校区
                campusOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const selectedCampus = this.getAttribute('data-campus');
                        campusSelector.querySelector('span').textContent = selectedCampus;
                        campusDropdown.classList.add('hidden');
                    });
                });
                
                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function() {
                    campusDropdown.classList.add('hidden');
                });
            }

            // 卡片悬停动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 数字计数动画
            const numbers = document.querySelectorAll('.text-3xl, .text-2xl, .text-lg');
            numbers.forEach(number => {
                const target = parseInt(number.textContent.replace(/[^\d]/g, ''));
                if (target > 0) {
                    let current = 0;
                    const increment = target / 30; // 30帧动画
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        
                        if (number.textContent.includes('¥')) {
                            number.textContent = '¥' + Math.floor(current).toLocaleString();
                        } else if (number.textContent.includes('%')) {
                            number.textContent = (current / target * parseFloat(number.textContent)).toFixed(1) + '%';
                        } else {
                            number.textContent = Math.floor(current);
                        }
                    }, 50);
                }
            });

            // 监听来自父页面的滚动同步消息
            window.addEventListener('message', function(event) {
                if (event.data.type === 'scroll') {
                    const contentArea = document.querySelector('.content-area');
                    if (contentArea) {
                        contentArea.scrollTop += event.data.deltaY;
                    }
                }
            });
        });
    </script>
</body>
</html>