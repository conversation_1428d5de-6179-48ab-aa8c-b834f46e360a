<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统 - 教务看板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            background: #000;
            padding: 2px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 38px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8fafc;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .time-filter-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            transition: stroke-dasharray 0.8s ease-in-out;
        }
        .metric-card {
            position: relative;
            overflow: hidden;
        }
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        .metric-card:hover::before {
            transform: translateX(100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-2 bg-white rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 头部信息 -->
                <div class="gradient-bg p-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-xl font-bold">聂道人力动态驱动系统</h1>
                            <p class="text-sm opacity-90">才佳林 · 教务</p>
                        </div>
                        <!-- 校区切换按钮 -->
                        <div class="relative">
                            <button id="campusSelector" class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl px-4 py-2 flex items-center gap-2 text-sm font-medium hover:bg-opacity-30 transition-all duration-200">
                                <i class="fas fa-building text-sm"></i>
                                <span>北京刘家窑</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- 下拉菜单 -->
                            <div id="campusDropdown" class="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-32 z-50 hidden">
                                <div class="py-2">
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京刘家窑">北京刘家窑</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京德胜门">北京德胜门</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京军博">北京军博</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京双井">北京双井</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间筛选模块 -->
                <div class="p-4 bg-white">
                    <div class="flex items-center gap-2 mb-3">
                        <i class="fas fa-calendar-alt text-blue-500"></i>
                        <span class="font-semibold text-gray-800">时间筛选</span>
                    </div>
                    <div class="flex gap-2 mb-3 overflow-x-auto">
                        <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                    </div>
                    <div class="flex gap-2">
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                        <span class="self-center text-gray-400">至</span>
                        <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                    </div>
                </div>

                <!-- 目标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-bullseye text-red-500"></i>
                        <span class="font-semibold text-gray-800">目标</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                        <!-- 正课续费总人数 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user-plus text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">145</div>
                                    <div class="text-xs text-gray-500">/ 180人</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">正课续费总人数</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 80.6%"></div>
                                </div>
                                <span class="text-xs font-medium text-blue-600">80.6%</span>
                            </div>
                        </div>

                        <!-- 老生续费业绩 -->
                        <div class="glass-card p-3 rounded-xl stat-card">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-800">¥285K</div>
                                    <div class="text-xs text-gray-500">/ ¥350K</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">老生续费业绩</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-1.5 mr-2">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 81.4%"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">81.4%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 过程指标模块 -->
                <div class="p-4">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-chart-pie text-purple-500"></i>
                        <span class="font-semibold text-gray-800">过程指标</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <!-- 流失率 -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-user-times text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-800">8.5%</div>
                                    <div class="text-xs text-red-500">目标 ≤10%</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">流失率</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">85%</span>
                            </div>
                        </div>

                        <!-- 活跃率 -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-800">92.3%</div>
                                    <div class="text-xs text-green-500">目标 ≥90%</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">活跃率</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 92.3%"></div>
                                </div>
                                <span class="text-xs font-medium text-green-600">102.6%</span>
                            </div>
                        </div>

                        <!-- 满班率 -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-800">88.7%</div>
                                    <div class="text-xs text-blue-500">目标 ≥85%</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">满班率</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 88.7%"></div>
                                </div>
                                <span class="text-xs font-medium text-blue-600">104.4%</span>
                            </div>
                        </div>

                        <!-- 学员出勤率 -->
                        <div class="glass-card p-4 rounded-xl metric-card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar-check text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-800">94.2%</div>
                                    <div class="text-xs text-purple-500">目标 ≥92%</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">学员出勤率</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-purple-500 h-2 rounded-full" style="width: 94.2%"></div>
                                </div>
                                <span class="text-xs font-medium text-purple-600">102.4%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 小程序绑定（占据一行） -->
                    <div class="glass-card p-4 rounded-xl metric-card">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-mobile-alt text-white text-lg"></i>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-gray-800">小程序绑定</div>
                                    <div class="text-sm text-gray-600">485人</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">目标: 520人</div>
                                <div class="flex items-center gap-2 mt-1">
                                    <div class="w-20 bg-gray-200 rounded-full h-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 93.3%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-orange-600">93.3%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 热力值模块 -->
                <div class="p-4 pb-6">
                    <div class="flex items-center gap-2 mb-4">
                        <i class="fas fa-coins text-yellow-500"></i>
                        <span class="font-semibold text-gray-800">热力值</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3 mb-3">
                        <!-- 续费率 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-percentage text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">续费率</div>
                            <div class="text-xl font-bold text-gray-800">34.56%</div>
                        </div>

                        <!-- 续费人头 -->
                        <div class="glass-card p-4 rounded-xl stat-card">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mb-3">
                                <i class="fas fa-user-plus text-white"></i>
                            </div>
                            <div class="text-sm text-gray-600 mb-1">续费人头</div>
                            <div class="text-xl font-bold text-gray-800">12人</div>
                        </div>
                    </div>

                    <!-- 续费课包情况 -->
                    <div class="glass-card p-4 rounded-xl mb-3">
                        <div class="flex items-center gap-3 mb-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-box text-white"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-600">续费课包情况</div>
                                <div class="text-lg font-bold text-gray-800">298个</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div class="text-center p-2 bg-blue-50 rounded-lg">
                                <div class="font-medium text-blue-700">30课包</div>
                                <div class="text-blue-600">85个</div>
                            </div>
                            <div class="text-center p-2 bg-green-50 rounded-lg">
                                <div class="font-medium text-green-700">60课包</div>
                                <div class="text-green-600">96个</div>
                            </div>
                            <div class="text-center p-2 bg-purple-50 rounded-lg">
                                <div class="font-medium text-purple-700">90课包</div>
                                <div class="text-purple-600">72个</div>
                            </div>
                            <div class="text-center p-2 bg-orange-50 rounded-lg">
                                <div class="font-medium text-orange-700">120课包</div>
                                <div class="text-orange-600">45个</div>
                            </div>
                        </div>
                    </div>

                    <!-- 实际续费业绩流水 -->
                    <div class="glass-card p-4 rounded-xl mb-3">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-bar text-white text-lg"></i>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600">实际续费业绩流水</div>
                                    <div class="text-2xl font-bold text-gray-800">¥1,640.18</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-500">月度目标达成</div>
                                <div class="text-lg font-bold text-green-600">81.4%</div>
                            </div>
                        </div>
                    </div>

                    <!-- 实际薪酬汇总 -->
                    <div class="glass-card p-4 rounded-xl relative overflow-hidden">
                        <!-- 背景装饰 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-teal-50/30 to-cyan-50/50 rounded-xl"></div>
                        <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full blur-2xl"></div>
                        
                        <div class="relative z-10">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-14 h-14 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-2xl flex items-center justify-center">
                                    <i class="fas fa-wallet text-white text-xl"></i>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-600">本月实际热力值</div>
                                    <div class="text-3xl font-bold text-gray-800">¥7,200</div>
                                </div>
                            </div>
                            
                            <!-- 热力值组成明细 -->
                            <div class="grid grid-cols-2 gap-3 text-xs">
                                <div class="bg-white/60 p-3 rounded-lg border border-emerald-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">续费率热力值</span>
                                        <span class="font-bold text-blue-700">¥2,850</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 34.56%</div>
                                </div>
                                <div class="bg-white/60 p-3 rounded-lg border border-emerald-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">续费人头热力值</span>
                                        <span class="font-bold text-green-700">12人</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 14.2%</div>
                                </div>
                                <div class="bg-white/60 p-3 rounded-lg border border-emerald-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">续费课包热力值</span>
                                        <span class="font-bold text-purple-700">¥2,130</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 29.6%</div>
                                </div>
                                <div class="bg-white/60 p-3 rounded-lg border border-emerald-100">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-gray-600">续费业绩热力值</span>
                                        <span class="font-bold text-orange-700">¥1,020</span>
                                    </div>
                                    <div class="text-xs text-gray-500">占比 14.2%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 时间筛选功能
        document.addEventListener('DOMContentLoaded', function() {
            const timeButtons = document.querySelectorAll('button');
            
            timeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.length === 1 || ['周', '月', '季', '半年', '年'].includes(this.textContent)) {
                        // 移除所有按钮的活跃状态
                        timeButtons.forEach(btn => {
                            btn.classList.remove('time-filter-active');
                            btn.classList.add('bg-gray-100', 'text-gray-600');
                        });
                        
                        // 为当前按钮添加活跃状态
                        this.classList.add('time-filter-active');
                        this.classList.remove('bg-gray-100', 'text-gray-600');
                    }
                });
            });

            // 校区切换功能
            const campusSelector = document.getElementById('campusSelector');
            const campusDropdown = document.getElementById('campusDropdown');
            const campusOptions = document.querySelectorAll('.campus-option');
            
            if (campusSelector && campusDropdown) {
                // 点击按钮显示/隐藏下拉菜单
                campusSelector.addEventListener('click', function(e) {
                    e.stopPropagation();
                    campusDropdown.classList.toggle('hidden');
                });
                
                // 选择校区
                campusOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const selectedCampus = this.getAttribute('data-campus');
                        campusSelector.querySelector('span').textContent = selectedCampus;
                        campusDropdown.classList.add('hidden');
                    });
                });
                
                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function() {
                    campusDropdown.classList.add('hidden');
                });
            }

            // 卡片悬停动画
            const statCards = document.querySelectorAll('.stat-card, .metric-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 数字计数动画
            const numbers = document.querySelectorAll('.text-3xl, .text-2xl, .text-xl, .text-lg');
            numbers.forEach(number => {
                const text = number.textContent;
                if (text.includes('¥')) {
                    let target;
                    if (text.includes('K')) {
                        // 处理包含K的金额，如¥285K
                        target = parseInt(text.replace(/[^\d]/g, '')) * 1000;
                    } else {
                        // 处理普通金额
                        target = parseInt(text.replace(/[^\d]/g, ''));
                    }
                    
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 40; // 40帧动画
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            
                            if (text.includes('K')) {
                                number.textContent = '¥' + Math.floor(current/1000) + 'K';
                            } else {
                                number.textContent = '¥' + Math.floor(current).toLocaleString();
                            }
                        }, 50);
                    }
                } else if (text.includes('%')) {
                    const target = parseFloat(text.replace('%', ''));
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 30;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            number.textContent = current.toFixed(1) + '%';
                        }, 50);
                    }
                } else {
                    const target = parseInt(text.replace(/[^\d]/g, ''));
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 30;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            number.textContent = Math.floor(current);
                        }, 50);
                    }
                }
            });

            // 进度条动画
            const progressBars = document.querySelectorAll('[style*="width:"]');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 1.2s ease-out';
                    bar.style.width = width;
                }, index * 150 + 600);
            });

            // 指标卡片光效动画
            const metricCards = document.querySelectorAll('.metric-card');
            metricCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease-out';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // 监听来自父页面的滚动同步消息
            window.addEventListener('message', function(event) {
                if (event.data.type === 'scroll') {
                    const contentArea = document.querySelector('.content-area');
                    if (contentArea) {
                        contentArea.scrollTop += event.data.deltaY;
                    }
                }
            });
        });
    </script>
</body>
</html>