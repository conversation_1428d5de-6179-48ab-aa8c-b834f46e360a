<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聂道人力动态驱动系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            background: #000;
            padding: 2px;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 38px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px);
            overflow-y: auto;
            background: #f8fafc;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .time-filter-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .role-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .role-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }
        .role-active {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- iOS状态栏 -->
            <div class="status-bar">
                <div class="flex items-center gap-1">
                    <span>9:41</span>
                </div>
                <div class="flex items-center gap-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <div class="w-6 h-3 border border-white rounded-sm">
                        <div class="w-4 h-2 bg-white rounded-sm m-0.5"></div>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 头部信息 -->
                <div class="gradient-bg p-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h1 class="text-xl font-bold">聂道人力动态驱动系统</h1>
                            <p class="text-sm opacity-90">才佳林 · 课程顾问</p>
                        </div>
                        <!-- 校区切换按钮 -->
                        <div class="relative">
                            <button id="campusSelector" class="bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl px-4 py-2 flex items-center gap-2 text-sm font-medium hover:bg-opacity-30 transition-all duration-200">
                                <i class="fas fa-building text-sm"></i>
                                <span>北京刘家窑</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <!-- 下拉菜单 -->
                            <div id="campusDropdown" class="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-32 z-50 hidden">
                                <div class="py-2">
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京刘家窑">北京刘家窑</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京德胜门">北京德胜门</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京军博">北京军博</button>
                                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 campus-option" data-campus="北京双井">北京双井</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色选择 -->
               

                <!-- 课程顾问看板内容 -->
                <div id="advisor-content">
                    <!-- 时间筛选模块 -->
                    <div class="p-4 bg-white border-t border-gray-100">
                        <div class="flex items-center gap-2 mb-3">
                            <i class="fas fa-calendar-alt text-blue-500"></i>
                            <span class="font-semibold text-gray-800">时间筛选</span>
                        </div>
                        <div class="flex gap-2 mb-3 overflow-x-auto">
                            <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                        </div>
                        <div class="flex gap-2">
                            <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                            <span class="self-center text-gray-400">至</span>
                            <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                        </div>
                    </div>

                    <!-- 目标模块 -->
                    <div class="p-4">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-bullseye text-red-500"></i>
                            <span class="font-semibold text-gray-800">目标</span>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <!-- 正课新招人数 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-users text-blue-600 text-sm"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-gray-800">15</div>
                                        <div class="text-xs text-gray-500">/ 20人</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">正课新招人数</div>
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-blue-600">75%</span>
                                </div>
                            </div>

                            <!-- 到店试听人数 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-store text-green-600 text-sm"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-gray-800">45</div>
                                        <div class="text-xs text-gray-500">/ 60人</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">到店试听人数</div>
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-green-600">75%</span>
                                </div>
                            </div>

                            <!-- 当月试听转化率 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-chart-line text-purple-600 text-sm"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-gray-800">33.3</div>
                                        <div class="text-xs text-gray-500">/ 40%</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">试听转化率</div>
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-purple-500 h-2 rounded-full" style="width: 83%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-purple-600">83%</span>
                                </div>
                            </div>

                            <!-- 签单率 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-handshake text-orange-600 text-sm"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-gray-800">25</div>
                                        <div class="text-xs text-gray-500">/ 30%</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">签单率</div>
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 83%"></div>
                                    </div>
                                    <span class="text-xs font-medium text-orange-600">83%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 新签流水金额 -->
                        <div class="glass-card p-4 rounded-xl mt-3">
                            <div class="flex items-center justify-between mb-2">
                                <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-yen-sign text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-800">¥158,000</div>
                                    <div class="text-xs text-gray-500">/ ¥200,000</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 mb-2">新签流水金额</div>
                            <div class="flex items-center justify-between">
                                <div class="flex-1 bg-gray-200 rounded-full h-3 mr-2">
                                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full" style="width: 79%"></div>
                                </div>
                                <span class="text-sm font-medium text-orange-600">79%</span>
                            </div>
                        </div>
                    </div>

                    <!-- 过程指标模块 -->
                    <div class="p-4">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-funnel-dollar text-blue-500"></i>
                            <span class="font-semibold text-gray-800">过程指标</span>
                        </div>
                        
                        <!-- Canvas漏斗图 -->
                        <div class="glass-card p-4 rounded-xl mb-4 relative overflow-hidden">
                            <!-- 背景装饰 -->
                            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-orange-50/30 rounded-xl"></div>
                            <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full blur-2xl"></div>
                            <div class="absolute bottom-2 left-2 w-16 h-16 bg-gradient-to-br from-green-200/20 to-orange-200/20 rounded-full blur-xl"></div>
                            
                            <!-- Canvas漏斗图容器 -->
                            <div class="relative z-10" style="width: 100%; height: 280px;">
                                <canvas id="funnelCanvas" width="345" height="280" class="w-full h-full"></canvas>
                            </div>
                            
                            <!-- 转化率指标 -->
                            <div class="mt-4 grid grid-cols-2 gap-3 text-xs relative z-10">
                                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-lg border border-blue-200/50 shadow-sm">
                                    <div class="flex items-center gap-2 mb-1">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <div class="text-blue-700 font-medium">邀约率</div>
                                    </div>
                                    <div class="text-blue-900 font-bold text-sm">56.25%</div>
                                    <div class="text-blue-600 text-xs opacity-75">试听/有效</div>
                                </div>
                                <div class="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg border border-green-200/50 shadow-sm">
                                    <div class="flex items-center gap-2 mb-1">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <div class="text-green-700 font-medium">试听转化率</div>
                                    </div>
                                    <div class="text-green-900 font-bold text-sm">33.33%</div>
                                    <div class="text-green-600 text-xs opacity-75">新招/试听</div>
                                </div>
                                <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg border border-purple-200/50 shadow-sm">
                                    <div class="flex items-center gap-2 mb-1">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <div class="text-purple-700 font-medium">渠道转化率</div>
                                    </div>
                                    <div class="text-purple-900 font-bold text-sm">18.75%</div>
                                    <div class="text-purple-600 text-xs opacity-75">新招/有效</div>
                                </div>
                                <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-3 rounded-lg border border-orange-200/50 shadow-sm">
                                    <div class="flex items-center gap-2 mb-1">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                                        <div class="text-orange-700 font-medium">当月转化率</div>
                                    </div>
                                    <div class="text-orange-900 font-bold text-sm">26.67%</div>
                                    <div class="text-orange-600 text-xs opacity-75">当月/试听</div>
                                </div>
                            </div>
                        </div>

                        <!-- 过程指标子项 -->
                        <div class="grid grid-cols-1 gap-3">
                            <!-- 电话沟通人数 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-phone text-blue-600"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-800">电话沟通人数</div>
                                            <div class="text-sm text-gray-500">65/80人</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-blue-600">81.25%</div>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 81%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 电话沟通时长 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-clock text-green-600"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-800">电话沟通时长</div>
                                            <div class="text-sm text-gray-500">120/150小时</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-green-600">80%</div>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 客户跟进人数 -->
                            <div class="glass-card p-4 rounded-xl">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-user-check text-purple-600"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-800">客户跟进人数</div>
                                            <div class="text-sm text-gray-500">72/90人</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-purple-600">80%</div>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-purple-500 h-2 rounded-full" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 热力值模块 -->
                    <div class="p-4 pb-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-coins text-yellow-500"></i>
                            <span class="font-semibold text-gray-800">热力值</span>
                        </div>
                        
                        <!-- 实际热力值汇总 -->
                        <div class="glass-card p-4 rounded-xl relative overflow-hidden mb-3">
                            <!-- 背景装饰 -->
                            <div class="absolute inset-0 bg-gradient-to-br from-yellow-50/50 via-orange-50/30 to-red-50/50 rounded-xl"></div>
                            <div class="absolute top-2 right-2 w-20 h-20 bg-gradient-to-br from-yellow-200/20 to-orange-200/20 rounded-full blur-2xl"></div>
                            
                            <div class="relative z-10">
                                <div class="flex items-center gap-3 mb-4">
                                    <div class="w-14 h-14 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center">
                                        <i class="fas fa-fire text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm text-gray-600">本月实际热力值</div>
                                        <div class="text-3xl font-bold text-gray-800">¥6,750</div>
                                    </div>
                                </div>
                                
                                <!-- 热力值组成明细 -->
                                <div class="grid grid-cols-2 gap-3 text-xs">
                                    <div class="bg-white/60 p-3 rounded-lg border border-yellow-100">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-gray-600">到店试听热力值</span>
                                            <span class="font-bold text-green-700">¥2,250</span>
                                        </div>
                                        <div class="text-xs text-gray-500">占比 33.33%</div>
                                    </div>
                                    <div class="bg-white/60 p-3 rounded-lg border border-yellow-100">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-gray-600">新招热力值</span>
                                            <span class="font-bold text-purple-700">¥4,500</span>
                                        </div>
                                        <div class="text-xs text-gray-500">占比 66.67%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 老师看板内容（初始隐藏） -->
                <div id="teacher-content" style="display: none;">
                    <!-- 时间筛选模块 -->
                    <div class="p-4 bg-white border-t border-gray-100">
                        <div class="flex items-center gap-2 mb-3">
                            <i class="fas fa-calendar-alt text-blue-500"></i>
                            <span class="font-semibold text-gray-800">时间筛选</span>
                        </div>
                        <div class="flex gap-2 mb-3 overflow-x-auto">
                            <button class="time-filter-active px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap">日</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">周</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">月</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">季</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">半年</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap">年</button>
                        </div>
                        <div class="flex gap-2">
                            <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-01">
                            <span class="self-center text-gray-400">至</span>
                            <input type="date" class="flex-1 p-2 border border-gray-200 rounded-lg text-sm" value="2024-01-31">
                        </div>
                    </div>

                    <!-- 目标模块 -->
                    <div class="p-4">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-bullseye text-red-500"></i>
                            <span class="font-semibold text-gray-800">目标</span>
                        </div>
                        <div class="grid grid-cols-1 gap-3">
                            <!-- 应出勤人数 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-user-check text-white text-lg"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-3xl font-bold text-gray-800">85</div>
                                        <div class="text-xs text-gray-500">人</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">应出勤人数</div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-500">天工实际排课人数</span>
                                </div>
                            </div>

                            <!-- 试听课应转化人数 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-exchange-alt text-white text-lg"></i>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-3xl font-bold text-gray-800">28</div>
                                        <div class="text-xs text-gray-500">人</div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">试听课应转化人数</div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-500">天工中实际排试听课人数</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 过程指标模块 -->
                    <div class="p-4">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-chart-line text-blue-500"></i>
                            <span class="font-semibold text-gray-800">过程指标</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <!-- 实际出勤人数 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-3">
                                    <i class="fas fa-users text-white"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">实际出勤人数</div>
                                <div class="text-2xl font-bold text-gray-800">78</div>
                                <div class="text-xs text-gray-500">人</div>
                            </div>

                            <!-- 已完成试听转化人数 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-3">
                                    <i class="fas fa-check-circle text-white"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">已完成试听转化</div>
                                <div class="text-2xl font-bold text-gray-800">22</div>
                                <div class="text-xs text-gray-500">人</div>
                            </div>

                            <!-- 流失人数 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mb-3">
                                    <i class="fas fa-user-times text-white"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">流失人数</div>
                                <div class="text-2xl font-bold text-gray-800">7</div>
                                <div class="text-xs text-gray-500">人</div>
                            </div>

                            <!-- 课程总结&作业 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-3">
                                    <i class="fas fa-clipboard-check text-white"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">课程总结&作业</div>
                                <div class="text-2xl font-bold text-gray-800">15</div>
                                <div class="text-xs text-gray-500">份</div>
                            </div>
                        </div>
                    </div>

                    <!-- 提成模块 -->
                    <div class="p-4 pb-6">
                        <div class="flex items-center gap-2 mb-4">
                            <i class="fas fa-coins text-yellow-500"></i>
                            <span class="font-semibold text-gray-800">提成</span>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <!-- 课消提成 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-book-open text-white text-lg"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">课消提成</div>
                                <div class="text-2xl font-bold text-gray-800">¥3,280</div>
                            </div>

                            <!-- 试听课提成 -->
                            <div class="glass-card p-4 rounded-xl stat-card">
                                <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-headphones text-white text-lg"></i>
                                </div>
                                <div class="text-sm text-gray-600 mb-1">试听课提成</div>
                                <div class="text-2xl font-bold text-gray-800">¥1,560</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 角色切换功能
        function switchRole(role) {
            const advisorContent = document.getElementById('advisor-content');
            const teacherContent = document.getElementById('teacher-content');
            const roleCards = document.querySelectorAll('.role-card');
            
            // 移除所有角色卡片的激活状态
            roleCards.forEach(card => {
                card.classList.remove('role-active');
                const icon = card.querySelector('i');
                const title = card.querySelector('.font-medium');
                const desc = card.querySelector('.text-sm');
                
                // 重置样式
                icon.className = icon.className.replace('text-white', 'text-blue-600');
                card.querySelector('.w-12').classList.remove('bg-white', 'bg-opacity-20');
                card.querySelector('.w-12').classList.add('bg-blue-100');
                title.classList.add('text-gray-800');
                desc.classList.add('text-gray-600');
                title.classList.remove('text-white');
                desc.classList.remove('opacity-75');
            });
            
            if (role === 'advisor') {
                // 激活课程顾问卡片
                const advisorCard = roleCards[0];
                advisorCard.classList.add('role-active');
                const icon = advisorCard.querySelector('i');
                const title = advisorCard.querySelector('.font-medium');
                const desc = advisorCard.querySelector('.text-sm');
                
                icon.classList.remove('text-blue-600');
                icon.classList.add('text-white');
                advisorCard.querySelector('.w-12').classList.remove('bg-blue-100');
                advisorCard.querySelector('.w-12').classList.add('bg-white', 'bg-opacity-20');
                title.classList.remove('text-gray-800');
                desc.classList.remove('text-gray-600');
                desc.classList.add('opacity-75');
                
                // 显示课程顾问内容
                advisorContent.style.display = 'block';
                teacherContent.style.display = 'none';
                
                // 初始化Canvas漏斗图
                setTimeout(() => {
                    initCanvasFunnelChart();
                }, 100);
            } else if (role === 'teacher') {
                // 激活老师卡片
                const teacherCard = roleCards[1];
                teacherCard.classList.add('role-active');
                const icon = teacherCard.querySelector('i');
                const title = teacherCard.querySelector('.font-medium');
                const desc = teacherCard.querySelector('.text-sm');
                
                icon.classList.remove('text-blue-600');
                icon.classList.add('text-white');
                teacherCard.querySelector('.w-12').classList.remove('bg-blue-100');
                teacherCard.querySelector('.w-12').classList.add('bg-white', 'bg-opacity-20');
                title.classList.remove('text-gray-800');
                desc.classList.remove('text-gray-600');
                desc.classList.add('opacity-75');
                
                                 // 显示老师内容
                 advisorContent.style.display = 'none';
                 teacherContent.style.display = 'block';
                 
                 // 初始化老师页面的交互效果
                 setTimeout(() => {
                     initTeacherInteractions();
                 }, 100);
            }
        }

        // 时间筛选功能
        document.addEventListener('DOMContentLoaded', function() {
            const timeButtons = document.querySelectorAll('button');
            
            timeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.textContent.length === 1 || ['周', '月', '季', '半年', '年'].includes(this.textContent)) {
                        // 移除所有按钮的活跃状态
                        timeButtons.forEach(btn => {
                            btn.classList.remove('time-filter-active');
                            btn.classList.add('bg-gray-100', 'text-gray-600');
                        });
                        
                        // 为当前按钮添加活跃状态
                        this.classList.add('time-filter-active');
                        this.classList.remove('bg-gray-100', 'text-gray-600');
                    }
                });
            });

            // 校区切换功能
            const campusSelector = document.getElementById('campusSelector');
            const campusDropdown = document.getElementById('campusDropdown');
            const campusOptions = document.querySelectorAll('.campus-option');
            
            if (campusSelector && campusDropdown) {
                // 点击按钮显示/隐藏下拉菜单
                campusSelector.addEventListener('click', function(e) {
                    e.stopPropagation();
                    campusDropdown.classList.toggle('hidden');
                });
                
                // 选择校区
                campusOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const selectedCampus = this.getAttribute('data-campus');
                        campusSelector.querySelector('span').textContent = selectedCampus;
                        campusDropdown.classList.add('hidden');
                    });
                });
                
                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function() {
                    campusDropdown.classList.add('hidden');
                });
            }

            // 初始化Canvas漏斗图
            initCanvasFunnelChart();

            // 模拟数据加载动画
            const progressBars = document.querySelectorAll('[style*="width:"]');
            progressBars.forEach((bar, index) => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-out';
                    bar.style.width = width;
                }, index * 200 + 500);
            });
        });

        // 初始化Canvas漏斗图
        function initCanvasFunnelChart() {
            const canvas = document.getElementById('funnelCanvas');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // 高DPI支持
            const dpr = window.devicePixelRatio || 1;
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;
            ctx.scale(dpr, dpr);
            
            // 漏斗数据
            const funnelData = [
                {
                    name: '有效人数',
                    current: 80,
                    target: 100,
                    value: 80,
                    colors: ['#6366f1', '#4f46e5', '#3730a3']
                },
                {
                    name: '到店试听人数',
                    current: 45,
                    target: 60,
                    value: 75,
                    colors: ['#10b981', '#059669', '#047857']
                },
                {
                    name: '正课新招人数',
                    current: 15,
                    target: 20,
                    value: 75,
                    colors: ['#8b5cf6', '#7c3aed', '#5b21b6']
                },
                {
                    name: '当月试听转化人数',
                    current: 12,
                    target: 15,
                    value: 80,
                    colors: ['#f97316', '#ea580c', '#c2410c']
                }
            ];
            
            // 绘制参数
            const canvasWidth = rect.width;
            const canvasHeight = rect.height;
            const padding = 20;
            const funnelWidth = canvasWidth - padding * 2;
            const funnelHeight = canvasHeight - padding * 2;
            const layerHeight = funnelHeight / funnelData.length;
            const gap = 6;
            
            let animationProgress = 0;
            let hoveredIndex = -1;
            
            // 创建渐变
            function createGradient(ctx, colors, x, y, width, height) {
                const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
                gradient.addColorStop(0, colors[0]);
                gradient.addColorStop(0.5, colors[1]);
                gradient.addColorStop(1, colors[2]);
                return gradient;
            }
            
            // 绘制梯形
            function drawTrapezoid(ctx, x, y, topWidth, bottomWidth, height, colors, index) {
                const leftOffset = (funnelWidth - topWidth) / 2;
                const rightOffset = (funnelWidth - bottomWidth) / 2;
                
                // 计算动画进度
                const progress = Math.min(1, Math.max(0, (animationProgress - index * 0.2) / 0.8));
                const animatedHeight = height * progress;
                const animatedTopWidth = topWidth * progress;
                const animatedBottomWidth = bottomWidth * progress;
                const animatedLeftOffset = (funnelWidth - animatedTopWidth) / 2;
                const animatedRightOffset = (funnelWidth - animatedBottomWidth) / 2;
                
                ctx.save();
                
                // 阴影效果
                if (hoveredIndex === index) {
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    ctx.shadowBlur = 20;
                    ctx.shadowOffsetY = 10;
                } else {
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.15)';
                    ctx.shadowBlur = 10;
                    ctx.shadowOffsetY = 5;
                }
                
                // 绘制梯形路径
                ctx.beginPath();
                ctx.moveTo(x + animatedLeftOffset, y);
                ctx.lineTo(x + animatedLeftOffset + animatedTopWidth, y);
                ctx.lineTo(x + animatedRightOffset + animatedBottomWidth, y + animatedHeight);
                ctx.lineTo(x + animatedRightOffset, y + animatedHeight);
                ctx.closePath();
                
                // 填充渐变
                const gradient = createGradient(ctx, colors, x, y, funnelWidth, animatedHeight);
                ctx.fillStyle = gradient;
                ctx.fill();
                
                // 边框
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.restore();
                
                return {
                    x: x + animatedLeftOffset,
                    y: y,
                    width: animatedTopWidth,
                    height: animatedHeight,
                    centerX: x + funnelWidth / 2,
                    centerY: y + animatedHeight / 2
                };
            }
            
            // 绘制文本
            function drawText(ctx, data, rect) {
                if (animationProgress < 0.5) return;
                
                ctx.save();
                ctx.fillStyle = '#ffffff';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // 标题
                ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC"';
                ctx.fillText(data.name, rect.centerX, rect.centerY - 10);
                
                // 数据
                ctx.font = '11px -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC"';
                const dataText = `${data.current}/${data.target}人 (${data.value}%)`;
                ctx.fillText(dataText, rect.centerX, rect.centerY + 8);
                
                ctx.restore();
            }
            
            // 绘制整个漏斗
            function drawFunnel() {
                ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                
                const layerRects = [];
                
                funnelData.forEach((data, index) => {
                    const y = padding + index * (layerHeight - gap);
                    const height = layerHeight - gap;
                    
                    // 计算宽度（基于数值创建漏斗效果）
                    const maxWidth = funnelWidth * 0.9;
                    const minWidth = funnelWidth * 0.3;
                    const topWidth = maxWidth - (index * (maxWidth - minWidth) / (funnelData.length - 1));
                    const bottomWidth = maxWidth - ((index + 1) * (maxWidth - minWidth) / (funnelData.length - 1));
                    
                    const rect = drawTrapezoid(ctx, padding, y, topWidth, bottomWidth, height, data.colors, index);
                    layerRects.push({ ...rect, index, data });
                    
                    drawText(ctx, data, rect);
                });
                
                return layerRects;
            }
            
            // 动画函数
            function animate() {
                animationProgress += 0.02;
                if (animationProgress >= 1.5) {
                    animationProgress = 1.5;
                }
                
                const layerRects = drawFunnel();
                
                if (animationProgress < 1.5) {
                    requestAnimationFrame(animate);
                }
                
                return layerRects;
            }
            
            // 鼠标交互
            let layerRects = [];
            
            canvas.addEventListener('mousemove', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                let newHoveredIndex = -1;
                
                layerRects.forEach((layerRect) => {
                    if (x >= layerRect.x && x <= layerRect.x + layerRect.width && 
                        y >= layerRect.y && y <= layerRect.y + layerRect.height) {
                        newHoveredIndex = layerRect.index;
                    }
                });
                
                if (newHoveredIndex !== hoveredIndex) {
                    hoveredIndex = newHoveredIndex;
                    canvas.style.cursor = hoveredIndex >= 0 ? 'pointer' : 'default';
                    layerRects = drawFunnel();
                }
            });
            
            canvas.addEventListener('mouseleave', () => {
                if (hoveredIndex >= 0) {
                    hoveredIndex = -1;
                    canvas.style.cursor = 'default';
                    layerRects = drawFunnel();
                }
            });
            
            // 工具提示
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px;
                border-radius: 8px;
                font-size: 12px;
                pointer-events: none;
                z-index: 1000;
                opacity: 0;
                transition: opacity 0.3s;
                max-width: 200px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC";
            `;
            document.body.appendChild(tooltip);
            
            canvas.addEventListener('mousemove', (e) => {
                if (hoveredIndex >= 0) {
                    const data = funnelData[hoveredIndex];
                    tooltip.innerHTML = `
                        <div style="font-weight: bold; margin-bottom: 6px;">${data.name}</div>
                        <div style="margin-bottom: 4px;">当前完成: <span style="color: #4ade80; font-weight: bold;">${data.current}人</span></div>
                        <div style="margin-bottom: 4px;">目标: <span style="color: #94a3b8;">${data.target}人</span></div>
                        <div>完成率: <span style="color: #fbbf24; font-weight: bold;">${data.value}%</span></div>
                    `;
                    tooltip.style.left = e.pageX + 10 + 'px';
                    tooltip.style.top = e.pageY - 10 + 'px';
                    tooltip.style.opacity = '1';
                } else {
                    tooltip.style.opacity = '0';
                }
            });
            
            canvas.addEventListener('mouseleave', () => {
                tooltip.style.opacity = '0';
            });
            
            // 响应式处理
            window.addEventListener('resize', () => {
                const newRect = canvas.getBoundingClientRect();
                canvas.width = newRect.width * dpr;
                canvas.height = newRect.height * dpr;
                ctx.scale(dpr, dpr);
                animationProgress = 1.5;
                layerRects = drawFunnel();
            });
            
            // 开始动画
            setTimeout(() => {
                layerRects = animate();
            }, 500);
        }

        // 初始化老师页面交互效果
        function initTeacherInteractions() {
            // 卡片悬停动画
            const teacherStatCards = document.querySelectorAll('#teacher-content .stat-card');
            teacherStatCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 数字计数动画
            const teacherNumbers = document.querySelectorAll('#teacher-content .text-3xl, #teacher-content .text-2xl');
            teacherNumbers.forEach(number => {
                const target = parseInt(number.textContent.replace(/[^\d]/g, ''));
                if (target > 0) {
                    let current = 0;
                    const increment = target / 30; // 30帧动画
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        
                        if (number.textContent.includes('¥')) {
                            number.textContent = '¥' + Math.floor(current).toLocaleString();
                        } else {
                            number.textContent = Math.floor(current);
                        }
                    }, 50);
                }
            });
        }

        // 监听来自父页面的滚动同步消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'scroll') {
                const contentArea = document.querySelector('.content-area');
                if (contentArea) {
                    contentArea.scrollTop += event.data.deltaY;
                }
            }
        });
    </script>
</body>
</html>