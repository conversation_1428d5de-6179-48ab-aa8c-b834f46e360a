<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校长看板 - 数据驾驶舱</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .metric-card {
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .chart-container {
            height: 300px;
        }
        
        .large-chart {
            height: 400px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-danger { background-color: #ef4444; }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="dashboard-bg">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <div class="sidebar w-64 flex-shrink-0 p-6">
            <div class="flex items-center mb-8">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-chart-line text-white text-lg"></i>
                </div>
                <h1 class="text-xl font-bold text-gray-800">数据驾驶舱</h1>
            </div>
            
            <nav class="space-y-2">
                <a href="#overview" class="nav-item active flex items-center p-3 rounded-lg bg-blue-50 text-blue-600">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    <span>总览</span>
                </a>
                <a href="#financial" class="nav-item flex items-center p-3 rounded-lg text-gray-600 hover:bg-gray-50">
                    <i class="fas fa-coins mr-3"></i>
                    <span>财务指标</span>
                </a>
                <a href="#performance" class="nav-item flex items-center p-3 rounded-lg text-gray-600 hover:bg-gray-50">
                    <i class="fas fa-trophy mr-3"></i>
                    <span>业绩分析</span>
                </a>
                <a href="#health" class="nav-item flex items-center p-3 rounded-lg text-gray-600 hover:bg-gray-50">
                    <i class="fas fa-heartbeat mr-3"></i>
                    <span>健康度</span>
                </a>
                <a href="#trends" class="nav-item flex items-center p-3 rounded-lg text-gray-600 hover:bg-gray-50">
                    <i class="fas fa-chart-area mr-3"></i>
                    <span>趋势分析</span>
                </a>
            </nav>
            
            <div class="mt-8 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-healthy"></span>
                    <span class="text-sm font-medium text-gray-700">系统状态</span>
                </div>
                <p class="text-xs text-gray-600">所有服务正常运行</p>
                <p class="text-xs text-gray-500 mt-1">最后更新: 2分钟前</p>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部栏 -->
            <div class="header-bar p-4 flex items-center justify-between">
                <div class="flex items-center">
                    <button class="md:hidden mr-4 p-2 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-bars text-gray-600"></i>
                    </button>
                    <h2 class="text-2xl font-bold text-gray-800">校长看板总览</h2>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 时间选择器 -->
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-calendar-alt text-gray-500"></i>
                        <select class="border border-gray-200 rounded-lg px-3 py-2 text-sm">
                            <option>今日</option>
                            <option>本周</option>
                            <option selected>本月</option>
                            <option>本季度</option>
                            <option>本年</option>
                        </select>
                    </div>
                    
                    <!-- 校区选择器 -->
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-building text-gray-500"></i>
                        <select class="border border-gray-200 rounded-lg px-3 py-2 text-sm">
                            <option>全部校区</option>
                            <option selected>北京刘家窑</option>
                            <option>北京德胜门</option>
                            <option>北京军博</option>
                            <option>北京双井</option>
                        </select>
                    </div>
                    
                    <!-- 刷新按钮 -->
                    <button class="p-2 rounded-lg hover:bg-gray-100" onclick="refreshData()">
                        <i class="fas fa-sync-alt text-gray-600"></i>
                    </button>
                    
                    <!-- 返回移动端 -->
                    <button onclick="window.location.href='main.html'" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-mobile-alt mr-2"></i>
                        移动端
                    </button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="flex-1 overflow-auto p-6">
                <!-- 核心指标卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- 现金流 -->
                    <div class="glass-card p-6 rounded-xl metric-card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-coins text-white text-lg"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">目标完成率</div>
                                <div class="text-lg font-bold text-green-600">80.8%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="text-2xl font-bold text-gray-800">¥485K</div>
                            <div class="text-sm text-gray-500">现金流 / ¥600K</div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full transition-all duration-1000" style="width: 80.8%"></div>
                        </div>
                    </div>
                    
                    <!-- 利润 -->
                    <div class="glass-card p-6 rounded-xl metric-card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">目标完成率</div>
                                <div class="text-lg font-bold text-blue-600">83.3%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="text-2xl font-bold text-gray-800">¥125K</div>
                            <div class="text-sm text-gray-500">利润 / ¥150K</div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 83.3%"></div>
                        </div>
                    </div>
                    
                    <!-- 人数 -->
                    <div class="glass-card p-6 rounded-xl metric-card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-violet-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-white text-lg"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">目标完成率</div>
                                <div class="text-lg font-bold text-purple-600">81.4%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="text-2xl font-bold text-gray-800">285</div>
                            <div class="text-sm text-gray-500">人数 / 350人</div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full transition-all duration-1000" style="width: 81.4%"></div>
                        </div>
                    </div>
                    
                    <!-- 业绩 -->
                    <div class="glass-card p-6 rounded-xl metric-card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-trophy text-white text-lg"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">目标完成率</div>
                                <div class="text-lg font-bold text-orange-600">85%</div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="text-2xl font-bold text-gray-800">¥680K</div>
                            <div class="text-sm text-gray-500">业绩 / ¥800K</div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-orange-500 h-2 rounded-full transition-all duration-1000" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- 业绩构成饼图 -->
                    <div class="glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">业绩构成分析</h3>
                            <div class="flex items-center space-x-2">
                                <span class="status-indicator status-healthy"></span>
                                <span class="text-sm text-gray-600">健康</span>
                            </div>
                        </div>
                        <div id="performanceChart" class="chart-container"></div>
                    </div>
                    
                    <!-- 财务趋势图 -->
                    <div class="glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">财务趋势</h3>
                            <div class="flex space-x-4 text-sm">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span>现金流</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                    <span>利润</span>
                                </div>
                            </div>
                        </div>
                        <div id="financialTrendChart" class="chart-container"></div>
                    </div>
                </div>
                
                <!-- 详细数据区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- 预算完成度详情 -->
                    <div class="glass-card p-6 rounded-xl">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">预算完成度</h3>
                        
                        <div class="space-y-4">
                            <!-- 营收 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-teal-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-hand-holding-usd text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">营收（税后）</div>
                                        <div class="text-xs text-gray-500">¥578K / ¥680K</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-teal-600">85%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-1">
                                        <div class="bg-teal-500 h-1 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 成本 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-rose-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-receipt text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">成本</div>
                                        <div class="text-xs text-gray-500">¥453K / ¥530K</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-rose-600">85.5%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-1">
                                        <div class="bg-rose-500 h-1 rounded-full" style="width: 85.5%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 校区健康度 -->
                    <div class="glass-card p-6 rounded-xl">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">校区健康度</h3>
                        
                        <div class="space-y-4">
                            <!-- 净现金流 -->
                            <div class="flex items-center justify-between p-3 bg-emerald-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-water text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">净现金流</div>
                                        <div class="text-xs text-emerald-600">流入 > 流出</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-emerald-600">+¥125K</div>
                                    <div class="text-xs text-emerald-500">健康</div>
                                </div>
                            </div>
                            
                            <!-- 净利润 -->
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-chart-pie text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">净利润</div>
                                        <div class="text-xs text-blue-600">利润率 16.9%</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-blue-600">+¥98K</div>
                                    <div class="text-xs text-blue-500">良好</div>
                                </div>
                            </div>
                            
                            <!-- 人数净增长 -->
                            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">人数净增长</div>
                                        <div class="text-xs text-purple-600">新增 > 流失</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-purple-600">+42人</div>
                                    <div class="text-xs text-purple-500">+18.5%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 业绩分解 -->
                    <div class="glass-card p-6 rounded-xl">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">业绩分解</h3>
                        
                        <div class="space-y-4">
                            <!-- 新签 -->
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">新签</div>
                                        <div class="text-xs text-gray-500">占比 47%</div>
                                    </div>
                                </div>
                                <div class="text-lg font-bold text-blue-600">¥320K</div>
                            </div>
                            
                            <!-- 续费 -->
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-redo text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">续费</div>
                                        <div class="text-xs text-gray-500">占比 42%</div>
                                    </div>
                                </div>
                                <div class="text-lg font-bold text-green-600">¥285K</div>
                            </div>
                            
                            <!-- 其他 -->
                            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-ellipsis-h text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">其他</div>
                                        <div class="text-xs text-gray-500">占比 11%</div>
                                    </div>
                                </div>
                                <div class="text-lg font-bold text-purple-600">¥75K</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 大图表区域 -->
                <div class="glass-card p-6 rounded-xl mb-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">综合趋势分析</h3>
                        <div class="flex space-x-4 text-sm">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span>现金流</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                <span>利润</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                                <span>业绩</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                <span>人数</span>
                            </div>
                        </div>
                    </div>
                    <div id="comprehensiveChart" class="large-chart"></div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 初始化图表
        function initCharts() {
            // 业绩构成饼图
            const performanceChart = echarts.init(document.getElementById('performanceChart'));
            const performanceOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    bottom: '0%',
                    left: 'center'
                },
                series: [
                    {
                        name: '业绩构成',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 320, name: '新签', itemStyle: { color: '#3b82f6' } },
                            { value: 285, name: '续费', itemStyle: { color: '#10b981' } },
                            { value: 75, name: '其他', itemStyle: { color: '#8b5cf6' } }
                        ]
                    }
                ]
            };
            performanceChart.setOption(performanceOption);
            
            // 财务趋势图
            const financialChart = echarts.init(document.getElementById('financialTrendChart'));
            const financialOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '¥{value}K'
                    }
                },
                series: [
                    {
                        name: '现金流',
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: '#10b981' },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                                    { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
                                ]
                            }
                        },
                        data: [320, 380, 420, 450, 470, 480, 485]
                    },
                    {
                        name: '利润',
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: '#3b82f6' },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                                    { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
                                ]
                            }
                        },
                        data: [80, 95, 105, 115, 120, 122, 125]
                    }
                ]
            };
            financialChart.setOption(financialOption);
            
            // 综合趋势图
            const comprehensiveChart = echarts.init(document.getElementById('comprehensiveChart'));
            const comprehensiveOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['现金流', '利润', '业绩', '人数']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '金额(K)',
                        position: 'left',
                        axisLabel: {
                            formatter: '¥{value}K'
                        }
                    },
                    {
                        type: 'value',
                        name: '人数',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}人'
                        }
                    }
                ],
                series: [
                    {
                        name: '现金流',
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: '#10b981' },
                        data: [320, 380, 420, 450, 470, 480, 485, 490, 495, 500, 510, 520]
                    },
                    {
                        name: '利润',
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: '#3b82f6' },
                        data: [80, 95, 105, 115, 120, 122, 125, 128, 130, 135, 140, 145]
                    },
                    {
                        name: '业绩',
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: '#f59e0b' },
                        data: [500, 580, 620, 650, 660, 670, 680, 690, 700, 720, 750, 780]
                    },
                    {
                        name: '人数',
                        type: 'line',
                        smooth: true,
                        yAxisIndex: 1,
                        itemStyle: { color: '#8b5cf6' },
                        data: [200, 220, 240, 250, 260, 270, 285, 290, 295, 300, 310, 320]
                    }
                ]
            };
            comprehensiveChart.setOption(comprehensiveOption);
            
            // 响应式处理
            window.addEventListener('resize', function() {
                performanceChart.resize();
                financialChart.resize();
                comprehensiveChart.resize();
            });
        }
        
        // 刷新数据
        function refreshData() {
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            
            icon.classList.add('fa-spin');
            
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                // 这里可以添加实际的数据刷新逻辑
                console.log('数据已刷新');
            }, 1000);
        }
        
        // 导航切换
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活跃状态
                document.querySelectorAll('.nav-item').forEach(nav => {
                    nav.classList.remove('active', 'bg-blue-50', 'text-blue-600');
                    nav.classList.add('text-gray-600');
                });
                
                // 添加当前活跃状态
                this.classList.add('active', 'bg-blue-50', 'text-blue-600');
                this.classList.remove('text-gray-600');
            });
        });
        
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            
            // 数字动画
            const numbers = document.querySelectorAll('.text-2xl, .text-lg');
            numbers.forEach(number => {
                const text = number.textContent;
                if (text.includes('¥') || text.includes('%')) {
                    let target;
                    if (text.includes('K')) {
                        target = parseInt(text.replace(/[^\d]/g, ''));
                    } else if (text.includes('%')) {
                        target = parseFloat(text.replace('%', ''));
                    } else {
                        target = parseInt(text.replace(/[^\d]/g, ''));
                    }
                    
                    if (target > 0) {
                        let current = 0;
                        const increment = target / 50;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            
                            if (text.includes('K')) {
                                number.textContent = '¥' + Math.floor(current) + 'K';
                            } else if (text.includes('%')) {
                                number.textContent = current.toFixed(1) + '%';
                            } else if (text.includes('¥')) {
                                number.textContent = '¥' + Math.floor(current) + 'K';
                            } else {
                                number.textContent = Math.floor(current);
                            }
                        }, 30);
                    }
                }
            });
        });
    </script>
</body>
</html>