# 课程顾问绩效管理H5页面

一个专为校区课程顾问设计的移动端绩效管理工具，帮助顾问实时了解月度目标和考核绩效。

## 🎯 项目特点

- **移动端优化**: 基于iPhone 15 Pro设计，完美适配移动设备
- **实时数据**: 直观展示各项绩效指标的完成情况
- **Canvas漏斗图**: 原生Canvas实现的高性能立体渐变漏斗图
- **现代设计**: 采用玻璃拟态效果，提升视觉体验
- **iOS风格**: 模拟iOS界面，提供原生应用般的体验

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 运行启动脚本
./scripts/start.sh
```

### 方法二：手动启动

```bash
# 使用Python启动（推荐）
python3 -m http.server 8000

# 或使用Node.js
npx live-server --port=8080

# 然后在浏览器中访问
# http://localhost:8000 或 http://localhost:8080
```

### 方法三：直接打开

直接在浏览器中打开 `index.html` 文件

## 📱 最佳体验

为了获得最佳体验，建议：

1. 使用Chrome或Safari浏览器
2. 开启开发者工具的移动端模拟器
3. 选择iPhone 15 Pro设备尺寸 (393 x 852px)
4. 确保网络连接正常（需要加载CDN资源）

## 🏗️ 项目结构

```
yuanxing/
├── index.html              # 主页面
├── README.md               # 项目说明
├── docs/                   # 技术文档
│   └── 技术说明文档.md
├── discuss/                # 产品文档
│   └── 产品需求文档.md
└── scripts/                # 运行脚本
    └── start.sh
```

## 🎨 功能模块

### 1. 时间筛选模块
- 支持日、周、月、季、半年、年多种时间维度
- 支持自定义时间区间筛选
- 默认选中"日"维度

### 2. 目标模块
展示五个核心指标：
- **正课新招人数**: 15/20人 (75%)
- **到店试听人数**: 45/60人 (75%)
- **当月试听转化率**: 33.3/40% (83%)
- **签单率**: 25/30% (83%)
- **新签流水金额**: ¥158,000/¥200,000 (79%)

### 3. 过程指标模块（Canvas漏斗图）
高性能Canvas绘制的立体漏斗图：
- **有效人数**: 80/100人 (80%) - 蓝色三级渐变
- **到店试听人数**: 45/60人 (75%) - 绿色三级渐变  
- **正课新招人数**: 15/20人 (75%) - 紫色三级渐变
- **当月试听转化人数**: 12/15人 (80%) - 橙色三级渐变

🚀 **Canvas特性**:
- 原生高性能渲染
- 高DPI屏幕适配
- 流畅的60fps动画
- 分层依次出现效果
- 鼠标悬停阴影增强
- 实时跟随工具提示

📊 **转化率指标卡片**:
- 邀约率: 56.25% (试听/有效)
- 试听转化率: 33.33% (新招/试听) 
- 渠道转化率: 18.75% (新招/有效)
- 当月转化率: 26.67% (当月/试听)

附加过程指标：
- 电话沟通人数: 65/80人 (81.25%)
- 电话沟通时长: 120/150小时 (80%)
- 客户跟进人数: 72/90人 (80%)

### 4. 提成模块
- **到店试听提成**: ¥2,250
- **新招提成**: ¥4,500

## 🛠️ 技术栈

- **HTML5**: 语义化结构 + Canvas 2D API
- **Tailwind CSS**: 原子化CSS框架
- **Font Awesome**: 图标库
- **原生JavaScript**: 交互逻辑 + Canvas渲染
- **CSS3**: 动画和视觉效果

## 📊 数据接口设计

详细的接口设计请参考：[技术说明文档](docs/技术说明文档.md)

## 🎨 设计规范

详细的设计规范请参考：[产品需求文档](discuss/产品需求文档.md)

## 🔧 开发说明

### 样式修改
项目使用Tailwind CSS，可以通过修改HTML中的class来调整样式。

### 数据修改
当前使用的是模拟数据，实际使用时需要：
1. 替换JavaScript中的数据源
2. 连接后端API接口
3. 添加数据加载状态处理

### 添加新功能
1. 在HTML中添加新的DOM结构
2. 使用Tailwind CSS添加样式
3. 在JavaScript中添加交互逻辑

## 📝 更新日志

### v1.2.0 (2024-01-01)
- 🚀 Canvas高性能漏斗图重构
- ⚡ 原生渲染，更快更流畅
- 📱 高DPI屏幕完美适配
- 🎭 鼠标交互和工具提示优化

### v1.1.0 (2024-01-01)
- ✨ 全新ECharts漏斗图
- 🎨 3D立体渐变视觉效果
- 💫 丰富的动画和交互效果
- 🎯 优化转化率指标展示

### v1.0.0 (2024-01-01)  
- ✨ 初始版本发布
- 🎨 完成四个核心模块的UI设计
- 📱 适配iOS设备样式
- 🚀 添加启动脚本

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 确认网络连接正常
3. 尝试使用不同的浏览器
4. 查看项目文档获取更多信息

## �� 许可证

本项目仅供学习和演示使用。 