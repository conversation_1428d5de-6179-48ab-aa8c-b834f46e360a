'use strict';
const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
const isProduction = process.env.NODE_ENV === 'production';

function resolve(dir) {
  return path.join(__dirname, dir);
}

const port = process.env.port || 9000; // 开发端口

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    proxy: {
      '/web': {
        target: 'https://tg-api-dev.estar-go.com',
        changeOrigin: true,
        pathRewrite: {
          '^/web': '/web',
        },
        secure: false,
      },
      '/api': {
        target: 'https://tg-api-dev.estar-go.com',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api',
        },
        secure: false,
      },
    },
  },
  configureWebpack: (config) => {
    // 路径别名
    config.resolve.alias = {
      '@': resolve('src'),
    };

    // 生产环境启用Gzip压缩
    if (isProduction) {
      config.plugins.push(
        new CompressionPlugin({
          filename: '[path][base].gz',
          algorithm: 'gzip',
          test: /\.js$|\.css$|\.html$|\.json$|\.svg$|\.woff$|\.ttf$/,
          threshold: 10240, // 只有大小大于10kb的资源会被处理
          minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
        }),
      );
    }
  },
  chainWebpack: (config) => {
    // 图片优化配置
    config.module
      .rule('images')
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        bypassOnDebug: process.env.NODE_ENV !== 'production',
        mozjpeg: {
          progressive: true,
          quality: 65,
        },
        optipng: {
          enabled: true,
        },
        pngquant: {
          quality: [0.65, 0.9],
          speed: 4,
        },
        gifsicle: {
          interlaced: false,
        },
        webp: {
          quality: 75,
        },
      })
      .end();
  },
  css: {
    loaderOptions: {
      // 配置样式变量
      less: {
        // 全局引入变量
        lessOptions: {
          modifyVars: {
            // 主题定制
            'primary-color': '#1989fa',
            'text-color': '#323233',
            'border-color': '#ebedf0',
          },
          javascriptEnabled: true,
        },
        additionalData: '@import "@/styles/variables.less";',
      },
    },
  },
};
