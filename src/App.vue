<template>
  <div id="app">
    <!-- 离线提示组件 -->
    <offline-notice />

    <!-- <transition :name="transitionName"> -->
    <keep-alive v-if="$route.meta.keepAlive">
      <router-view class="router-view" />
    </keep-alive>
    <router-view v-else class="router-view" />
    <!-- </transition> -->
  </div>
</template>

<script>
import OfflineNotice from '@/components/OfflineNotice';

export default {
  name: 'App',
  components: {
    OfflineNotice,
  },
  data() {
    return {
      active: 0,
      transitionName: 'fade',
    };
  },
  computed: {
    // 判断当前是否为登录页
    isLoginPage() {
      return this.$route.path === '/login' || this.$route.path === '/404';
    },
  },
  watch: {
    $route(to, from) {
      // 根据路径更新底部导航激活标签
      if (to.path === '/') {
        this.active = 0;
      } else if (to.path === '/about') {
        this.active = 1;
      }

      // 根据路由深度判断是前进还是后退
      const toDepth = to.path.split('/').length;
      const fromDepth = from.path.split('/').length;
      this.transitionName = toDepth < fromDepth ? 'slide-right' : 'slide-left';
    },
  },
};
</script>

<style lang="less">
@import './styles/variables.less';
@import './styles/transition.less';
@import './styles/common.less';
div,
p,
span,
h1,
h2,
h3,
h4,
h5 {
  box-sizing: border-box;
}
html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
}

#app {
  min-height: 100vh;
  background-color: @background-color;
  box-sizing: border-box;
}

.router-view {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-x: hidden;
  background-color: @background-color;
}
</style>
