import dayjs from 'dayjs';

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
export const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 * 获取url中的参数值
 * @param {string} name 参数名
 * @returns {string} 参数值
 */
export const getUrlParams = (name) => {
  const url = window.location.search;
  const params = new URLSearchParams(url);
  return params.get(name);
};

/**
 * 获取阿里云OSS图片
 * @param {string} url 图片地址
 * @param {number} width 宽度
 * @param {number} height 高度
 * @returns {string} 图片地址
 */
export const getOssImage = (url, width, height) => {
  return `${url}?x-oss-process=image/resize,w_${width},h_${height}`;
};

/**
 * 判断是否为数字
 * @param {any} value 值
 * @returns {boolean} 是否为数字
 */
export const isNumber = (value) => {
  return typeof value === 'number';
};

/**
 * px转vw
 * @param {number} px 像素值
 * @param {number} width 设计稿宽度
 * @returns {string} vw值
 */
export const pxToVw = (px, width = 750) => {
  if (!isNumber(px)) {
    return px;
  }
  return `${(Number(px) / width) * 100}vw`;
};

// 格式化时间
export const formatDate = (time, format = 'YYYY-MM-DD') => {
  return dayjs(time).format(format);
};

// 格式化时间
export const formatTime = (time, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(time).format(format);
};

/**
 * 节流函数 - 在一段时间内只执行一次函数
 * @param {Function} fn 要执行的函数
 * @param {number} delay 延迟时间，单位毫秒
 * @returns {Function} 节流后的函数
 */
export const throttle = (fn, delay = 300) => {
  let timer = null;
  let lastTime = 0;

  return function (...args) {
    const now = Date.now();
    const remaining = delay - (now - lastTime);
    const context = this;

    if (remaining <= 0) {
      lastTime = now;
      clearTimeout(timer);
      timer = null;
      fn.apply(context, args);
    } else if (!timer) {
      timer = setTimeout(() => {
        lastTime = Date.now();
        timer = null;
        fn.apply(context, args);
      }, remaining);
    }
  };
};

/**
 * 防抖函数 - 在最后一次触发后等待一定时间再执行
 * @param {Function} fn 要执行的函数
 * @param {number} delay 延迟时间，单位毫秒
 * @returns {Function} 防抖后的函数
 */
export const debounce = (fn, delay = 300) => {
  let timer = null;

  return function (...args) {
    const context = this;
    if (timer) clearTimeout(timer);

    timer = setTimeout(() => {
      fn.apply(context, args);
      timer = null;
    }, delay);
  };
};
