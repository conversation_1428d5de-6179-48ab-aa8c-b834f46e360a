import axios from 'axios';
import store from '@/store';
import { Toast } from 'vant';
import { isSlowNetwork } from '@/utils/network';

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // 统一使用环境变量配置的API地址
  timeout: 10000, // 请求超时时间
  // withCredentials: true, // 跨域请求时发送cookie
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    client: 'web',
  },
  // 默认不启用重试机制
  retry: 0,
  retryDelay: 1000,
});

// 用于存储pending的请求
const pendingMap = new Map();

/**
 * 生成每个请求唯一的键
 * @param {*} config
 * @returns string
 */
function getPendingKey(config) {
  let { url, method, params, data } = config;
  if (typeof data === 'string') data = JSON.parse(data); // response里面返回的config.data是个字符串对象
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
}

/**
 * 储存每个请求唯一值, 也就是cancel token
 * @param {*} config
 */
function addPending(config) {
  const pendingKey = getPendingKey(config);
  config.cancelToken =
    config.cancelToken ||
    new axios.CancelToken((cancel) => {
      if (!pendingMap.has(pendingKey)) {
        pendingMap.set(pendingKey, cancel);
      }
    });
}

/**
 * 删除重复的请求
 * @param {*} config
 */
function removePending(config) {
  const pendingKey = getPendingKey(config);
  if (pendingMap.has(pendingKey)) {
    const cancelToken = pendingMap.get(pendingKey);
    cancelToken(pendingKey);
    pendingMap.delete(pendingKey);
  }
}

/**
 * 弱网环境下优化请求
 * @param {*} config
 */
function optimizeForSlowNetwork(config) {
  // 检查是否为弱网环境
  if (isSlowNetwork()) {
    // 延长超时时间
    config.timeout = 30000;

    // 对于非GET请求，提醒用户网络环境较差
    if (config.method.toUpperCase() !== 'GET' && !config.silent) {
      Toast({
        message: '当前网络较慢，请耐心等待',
        icon: 'warning-o',
        duration: 2000,
      });
    }

    // 减少重试次数，避免过多重试导致用户等待时间过长
    config.retry = 1;
  }
  return config;
}

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    removePending(config); // 在请求开始前，对之前的请求做检查取消操作
    addPending(config); // 将当前请求添加到pendingMap

    // 弱网环境处理
    config = optimizeForSlowNetwork(config);

    // 请求头添加token
    const token = store.getters.token;
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }

    // 显示加载状态
    if (config.showLoading !== false) {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
    }

    return config;
  },
  (error) => {
    // 请求错误处理
    console.error('请求错误:', error);
    Toast.clear();
    return Promise.reject(error);
  },
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 在请求完成后，移除本次请求
    removePending(response.config);
    // 清除loading
    Toast.clear();

    const res = response.data;
    console.log('response.data-->', res);

    return res;
  },
  (error) => {
    console.log('error-->', error);
    const { response } = error;
    // 清除loading
    Toast.clear();

    // 如果请求被取消，不显示错误信息
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return Promise.reject(error);
    }

    const config = error.config;
    // 检查是否启用了重试机制
    if (!config || !config.retry || config.retry === 0) {
      let message = error.message;
      if (response) {
        switch (error.response.status) {
          case 401:
            message = 'token已过期，请重新登录';
            store.dispatch('user/logout');
            break;
          // case 403:
          //   message = '拒绝访问';
          //   break;
          case 404:
            message = '接口不存在';
            break;
          case 500:
            message = '服务器内部错误';
            break;
          default:
            message = '请求错误';
            break;
        }
      }
      Toast({
        message: message,
        forbidClick: true,
        duration: 2000,
      });
      return Promise.reject(error);
    }

    // 设置重试计数
    config.__retryCount = config.__retryCount || 0;

    // 判断是否超过了重试次数
    if (config.__retryCount >= config.retry) {
      console.log('重试次数已用完');
      let message = error.message || '请求错误';
      if (error.response) {
        switch (error.response.status) {
          case 401:
            message = '未授权，请重新登录';
            store.dispatch('user/logout');
            break;
          case 403:
            message = '拒绝访问';
            break;
          case 404:
            message = '请求错误，未找到该资源';
            break;
          case 500:
            message = '服务器内部错误';
            break;
        }
      }
      Toast({
        message: message,
        forbidClick: true,
        duration: 2000,
      });
      return Promise.reject(error);
    }

    // 重试次数加1
    config.__retryCount += 1;

    // 错误信息
    let message = error.message || '请求错误';
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录';
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求错误，未找到该资源';
          break;
        case 500:
          message = '服务器内部错误';
          break;
      }
    }

    // 如果配置了显示重试提示
    if (config.showRetryTip) {
      Toast({
        message: message,
        duration: 2000,
        onClose: () => {
          Toast.loading({
            message: `第${config.__retryCount}次重试...`,
            forbidClick: true,
          });
        },
      });
    }

    // 创建新的Promise来处理重试
    const backoff = new Promise(function (resolve) {
      setTimeout(function () {
        resolve();
      }, (config.retryDelay || 1000) + (config.showRetryTip ? 2000 : 0));
    });

    // 返回重试请求
    return backoff.then(function () {
      // 创建新的请求配置，避免使用已经被标记为完成的配置
      const newConfig = { ...config };
      // 移除之前的 cancelToken，因为它可能已经被使用过
      delete newConfig.cancelToken;
      // 确保不会重复添加 pendingMap
      removePending(newConfig);
      // 返回新的请求
      return service(newConfig);
    });
  },
);

export default service;
