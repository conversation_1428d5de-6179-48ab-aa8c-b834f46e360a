/**
 * 网络状态监测和处理工具
 */

// 获取当前网络类型
export const getNetworkType = () => {
  if (navigator.connection && navigator.connection.effectiveType) {
    return navigator.connection.effectiveType; // 'slow-2g', '2g', '3g', '4g'
  }
  return navigator.onLine ? '4g' : 'offline';
};

// 检查是否是弱网环境
export const isSlowNetwork = () => {
  const networkType = getNetworkType();
  return networkType === 'slow-2g' || networkType === '2g';
};

// 监听网络状态变化
export const setupNetworkListener = (onlineCallback, offlineCallback) => {
  window.addEventListener('online', () => {
    if (typeof onlineCallback === 'function') {
      onlineCallback();
    }
  });

  window.addEventListener('offline', () => {
    if (typeof offlineCallback === 'function') {
      offlineCallback();
    }
  });

  // 监听网络状态变化（部分浏览器支持）
  if (navigator.connection && navigator.connection.addEventListener) {
    navigator.connection.addEventListener('change', () => {
      const isOnline = navigator.onLine;
      const networkType = getNetworkType();

      if (typeof onlineCallback === 'function' && isOnline) {
        onlineCallback(networkType);
      }

      if (typeof offlineCallback === 'function' && !isOnline) {
        offlineCallback(networkType);
      }
    });
  }
};

// 创建网络状态提示组件
export const showNetworkStatus = (status) => {
  let existingTip = document.getElementById('network-status-tip');

  if (!existingTip) {
    existingTip = document.createElement('div');
    existingTip.id = 'network-status-tip';
    existingTip.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      padding: 6px;
      font-size: 12px;
      text-align: center;
      z-index: 9999;
      transition: all 0.3s;
    `;
    document.body.appendChild(existingTip);
  }

  if (status === 'offline') {
    existingTip.textContent = '网络已断开，请检查网络设置';
    existingTip.style.backgroundColor = '#ff4d4f';
    existingTip.style.color = 'white';
    existingTip.style.display = 'block';
  } else if (status === 'slow') {
    existingTip.textContent = '当前网络较慢，请耐心等待';
    existingTip.style.backgroundColor = '#faad14';
    existingTip.style.color = 'white';
    existingTip.style.display = 'block';
  } else {
    existingTip.style.display = 'none';
  }
};

// 初始化网络监听
export const initNetworkMonitor = () => {
  // 初始检查
  const initialNetworkType = getNetworkType();
  if (initialNetworkType === 'offline') {
    showNetworkStatus('offline');
  } else if (initialNetworkType === 'slow-2g' || initialNetworkType === '2g') {
    showNetworkStatus('slow');
  }

  // 设置监听
  setupNetworkListener(
    // 网络恢复
    () => {
      showNetworkStatus('online');
      setTimeout(() => {
        showNetworkStatus('');
      }, 3000);
    },
    // 网络断开
    () => {
      showNetworkStatus('offline');
    },
  );
};
