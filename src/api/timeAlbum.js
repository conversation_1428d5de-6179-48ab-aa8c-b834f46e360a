import request from '@/utils/request';
// 获取时光相册列表-教师端
export function getTimeAlbumList(params) {
  console.log(params);
  const url =
    params.from === '1' // 教师端
      ? '/api/student-service/miniprogram/time-album/infoList'
      : '/web/student-service/time-album/info-list';
  return request({
    url,
    method: 'get',
    params,
    headers: {
      token: params.token,
      Visitor: params.visitor,
      'Operation-Id': params.operation_id,
    },
    showLoading: false,
  });
}
// 获取时光相册时间点列表
export function getTimeAlbumTimePointList(params) {
  const url =
    params.from === '1' // 教师端
      ? '/api/student-service/miniprogram/time-album/event-time-list'
      : '/web/student-service/time-album/event-time-list';
  return request({
    url,
    method: 'get',
    params,
    headers: {
      token: params.token,
      Visitor: params.visitor,
      'Operation-Id': params.operation_id,
    },
    showLoading: false,
  });
}
// 获取时光相册列表-学生端
export function getTimeAlbumListOfStudent(params) {
  return request({
    url: '/web/student-service/time-album/show-list',
    method: 'get',
    params,
  });
}

// 获取时光相册节点详情
export function getTimeAlbumDetail(params) {
  const url = '/api/student-service/public/time-album/info';
  return request({
    url,
    method: 'get',
    params,
    headers: {
      // token: params.token,
      // Visitor: params.visitor,
      // 'Operation-Id': params.operation_id,
    },
    showLoading: false,
  });
}
