import request from '@/utils/request';

// 课程顾问
export function getAdvisorList(params) {
  return request({
    url: '/api/market-service/board/advisor',
    method: 'get',
    params,
    // headers: {
    //   token: params.token,
    //   Visitor: params.visitor,
    //   'Operation-Id': params.operation_id,
    // },
    // showLoading: false,
    // retry: 3,
  });
}

// 课程顾问-电话沟通
export function getAdvisorVoipList(params) {
  return request({
    url: '/api/market-service/board/advisor-voip',
    method: 'get',
    params,
    // showLoading: false,
    // retry: 3,
  });
}

// 授课老师
export function getInstructorList(params) {
  return request({
    url: '/api/school-service/board/teacher',
    method: 'get',
    params,
  });
}

// 市场专员
export function getMarketSpecialistList(params) {
  return request({
    url: '/api/market-service/board/responsible',
    method: 'get',
    params,
    showLoading: false,
  });
}

// 教务
export function getBoardEducator(params) {
  return request({
    url: '/api/school-service/board/educator',
    method: 'get',
    params,
    // showLoading: false,
    // retry: 3,
  });
}
