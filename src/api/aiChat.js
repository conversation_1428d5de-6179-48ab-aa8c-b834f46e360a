import aiConfig from '@/config/aiModel';
import { selectSystemPrompt } from '@/config/systemPrompts';

export default {
  // 流式聊天方法 - 支持HTML渲染
  async chatWithAIStream(messages, onProgress, onComplete, onError) {
    const lastUserMessage = messages.filter((msg) => msg.from === 'user').pop();
    const userText = lastUserMessage ? lastUserMessage.text : '';
    const selectedPrompt = selectSystemPrompt(userText);

    const formattedMessages = messages.map((msg) => ({
      role: msg.from === 'user' ? 'user' : 'assistant',
      content: msg.text,
    }));

    formattedMessages.unshift({
      role: 'system',
      content: selectedPrompt.prompt,
    });

    const options = {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${aiConfig.model.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: aiConfig.model.name,
        messages: formattedMessages,
        ...aiConfig.requestParams,
        stream: true,
      }),
    };

    let fullContent = '';
    let extractedContent = ''; // 提取的实际内容
    let isJsonStarted = false;
    let jsonBuffer = '';

    // 设置超时
    const timeout = 90000;
    const timeoutId = setTimeout(() => {
      onError(new Error('请求超时，请稍后重试'));
    }, timeout);

    try {
      const response = await fetch(aiConfig.model.apiUrl, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let isReading = true;

      while (isReading) {
        const { done, value } = await reader.read();

        if (done) {
          isReading = false;
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              clearTimeout(timeoutId);

              // 完成时处理最终内容
              const finalResponse = this.processFinalStreamContent(
                fullContent,
                messages,
                selectedPrompt.name,
              );
              onComplete(finalResponse);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                const delta = parsed.choices[0].delta;
                if (delta.content) {
                  const chunk = delta.content;
                  fullContent += chunk;

                  console.log('收到流式数据块:', chunk); // 调试信息

                  // 检测JSON开始
                  if (!isJsonStarted && chunk.includes('{')) {
                    isJsonStarted = true;
                    const jsonStart = chunk.indexOf('{');
                    jsonBuffer = chunk.substring(jsonStart);
                    console.log('JSON开始，初始buffer:', jsonBuffer); // 调试信息
                  } else if (isJsonStarted) {
                    jsonBuffer += chunk;
                  }

                  let newContent = '';

                  if (isJsonStarted) {
                    // 尝试实时提取content字段
                    newContent = this.extractContentFromJsonBuffer(jsonBuffer);
                    console.log('提取的内容:', newContent); // 调试信息
                  } else {
                    // 还没开始JSON，需要检查是否直接包含了JSON格式的内容
                    // 先尝试提取content，如果失败则显示原始内容
                    const extractedFromFull = this.extractContentFromStream(fullContent);
                    newContent = extractedFromFull || fullContent;
                  }

                  // 只要有新内容就立即显示（更激进的策略）
                  if (newContent && newContent !== extractedContent) {
                    extractedContent = newContent;

                    console.log('更新显示内容:', extractedContent.substring(0, 100) + '...'); // 调试信息

                    onProgress({
                      content: extractedContent,
                      promptType: selectedPrompt.name,
                      isComplete: false,
                      isHTML: true,
                    });
                  }
                }
              }
            } catch (e) {
              console.warn('解析流式数据失败:', e, data);
            }
          }
        }
      }

      // 如果循环正常结束但没有收到[DONE]信号，也要处理最终内容
      clearTimeout(timeoutId);
      const finalResponse = this.processFinalStreamContent(
        fullContent,
        messages,
        selectedPrompt.name,
      );
      onComplete(finalResponse);
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('流式请求失败:', error);
      onError(error);
    }
  },

  // 从JSON缓冲区中实时提取content字段
  extractContentFromJsonBuffer(jsonBuffer) {
    try {
      // 方法1: 尝试解析完整JSON
      try {
        const jsonData = JSON.parse(jsonBuffer);
        if (jsonData.content) {
          return jsonData.content;
        }
      } catch (e) {
        // JSON不完整，继续用正则表达式
      }

      // 方法2: 使用正则表达式实时提取content字段
      // 这个正则表达式更宽松，能处理不完整的JSON
      const contentMatch = jsonBuffer.match(/"content"\s*:\s*"((?:[^"\\]|\\.)*)"/s);
      if (contentMatch) {
        return this.decodeJsonString(contentMatch[1]);
      }

      // 方法3: 如果找到content字段开始但还没结束，显示部分内容
      const partialMatch = jsonBuffer.match(/"content"\s*:\s*"([^"\\]*(?:\\.[^"\\]*)*)/s);
      if (partialMatch) {
        return this.decodeJsonString(partialMatch[1]);
      }

      return '';
    } catch (e) {
      console.warn('提取JSON内容失败:', e);
      return '';
    }
  },

  // 从流式内容中提取实际的content（保留原方法作为备用）
  extractContentFromStream(streamContent) {
    try {
      // 方法1: 尝试直接解析完整JSON
      const jsonMatch = streamContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          const jsonData = JSON.parse(jsonMatch[0]);
          if (jsonData.content) {
            return jsonData.content;
          }
        } catch (e) {
          // JSON不完整，继续用其他方法
        }
      }

      // 方法2: 使用正则表达式提取content字段的值
      const contentMatch = streamContent.match(/"content"\s*:\s*"((?:[^"\\]|\\.)*)"/s);
      if (contentMatch) {
        return this.decodeJsonString(contentMatch[1]);
      }

      // 方法3: 检查是否包含suggestions数组，如果有则说明是完整JSON，应该只返回content
      if (streamContent.includes('"suggestions"') && streamContent.includes('"content"')) {
        console.warn('检测到完整JSON格式但无法解析content，返回空字符串避免显示原始JSON');
        return '';
      }

      // 方法4: 如果没有JSON结构，直接返回原始内容
      if (!streamContent.includes('{') && !streamContent.includes('"content"')) {
        return streamContent;
      }

      return '';
    } catch (e) {
      console.warn('提取内容失败:', e);
      return '';
    }
  },

  // 解码JSON字符串中的转义字符
  decodeJsonString(str) {
    return str
      .replace(/\\"/g, '"')
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\r/g, '\r')
      .replace(/\\b/g, '\b')
      .replace(/\\f/g, '\f')
      .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
      .replace(/\\\\/g, '\\'); // 这个要放在最后
  },

  // 处理最终流式内容
  processFinalStreamContent(content, messages, promptType) {
    let parsedResponse;

    try {
      // 尝试解析JSON格式
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedResponse = JSON.parse(jsonMatch[0]);
        console.log('最终解析JSON响应:', parsedResponse);

        // 确保content字段存在且有效
        if (!parsedResponse.content) {
          throw new Error('JSON中缺少content字段');
        }
      } else {
        throw new Error('No JSON found');
      }
    } catch (e) {
      console.warn('AI响应不是JSON格式，使用备用方案:', e.message);

      // 尝试从原始内容中提取content
      const extractedContent = this.extractContentFromStream(content);

      // 对于非JSON格式，使用提取的内容或原始内容
      parsedResponse = {
        content: extractedContent || content,
        suggestions: this.generateFallbackSuggestions(messages, promptType),
      };
    }

    // 验证和补全响应
    if (!parsedResponse.content) {
      parsedResponse.content = content;
    }

    if (
      !parsedResponse.suggestions ||
      !Array.isArray(parsedResponse.suggestions) ||
      parsedResponse.suggestions.length !== 3
    ) {
      parsedResponse.suggestions = this.generateFallbackSuggestions(messages, promptType);
    }

    return {
      content: parsedResponse.content,
      suggestions: parsedResponse.suggestions,
      promptType: promptType,
      isComplete: true,
      isHTML: true,
    };
  },

  // 保留原有的非流式方法
  async chatWithAI(messages) {
    // 获取最后一条用户消息来选择合适的系统提示词
    const lastUserMessage = messages.filter((msg) => msg.from === 'user').pop();
    const userText = lastUserMessage ? lastUserMessage.text : '';

    // 根据用户消息选择系统提示词
    const selectedPrompt = selectSystemPrompt(userText);

    const formattedMessages = messages.map((msg) => ({
      role: msg.from === 'user' ? 'user' : 'assistant',
      content: msg.text,
    }));

    // 添加动态选择的系统提示词
    formattedMessages.unshift({
      role: 'system',
      content: selectedPrompt.prompt,
    });

    const options = {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${aiConfig.model.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: aiConfig.model.name,
        messages: formattedMessages,
        ...aiConfig.requestParams,
        stream: false, // 非流式模式
      }),
    };

    try {
      const response = await fetch(aiConfig.model.apiUrl, options);
      const data = await response.json();

      if (data.choices && data.choices.length > 0) {
        const aiContent = data.choices[0].message.content;

        // 记录原始响应用于调试
        console.log('AI原始响应:', aiContent);

        // 尝试解析JSON格式的响应
        let parsedResponse;
        try {
          parsedResponse = JSON.parse(aiContent);
          console.log('成功解析JSON响应:', parsedResponse);
        } catch (e) {
          // 如果不是JSON格式，则使用原始内容并生成默认建议
          console.warn('AI响应不是JSON格式，使用备用方案:', e.message);
          parsedResponse = {
            content: aiContent,
            suggestions: this.generateFallbackSuggestions(messages, selectedPrompt.name),
          };
        }

        // 验证响应格式
        if (!parsedResponse.content) {
          console.error('AI响应格式错误：缺少content字段');
          throw new Error('AI响应格式错误：缺少content字段');
        }

        // 确保有3条建议
        if (
          !parsedResponse.suggestions ||
          !Array.isArray(parsedResponse.suggestions) ||
          parsedResponse.suggestions.length !== 3
        ) {
          console.warn('AI建议格式不正确，使用备用建议');
          parsedResponse.suggestions = this.generateFallbackSuggestions(
            messages,
            selectedPrompt.name,
          );
        }

        return {
          content: parsedResponse.content,
          suggestions: parsedResponse.suggestions,
          promptType: selectedPrompt.name,
          isHTML: true, // 标记为HTML内容
        };
      } else {
        console.error('AI响应数据异常:', data);
        throw new Error('无效的AI响应');
      }
    } catch (error) {
      console.error('AI请求失败:', error);
      throw error;
    }
  },

  // 生成备用建议（当AI没有返回建议或格式错误时使用）
  generateFallbackSuggestions(messages, promptType) {
    // 获取最近10条消息的内容用于分析
    const recentMessages = messages.slice(-10);
    const userMessages = recentMessages.filter((msg) => msg.from === 'user').map((msg) => msg.text);
    const allText = userMessages.join(' ').toLowerCase();

    // 检查是否为非围棋相关问题
    const nonGoKeywords = [
      '数学',
      '语文',
      '英语',
      '历史',
      '地理',
      '物理',
      '化学',
      '生物',
      '音乐',
      '美术',
      '体育',
      '电影',
      '游戏',
      '动画',
      '小说',
      '天气',
      '新闻',
      '股票',
      '购物',
      '美食',
      '旅游',
      '健康',
      '编程',
      '电脑',
      '手机',
      '科技',
      '汽车',
      '足球',
      '篮球',
    ];

    const isNonGoQuestion = nonGoKeywords.some((keyword) => allText.includes(keyword));

    if (promptType === '棋谱大师') {
      if (isNonGoQuestion) {
        return [
          '请分享一个围棋棋谱让我分析',
          '想了解围棋AI技术发展吗？',
          '有什么围棋技术问题需要探讨？',
        ];
      }
      // 围棋相关的专业建议
      if (allText.includes('sgf') || allText.includes('棋谱')) {
        return ['这个棋谱还有哪些变化？', '请分析关键转折点', '如何改进这个下法？'];
      } else if (allText.includes('布局')) {
        return ['这个布局的后续发展？', '如何应对这种布局？', '现代布局的新变化'];
      } else if (allText.includes('定式')) {
        return ['这个定式的最新理论？', '定式选择的原则', '如何灵活运用定式？'];
      } else if (allText.includes('死活') || allText.includes('手筋')) {
        return ['更多死活题练习', '手筋的实战应用', '如何提高计算力？'];
      }
      return ['分析一个经典棋谱', '讲解现代定式理论', '如何提高棋力？'];
    } else {
      // 棋精灵的建议
      if (isNonGoQuestion) {
        return ['围棋有什么有趣的故事？', '我想学围棋的基本规则', '围棋为什么这么有趣？'];
      }
      // 围棋相关的儿童友好建议
      if (allText.includes('规则')) {
        return [
          '围棋还有什么有趣的规则？',
          '我们来玩个围棋小游戏吧！',
          '围棋和其他棋类有什么不同？',
        ];
      } else if (allText.includes('故事') || allText.includes('历史')) {
        return ['还有什么围棋故事？', '古代小朋友也下围棋吗？', '围棋是怎么传到其他国家的？'];
      } else if (allText.includes('学习') || allText.includes('入门')) {
        return ['我想学更多围棋知识！', '有什么围棋小游戏吗？', '怎样才能下得更好？'];
      }
      return ['围棋有什么有趣的故事？', '我想学围棋的基本规则', '围棋为什么这么有趣？'];
    }
  },

  // 保留原有的生成建议方法作为备用
  generateSuggestions(text, promptType = '棋精灵') {
    // 这个方法现在主要作为备用，实际建议由AI生成
    return this.generateFallbackSuggestions([{ from: 'user', text }], promptType);
  },
};
