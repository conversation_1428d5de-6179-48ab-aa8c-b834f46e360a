import request from '@/utils/request';

// 用户登录
export function login(data) {
  return request({
    url: '/api/login/login',
    method: 'post',
    data,
    retry: 0,
    retryDelay: 1500,
    showRetryTip: true,
    timeout: 15000,
    showLoading: true,
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get',
  });
}

// 用户登出
export function logout() {
  return request({
    url: '/user/logout',
    method: 'post',
  });
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params,
  });
}

// 获取企业微信用户信息
export function getWechatUserInfo(params) {
  return request({
    url: '/api/corp-wechat-service/wechat/userinfo',
    method: 'get',
    params,
  });
}
