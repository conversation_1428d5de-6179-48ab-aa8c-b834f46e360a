export const humanPoweredSysRoutes = [
  {
    path: '/humanPoweredSys',
    component: () => import('@/views/humanPoweredSys'),
    name: 'HumanPoweredSys',
    meta: {
      title: '聂道人力驱动系统',
      keepAlive: true,
    },
  },
  // 校长看板
  {
    path: '/humanPoweredSys/principal',
    component: () => import('@/views/humanPoweredSys/principal'),
    name: 'Principal',
    meta: {
      title: '校长看板',
      keepAlive: true,
    },
  },
  // 授课老师看板
  {
    path: '/humanPoweredSys/instructor',
    component: () => import('@/views/humanPoweredSys/Instructor'),
    name: 'Instructor',
    meta: {
      title: '授课老师看板',
      keepAlive: true,
    },
  },
  {
    path: '/humanPoweredSys/courseAdvisor',
    component: () => import('@/views/humanPoweredSys/courseAdvisor'),
    name: 'CourseAdvisor',
    meta: {
      title: '课程顾问看板',
      keepAlive: true,
    },
  },
  // 市场专员绩效看板
  {
    path: '/humanPoweredSys/marketSpecialistPerformance',
    component: () => import('@/views/humanPoweredSys/marketSpecialistPerformance'),
    name: 'MarketSpecialistPerformance',
    meta: {
      title: '市场专员绩效看板',
      keepAlive: true,
    },
  },
  {
    path: '/humanPoweredSys/educational',
    component: () => import('@/views/humanPoweredSys/educational'),
    name: 'Educational',
    meta: {
      title: '教务绩效看板',
      keepAlive: true,
    },
  },
];
