import store from '@/store';
import { Toast } from 'vant';

// 白名单路由
const whiteList = [
  '/login',
  '/404',
  '/humanPoweredSys',
  '/humanPoweredSys/courseAdvisor',
  '/humanPoweredSys/marketSpecialistPerformance',
  '/humanPoweredSys/instructor',
  '/humanPoweredSys/educational',
  '/tg-minigram/timeAlbum',
  '/tg-minigram/niedaoCircle/shareCard',
  '/tg-minigram/timeAlbum/shareCard',
  '/tg-minigram/imgShow',
  '/tg-minigram/studyReport',
  '/tg-minigram/aiChat',
];

// 导出一个函数，以便在main.js中调用
export function setupPermission(router) {
  router.beforeEach(async (to, from, next) => {
    // 设置页面标题
    document.title = to.meta.title;

    // 获取token
    const hasToken = store.state.user && store.state.user.token;

    if (hasToken) {
      if (to.path === '/login') {
        // 已登录且要跳转的页面是登录页，直接跳转到首页
        next({ path: '/' });
      } else {
        // 判断是否已获取用户信息
        const hasUserInfo = store.state.user && store.state.user.userInfo;
        if (hasUserInfo) {
          next();
        } else {
          try {
            // 获取用户信息
            await store.dispatch('user/getUserInfo');
            // 确保导航一次性完成
            next({ ...to, replace: true });
          } catch (error) {
            // 获取用户信息失败，可能token过期
            await store.dispatch('user/logout');
            Toast.fail('登录状态已过期，请重新登录');
            // 使用replace避免导航记录堆积
            next({ path: '/login', query: { redirect: to.path }, replace: true });
          }
        }
      }
    } else {
      // 未登录
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next();
      } else {
        // 其他没有访问权限的页面将被重定向到登录页面
        // 使用replace避免导航记录堆积
        next({ path: '/login', query: { redirect: to.fullPath }, replace: true });
      }
    }
  });
}
