export const miniAppRoutes = [
  {
    path: '/tg-minigram/timeAlbum',
    component: () => import('@/views/tg-minigram/timeAlbum'),
    name: 'TimeAlbum',
    meta: {
      title: '时光相册',
      keepAlive: true,
    },
  },
  {
    path: '/tg-minigram/timeAlbum/shareCard',
    component: () => import('@/views/tg-minigram/timeAlbum/shareCard'),
    name: 'ShareCardTimeAlbum',
    meta: {
      title: '生成海报',
      keepAlive: true,
    },
  },
  {
    path: '/tg-minigram/studyReport',
    component: () => import('@/views/tg-minigram/studyReport'),
    name: 'StudyReport',
    meta: {
      title: '',
      keepAlive: true,
    },
  },
  {
    path: '/tg-minigram/niedaoCircle/shareCard',
    component: () => import('@/views/tg-minigram/niedaoCircle/shareCard'),
    name: 'ShareCardNiedaoCircle',
    meta: {
      title: '生成海报',
      keepAlive: true,
    },
  },
  {
    path: '/tg-minigram/imgShow',
    component: () => import('@/views/tg-minigram/banner/img.vue'),
    name: 'imgShow',
    meta: {
      title: '',
      keepAlive: true,
    },
  },
  // AI聊天
  {
    path: '/tg-minigram/aiChat',
    component: () => import('@/views/tg-minigram/aiChat'),
    name: 'AiChat',
    meta: {
      title: '围棋小助手',
      keepAlive: true,
    },
  },
];
