import Vue from 'vue';
import VueRouter from 'vue-router';
import { miniAppRoutes } from './miniApp';
import { humanPoweredSysRoutes } from './humanPoweredSys';
Vue.use(VueRouter);

export const constantRoutes = [
  ...miniAppRoutes,
  ...humanPoweredSysRoutes,
  {
    path: '/login',
    component: () => import('@/views/login'),
    name: 'Login',
    meta: {
      title: '登录',
      keepAlive: false,
    },
  },
  {
    path: '/',
    component: () => import('@/views/index'),
    name: 'Index',
    meta: {
      title: '时光相册',
      keepAlive: true,
    },
  },

  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
    meta: {
      title: '404',
      keepAlive: false,
    },
  },

  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
];

const createRouter = () =>
  new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// 重置路由
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
