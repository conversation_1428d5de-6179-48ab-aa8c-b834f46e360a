import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
// import initVConsole from './utils/vconsole';
import { initNetworkMonitor } from './utils/network';
// 导入路由权限控制函数
import { setupPermission } from './router/permission';
// 导入图片预览组件
import preview from 'vue-photo-preview';
import 'vue-photo-preview/dist/skin.css';
// var option = {
//   maxSpreadZoom: 1, // 控制预览图最大的倍数，默认是2倍，我这里改成了原图
//  fullscreenEl: false, //控制是否显示右上角全屏按钮
//   closeEl: false, //控制是否显示右上角关闭按钮
//   tapToClose: true, //点击滑动区域应关闭图库
//   shareEl: false, //控制是否显示分享按钮
//   zoomEl: false, //控制是否显示放大缩小按钮
//   counterEl: false, //控制是否显示左上角图片数量按钮
//   arrowEl: true,  //控制如图的左右箭头（pc浏览器模拟手机时）
//   tapToToggleControls: true, //点击应切换控件的可见性
//   clickToCloseNonZoomable: true, //点击图片应关闭图库，仅当图像小于视口的大小时
// };
Vue.use(preview);
// 初始化调试工具
// initVConsole();

// 初始化网络监测
initNetworkMonitor();

// 设置路由权限控制
setupPermission(router);

// 按需引入Vant组件
import {
  Button,
  NavBar,
  Cell,
  CellGroup,
  Toast,
  Dialog,
  Empty,
  Loading,
  PullRefresh,
  List,
  Tabbar,
  TabbarItem,
  Form,
  Field,
  Icon,
  Popup,
  Picker,
  DatetimePicker,
  ImagePreview,
  Image as VanImage,
} from 'vant';

// 注册Vant组件
Vue.use(Button);
Vue.use(NavBar);
Vue.use(Cell);
Vue.use(CellGroup);
Vue.use(Toast);
Vue.use(Dialog);
Vue.use(Empty);
Vue.use(Loading);
Vue.use(PullRefresh);
Vue.use(List);
Vue.use(Tabbar);
Vue.use(TabbarItem);
Vue.use(Form);
Vue.use(Field);
Vue.use(Icon);
Vue.use(Popup);
Vue.use(Picker);
Vue.use(DatetimePicker);
Vue.use(ImagePreview);
Vue.use(VanImage);
Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
