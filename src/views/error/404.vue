<template>
  <div class="not-found">
    <div class="not-found-content">
      <img :src="require('@/assets/404.png')" alt="404" class="not-found-img" />
      <h2 class="not-found-title">页面不存在</h2>
      <p class="not-found-desc">抱歉，您访问的页面不存在</p>
      <van-button type="primary" size="small" @click="goBack">返回上一页</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFound',
  methods: {
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="less" scoped>
.not-found {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.not-found-content {
  text-align: center;
  padding: 20px;
}

.not-found-img {
  width: 200px;
  margin-bottom: 20px;
}

.not-found-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.not-found-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}
</style>
