<template>
  <div>
    <div
      ref="studyReportDetailPage"
      class="study-report-detail-page"
      :class="pageTheme[pageTitle].class"
    >
      <div class="content">
        <div class="content__wrap">
          <div class="content__wrap-teacher-auto">
            <div class="avatar">
              <img :src="genders_list[studyReportInfo.choose_head] || genders_list[1]" />
            </div>
            <div class="student-name">{{ studyReportInfo.student_name }}</div>
            <div class="student-info">
              <img :src="pageTheme[pageTitle].joinImg" class="fixed-img left" alt="" />
              <img :src="pageTheme[pageTitle].joinImg" class="fixed-img right" alt="" />
              <div>
                <div class="classroom-name">
                  {{ studyReportInfo.classroom_name }}
                </div>
                <div class="report-date">
                  <div v-if="!isClassNotice">上课时间：{{ studyReportInfo.start_time }}</div>
                  <template v-if="studyReportInfo.created_at">
                    <span class="teacher-name">{{ studyReportInfo.teacher_name }}</span>
                    <span class="report-date-time">
                      发布于{{ formatTime(studyReportInfo.created_at) }}
                    </span>
                  </template>
                </div>
              </div>
              <div class="title">{{ pageTitle }}</div>
            </div>
            <div class="teacher-remark">
              <div class="content-txt-wrap">
                <template>
                  <div class="content-txt" v-html="format_content"></div>
                </template>
              </div>
            </div>
          </div>
          <div class="qrcode">
            <div class="qrcode-text">聂道少儿围棋，只为智慧成长</div>
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c5927512-25dd-47f5-83ef-4b8b6183e8a6.png"
              class="qrcode-img"
              alt=""
            />
            
          </div>
        </div>
        <div class="tips">长按图片保存到相册</div>
      </div>
    </div>
    <div class="generate-share-img-wrap">
      <div class="generate-share-img" :style="{ width: shareImgW, height: shareImgH }">
        <img :src="shareUrl" style="width: 100%" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import { pageTheme, genders_list } from './config';
import { feedbackDetail } from '@/api/niedaoCicle';
import { formatTime } from '@/utils/util';
export default {
  data() {
    return {
      pageTheme,
      genders_list,
      studyReportInfo: {
        student_gender: 'female',
        student_name: '小明',
        content: '',
        created_at: '2021-01-01 12:00:00',
        start_time: '2021-01-01 12:00:00',
        classroom_name: '小明',
      },
      pageTitle: '家长课堂',
      shareUrl: '',
      shareImgW: '100%',
      shareImgH: '100%',
    };
  },
  computed: {
    query() {
      return this.$route.query;
    },
    format_content() {
      return this.formatContent(this.studyReportInfo.content);
    },
    isCourseSummary() {
      console.log('this.pageTitle :>> ', this.pageTitle);
      return this.pageTheme[this.pageTitle].class === 'course-summary';
    },
    isClassNotice() {
      return this.pageTheme[this.pageTitle].class === 'class-notice';
    },
  },
  beforeDestroy() {},
  mounted() {
    this.pageTitle = this.query.pageTitle;
    this.getFeedbackDetail();
  },
  methods: {
    formatTime,
    async getFeedbackDetail() {
      const res = await feedbackDetail({
        feedback_id: this.query.feedback_id,
        student_id: this.query.student_id,
      });
      console.log('res :>> ', res);
      const { code, data } = res;
      if (code === 0) {
        this.studyReportInfo = data;
        this.shareScreenshot();
      }
    },
    // 格式化studyReportInfo.content中的img标签，在img标签上添加preview="preview" preview-text=""
    formatContent(content) {
      console.log('content :>> ', content);
      if (!content) return content;
      
      // First handle img tags
      content = content.replace(/<img/g, '<img preview="0" preview-text=""');
      
      // Handle video tags
      content = content.replace(/<video[^>]*>.*?<\/video>/g, (match) => {
        // Extract src and poster from video tag
        const srcMatch = match.match(/src="([^"]+)"/);
        const posterMatch = match.match(/poster="([^"]+)"/);
        
        if (!srcMatch) return match; // If no src found, return original tag
        
        const videoSrc = srcMatch[1];
        let imgSrc = '';
        
        if (posterMatch) {
          // Use poster if available
          imgSrc = posterMatch[1];
        } else {
          // Generate first frame URL from OSS
          // Assuming the video URL is from OSS, append appropriate parameter for first frame
          imgSrc = videoSrc + '?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast';
        }
        
        // Add video-thumbnail class for play button styling
        if(videoSrc){
          return `<div class="video-thumbnaill" data-video-src="${videoSrc}">
            <img src="${imgSrc}" preview="0" preview-text="" />
            <div class="play-button">
              <img src="https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png" alt="" />
            </div>
          </div>`;
        }else{
          return `<img src="${imgSrc}" preview="0" preview-text="/>`;
        }
        // return `<img src="${imgSrc}" preview="0" preview-text="" class="video-thumbnail" data-video-src="${videoSrc}" />`;
      });
      
      return content;
    },
    async captureElement() {
      try {
        // 等待图片加载完成
        await this.$nextTick();

        const element = this.$refs.studyReportDetailPage;
        const pixelRatio = window.devicePixelRatio || 1;
        const canvas = await html2canvas(element, {
          scale: pixelRatio, // 高清截图
          useCORS: true,
          logging: false, // 关闭日志
          scrollX: 0,
          scrollY: 0,
        });

        return canvas.toDataURL('image/png', 0.9); // 使用JPEG格式，90%质量
      } catch (error) {
        console.error('截图失败:', error);
        return null;
      }
    },

    async shareScreenshot() {
      const imgUrl = await this.captureElement();
      if (!imgUrl) return;
      this.shareUrl = imgUrl;
      const { width, height } = this.$refs.studyReportDetailPage.getBoundingClientRect();
      this.shareImgW = width + 'px';
      this.shareImgH = height + 'px';
      console.log('width, height :>> ', width, height);
      // 分享图片到微信
      if (window.wx && wx.miniProgram) {
        wx.miniProgram.postMessage({
          data: {
            type: 'shareImage',
            imgUrl: imgUrl,
          },
        });
      }
    },
  },
};
</script>

<style lang="less">
.content-txt {
  img {
    max-width: 100%;
    border-radius: 24px;
  }
  video {
    max-width: 100%;
  }
  .video-thumbnail {
    position: relative;
    cursor: pointer;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      background: rgba(0, 0, 0, 0.5) url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOCA2LjgyVjE3LjE4QzggMTcuOTcgOC44NyAxOC40NSA5LjU0IDE4LjAyTDE3Ljc3IDEyLjg0QzE4LjQgMTIuNDEgMTguNCAxMS41OSAxNy43NyAxMS4xNkw5LjU0IDUuOThDOC44NyA1LjU1IDggNi4wMyA4IDYuODJaIiBmaWxsPSJ3aGl0ZSIvPjwvc3ZnPg==') center/24px no-repeat;
      border-radius: 50%;
    }
  }
}
body {
  overflow-y: auto;
}

.parent-class {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/2e373ae6-cb62-49cf-9261-7c8c6f28b533.png)
    no-repeat;
  .student-info {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/a9e5013f-31e4-456c-8fed-a1519a9aad75.png)
      no-repeat;
  }
  .teacher-remark {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/a9e5013f-31e4-456c-8fed-a1519a9aad75.png)
      no-repeat;
  }
  .title {
    background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
    &::after {
      border-color: transparent transparent #cf6500 transparent;
    }
    &::before {
      border-color: transparent transparent #cf6500 transparent;
    }
  }
}
.class-notice {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/f8731b97-ffa8-4b53-b3d9-1fbb2d5d1074.png)
    no-repeat;
  .student-info {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/27495007-f303-46ed-8c0a-2cc86a398c0f.png)
      no-repeat;
  }
  .teacher-remark {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/27495007-f303-46ed-8c0a-2cc86a398c0f.png)
      no-repeat;
  }
  .title {
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    &::after {
      border-color: transparent transparent #d99f00 transparent;
    }
    &::before {
      border-color: transparent transparent #d99f00 transparent;
    }
  }
}
.course-summary {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/e1db9c10-dc39-4f35-a496-2e563dfec7d3.png)
    no-repeat;
  .student-info {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/1ffe4718-cd51-481a-8b8a-7b0329b344ac.png)
      no-repeat;
  }
  .teacher-remark {
    background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/1ffe4718-cd51-481a-8b8a-7b0329b344ac.png)
      no-repeat;
  }
  .title {
    background: linear-gradient(6deg, #c26ff2 30.97%, #d68fff 92.66%);
    &::after {
      border-color: transparent transparent #ad3cee transparent;
    }
    &::before {
      border-color: transparent transparent #ad3cee transparent;
    }
  }
}
</style>
<style lang="less" scoped>
.study-report-detail-page {
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 100vw;
  min-height: 100vh;
  padding: 68px 38px 100px 38px;
  box-sizing: border-box;
  background-size: 100% 100%;
  ::v-deep .u-navbar {
    // .u-navbar--fixed {
    //   top: 60rpx;
    // }
    .u-navbar__content__title {
      text-align: center;
      font-size: 32px;
      color: #fff;
      font-weight: 600;
    }
  }
  ::v-deep .el-textarea__inner {
    border-radius: 14px;
    border: 1px solid #e9e9e9;
    background: #f5f6f7;
  }
  ::v-deep .el-drawer__title {
    border-radius: 28px 28px 0 0;
    .el-drawer__header {
      border-bottom: 0;
    }
  }
  .shareCard-bg {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(100px); /* 关键属性 - 模糊效果 */
    -webkit-backdrop-filter: blur(20px); /* Safari 支持 */
    background: rgba(255, 255, 255, 0.5);
    box-shadow: inset 0px 0 75px 25px rgba(255, 255, 255, 1);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  .content {
    width: 100%;
    .content__wrap {
      position: relative;
      width: 100%;
      // min-height: 80vh;
      padding: 36px 24px 36px 24px;
      border-radius: 28px;
      background: #fff;
      box-sizing: border-box;
      .avatar {
        width: 144px;
        height: 144px;
        position: absolute;
        top: -28px;
        left: 22px;
        border-radius: 50%;
        border: 1px solid #fff;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .content__wrap-teacher-auto {
        height: 100%;
        border-radius: 28px;
        display: flex;
        flex-direction: column;
        .student-name {
          color: #333;
          font-size: 24px;
          font-weight: 500;
          margin-left: 155px;
          margin-bottom: 68px;
        }
        .student-info {
          padding: 20px 30px;
          border-radius: 14px;
          margin-bottom: 24px;
          position: relative;
          .fixed-img {
            position: absolute;
            bottom: -34px;
            width: 14px;
            height: 50px;
            z-index: 2;
            &.left {
              left: 28px;
            }
            &.right {
              right: 28px;
            }
          }
          .classroom-name {
            color: #333;
            font-size: 26px;
            font-weight: 500;
            margin-bottom: 10px;
          }
          .report-date {
            color: #666;
            font-size: 26px;
            font-weight: 400;
            .teacher-name {
              margin-right: 10px;
            }
          }
          .title {
            color: #fff;
            border-radius: 0px 0px 43.2px 43.2px;
            font-size: 24px;
            font-weight: 600;
            position: absolute;
            bottom: -68px;
            left: 50%;
            z-index: 2;
            transform: translateX(-50%);
            border-radius: 0px 0px 21.6px 21.6px;
            width: 216px;
            height: 52px;
            text-align: center;
            line-height: 52px;
            &::after {
              content: '';
              display: block;
              width: 0;
              height: 0;
              border-width: 8px;
              position: absolute;
              top: 1px;
              left: -8px;
              border-style: solid;
              transform: rotate(135deg);
            }
            &::before {
              content: '';
              display: block;
              width: 0;
              height: 0;
              border-width: 6px;
              position: absolute;
              top: 1px;
              right: -6px;
              border-style: solid;
              transform: rotate(-135deg);
            }
          }
        }
        .teacher-remark {
          height: 100%;
          flex-grow: 1;
          border-radius: 14px;
          position: relative;
          background-size: 100% 100%;
          padding: 60px 32px 32px 30px;
          overflow: auto;
          .page-corner {
            width: 70px;
            height: 66px;
            position: absolute;
            right: -4px;
            bottom: 20px;
          }
          .content-txt-wrap {
            height: 100%;
            overflow: auto;
            .content-txt {
              width: 100%;
              height: 100%;
              overflow: auto;
            }
          }
        }
      }
    }
    .tips{
      color: #FFF;
      font-size: 28px;
      font-weight: 400;
      line-height: normal;
      margin-top: 24px;
      text-align: center;
    }
    .qrcode {
      text-align: center;
      margin-top: 33px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 94px;
        height: 94px;
        // margin-bottom: 23px;
      }
      .qrcode-text {
        color:  #333;
        font-size: 26px;
        font-weight: 400;
        line-height: normal;
        margin-right: 20px;
      }
    }
  }
}
.generate-share-img-wrap {
  position: relative;
  .tips {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28px;
    font-weight: 400;
    color: #fff;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.23);
  }
}
::v-deep .video-thumbnaill{
  position: relative;
  .play-button{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
  }
  img{
    width: 100%;
    height: 100%;
  }
}
</style>
