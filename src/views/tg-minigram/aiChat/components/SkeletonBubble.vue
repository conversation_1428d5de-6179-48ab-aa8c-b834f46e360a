<template>
  <div class="skeleton-bubble">
    <div class="skeleton-content">
      <div class="skeleton-header">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
      <div class="skeleton-body">
        <div
          class="skeleton-line"
          v-for="(line, index) in lines"
          :key="index"
          :style="{ width: line.width, height: line.height }"
        ></div>
      </div>
      <div class="skeleton-footer">
        <div class="skeleton-dot"></div>
        <div class="skeleton-dot"></div>
        <div class="skeleton-dot"></div>
      </div>
    </div>
    <div class="skeleton-shimmer"></div>
  </div>
</template>

<script>
export default {
  name: 'SkeletonBubble',
  data() {
    return {
      lines: this.generateRandomLines(),
    };
  },
  methods: {
    generateRandomLines() {
      const linesCount = Math.floor(Math.random() * 3) + 3; // 3-5行
      const result = [];

      for (let i = 0; i < linesCount; i++) {
        const width = `${Math.floor(Math.random() * 30) + 70}%`; // 70%-100%
        result.push({
          width,
          height: `${Math.floor(Math.random() * 4) + 14}px`, // 14px-18px
        });
      }

      return result;
    },
  },
};
</script>

<style lang="less" scoped>
.skeleton-bubble {
  position: relative;
  width: 70vw;
  min-height: 120px;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16px 16px 16px 4px;
  padding: 16px 20px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03), 0 1px 2px rgba(0, 0, 0, 0.01),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    left: -14px;
    top: 16px;
    width: 0;
    height: 0;
    border-top: 14px solid transparent;
    border-bottom: 14px solid transparent;
    border-right: 14px solid rgba(255, 255, 255, 0.85);
  }

  .skeleton-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    z-index: 1;
    position: relative;
  }

  .skeleton-header {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .skeleton-title {
      height: 20px;
      width: 70%;
      background: #f0f0f0;
      border-radius: 10px;
    }

    .skeleton-subtitle {
      height: 16px;
      width: 50%;
      background: #f0f0f0;
      border-radius: 8px;
    }
  }

  .skeleton-body {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .skeleton-line {
      background: #f0f0f0;
      border-radius: 8px;
    }
  }

  .skeleton-footer {
    display: flex;
    gap: 6px;
    margin-top: 6px;

    .skeleton-dot {
      width: 10px;
      height: 10px;
      background: #f0f0f0;
      border-radius: 50%;
      animation: bounce 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .skeleton-shimmer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    animation: shimmer 2s infinite;
    z-index: 2;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  40% {
    transform: translateY(-5px);
    opacity: 1;
  }
}
</style>
