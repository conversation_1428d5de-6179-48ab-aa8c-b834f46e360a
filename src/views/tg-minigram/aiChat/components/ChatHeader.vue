<template>
  <div class="chat-header">
    <div class="ai-avatar">
      <!-- AI头像 -->
      <div class="avatar-placeholder">
        <div class="avatar-inner">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6017390e-0ffc-4c7f-9e82-103f10d051e4.png"
            alt="AI Avatar"
          />
        </div>
      </div>
    </div>
    <div class="ai-title">
      <span class="ai-name">棋精灵</span>
      <span class="ai-desc">围棋AI助手</span>
    </div>
    <i @click="handleSettings" class="fa fa-cog settings-icon"></i>
  </div>
</template>

<script>
export default {
  name: 'ChatHeader',
  methods: {
    handleSettings() {
      this.$toast('功能准备中...');
    },
  },
};
</script>

<style lang="less" scoped>
.chat-header {
  display: flex;
  align-items: center;
  padding: 16px 12px 8px 12px;
  background: rgba(255, 255, 255, 0.75);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.02);
  position: relative;
  z-index: 2;
  margin: 0 16px;
  border-radius: 40px;
  margin-top: 16px;

  .ai-avatar {
    margin-right: 12px;
    .avatar-placeholder {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: inline-block;
      padding: 3px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;

      .avatar-inner {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }
      }
    }
  }

  .ai-title {
    flex: 1;
    .ai-name {
      font-size: 20px;
      font-weight: bold;
      color: #2d5b99;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      display: block;
    }
    .ai-desc {
      font-size: 18px;
      color: #718096;
    }
  }

  .settings-icon {
    font-size: 26px;
    color: #718096;
    cursor: pointer;
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
