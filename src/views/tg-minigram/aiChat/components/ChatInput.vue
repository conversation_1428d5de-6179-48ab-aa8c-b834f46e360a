<template>
  <div class="chat-input">
    <!-- <i class="fa fa-microphone input-icon"></i> -->
    <div class="input-container">
      <input
        v-model="text"
        type="text"
        class="input-box"
        :placeholder="placeholder"
        @keyup.enter="send"
      />
    </div>
    <button class="send-btn" @click="send">
      <i class="fa fa-paper-plane"></i>
    </button>
  </div>
</template>

<script>
export default {
  name: 'ChatInput',
  data() {
    return {
      text: '',
    };
  },
  props: {
    placeholder: {
      type: String,
      default: '小棋手，问我个问题吧！',
    },
  },
  methods: {
    send() {
      if (!this.text.trim()) return;
      this.$emit('send', this.text.trim());
      this.text = '';
    },
  },
};
</script>

<style lang="less" scoped>
.chat-input {
  display: flex;
  align-items: center;
  padding: 12px 14px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;

  .input-icon {
    font-size: 22px;
    color: #718096;
    margin-right: 10px;
    cursor: pointer;
  }

  .input-container {
    flex: 1;
    margin-right: 10px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .input-box {
      width: 100%;
      border: none;
      outline: none;
      border-radius: 20px;
      padding: 10px 16px;
      font-size: 24px;
      background: transparent;
      color: #4a5568;
      box-sizing: border-box;
      border: 1px solid #f1f1f191;

      &::placeholder {
        color: #a0aec0;
      }
    }
  }

  .send-btn {
    background: #4299e1;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      background: #3182ce;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
