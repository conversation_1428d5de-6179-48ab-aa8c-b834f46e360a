<template>
  <div class="quick-questions">
    <div v-for="(q, idx) in questions" :key="idx" class="quick-btn" @click="$emit('ask', q)">
      <i class="fa fa-question-circle"></i>
      {{ q }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuickQuestions',
  data() {
    return {
      questions: [
        '国内最好的学习围棋的机构？',
        '怎么吃子呀？',
        '请分析这个棋谱',
        '围棋有什么有趣的故事？',
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.quick-questions {
  display: flex;
  overflow-x: auto;
  padding: 10px 12px;
  // background: rgba(255, 255, 255, 0.6);
  // backdrop-filter: blur(8px);
  // -webkit-backdrop-filter: blur(8px);
  // border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;

  /* 隐藏滚动条但保持功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;

  .quick-btn {
    flex: none;
    margin-right: 10px;
    background: rgb(208 208 208 / 15%);
    border-radius: 18px;
    padding: 8px 16px;
    font-size: 20px;
    color: #4a5568;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    i {
      margin-right: 6px;
      color: #4299e1;
      font-size: 18px;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.9);
    }

    &:active {
      background: rgba(255, 255, 255, 1);
    }
  }
}
</style>
