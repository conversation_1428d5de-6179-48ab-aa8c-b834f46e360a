<template>
  <div class="chat-messages" ref="msgList">
    <div
      v-for="(msg, idx) in messages"
      :key="msg.animationId || msg.id || idx"
      :class="[
        'msg-item',
        msg.from,
        {
          thinking: msg.type === 'thinking',
          streaming: msg.type === 'streaming',
          complete: msg.type === 'complete',
        },
      ]"
    >
      <!-- AI头像 -->
      <div v-if="msg.from === 'ai'" class="avatar">
        <div class="avatar-placeholder">
          <div class="avatar-inner">
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6017390e-0ffc-4c7f-9e82-103f10d051e4.png"
              alt="AI Avatar"
            />
          </div>
        </div>
      </div>
      <!-- 用户头像 -->
      <div v-if="msg.from === 'user'" class="avatar user-avatar">
        <div class="avatar-placeholder">
          <div class="avatar-inner">
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/0a4b48aa-5bfd-44ee-af56-b05af8ccbcd0.png"
              alt="User Avatar"
            />
          </div>
        </div>
      </div>
      <!-- 普通消息气泡 -->
      <div
        v-if="!msg.type || !['thinking'].includes(msg.type)"
        :class="[
          'msg-bubble',
          {
            'reveal-animation': msg.from === 'ai' && msg.type === 'complete',
            'streaming-animation': msg.from === 'ai' && msg.type === 'streaming',
          },
        ]"
      >
        <div class="content-wrapper">
          <div v-if="msg.from === 'ai' && msg.promptType" class="prompt-type-badge">
            {{ msg.promptType }}
          </div>

          <!-- HTML内容渲染 -->
          <div v-if="msg.isHTML" class="msg-html-content" v-html="msg.text"></div>

          <!-- 普通文本内容 -->
          <span v-else class="msg-text" v-html="msg.text"></span>

          <span class="msg-time">{{ msg.time }}</span>
        </div>
        <div class="shimmer-overlay"></div>

        <!-- AI回复后的建议内容，只在最后一条AI消息显示 -->
        <div
          v-if="
            msg.from === 'ai' &&
            msg.suggestions &&
            msg.suggestions.length > 0 &&
            msg.type === 'complete' &&
            isLastAiMessage(idx)
          "
          class="suggestions-container"
        >
          <div
            v-for="(suggestion, i) in msg.suggestions"
            :key="i"
            class="suggestion-item"
            :class="`suggestion-item-${i}`"
            :style="{ animationDelay: `${i * 150}ms` }"
            @click="$emit('use-suggestion', suggestion)"
          >
            <i class="fa fa-lightbulb-o suggestion-icon"></i>
            <span>{{ suggestion }}</span>
          </div>
        </div>
      </div>

      <!-- 骨架屏组件 - 添加进度提示 -->
      <skeleton-bubble
        v-else-if="msg.type === 'thinking'"
        :show-progress="true"
        :elapsed-time="msg.elapsedTime"
      />
    </div>
  </div>
</template>

<script>
import SkeletonBubble from './SkeletonBubble.vue';

export default {
  name: 'ChatMessages',
  components: {
    SkeletonBubble,
  },
  props: {
    messages: Array,
    aiAvatar: String,
  },
  data() {
    return {
      lastMessageCount: 0,
      scrollTimer: null,
      isUserScrolling: false,
      lastScrollTop: 0,
    };
  },
  methods: {
    // 判断是否为最后一条AI消息
    isLastAiMessage(currentIndex) {
      for (let i = currentIndex + 1; i < this.messages.length; i++) {
        if (this.messages[i].from === 'ai' && this.messages[i].type !== 'thinking') {
          return false;
        }
      }
      return true;
    },

    // 检测用户是否在手动滚动
    handleScroll() {
      const el = this.$refs.msgList;
      if (!el) return;

      const currentScrollTop = el.scrollTop;
      const scrollHeight = el.scrollHeight;
      const clientHeight = el.clientHeight;

      // 如果用户向上滚动，标记为手动滚动
      if (currentScrollTop < this.lastScrollTop) {
        this.isUserScrolling = true;
        // 3秒后重置，允许自动滚动
        clearTimeout(this.scrollTimer);
        this.scrollTimer = setTimeout(() => {
          this.isUserScrolling = false;
        }, 3000);
      }

      // 如果用户滚动到底部附近，重置手动滚动状态
      if (scrollHeight - currentScrollTop - clientHeight < 50) {
        this.isUserScrolling = false;
        clearTimeout(this.scrollTimer);
      }

      this.lastScrollTop = currentScrollTop;
    },

    // 平滑滚动到底部
    smoothScrollToBottom(force = false) {
      const el = this.$refs.msgList;
      if (!el) return;

      // 如果用户正在手动滚动且不是强制滚动，则不自动滚动
      if (this.isUserScrolling && !force) return;

      // 使用平滑滚动到底部
      el.scrollTo({
        top: el.scrollHeight,
        behavior: 'smooth',
      });
    },

    // 滚动到最新AI消息的顶部
    scrollToNewAiMessage() {
      const el = this.$refs.msgList;
      if (!el) return;

      // 如果用户正在手动滚动，则不自动滚动
      if (this.isUserScrolling) return;

      // 找到最后一条AI消息的元素
      const aiMessages = el.querySelectorAll('.msg-item.ai:not(.thinking)');
      if (aiMessages.length > 0) {
        const lastAiMessage = aiMessages[aiMessages.length - 1];

        // 滚动到该消息的顶部位置，并留出一些空间
        const topPosition = lastAiMessage.offsetTop - 20;
        el.scrollTo({
          top: topPosition,
          behavior: 'smooth',
        });
      }
    },

    // 流式内容更新时的滚动处理 - 优化版本
    handleStreamingScroll() {
      const el = this.$refs.msgList;
      if (!el || this.isUserScrolling) return;

      // 使用 requestAnimationFrame 优化性能
      requestAnimationFrame(() => {
        const scrollHeight = el.scrollHeight;
        const scrollTop = el.scrollTop;
        const clientHeight = el.clientHeight;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        // 如果距离底部小于50px，则跟随滚动
        if (distanceFromBottom < 50) {
          el.scrollTo({
            top: scrollHeight,
            behavior: 'smooth',
          });
        }
      });
    },

    // 在建议内容渲染完成后滚动到底部
    scrollToBottomAfterSuggestions() {
      // 如果用户正在手动滚动，则不自动滚动
      if (this.isUserScrolling) return;

      // 使用多重延迟确保建议内容完全渲染和动画完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.$nextTick(() => {
            const el = this.$refs.msgList;
            if (el) {
              // 强制滚动到最底部，确保建议内容可见
              el.scrollTo({
                top: el.scrollHeight,
                behavior: 'smooth',
              });
            }
          });
        }, 200); // 增加延迟时间，等待动画完成
      });
    },
  },

  watch: {
    messages: {
      handler(newMessages) {
        this.$nextTick(() => {
          // 如果是新增消息
          if (newMessages.length > this.lastMessageCount) {
            const lastMsg = newMessages[newMessages.length - 1];

            if (lastMsg.from === 'user' || lastMsg.type === 'thinking') {
              // 用户消息或思考状态，强制滚动到底部
              this.smoothScrollToBottom(true);
            } else if (lastMsg.from === 'ai' && lastMsg.type === 'streaming') {
              // 流式输出开始，滚动到新消息
              this.scrollToNewAiMessage();
            } else if (lastMsg.from === 'ai' && lastMsg.type === 'complete') {
              // AI回复完成，需要等待建议内容渲染后再滚动
              this.scrollToBottomAfterSuggestions();
            }

            this.lastMessageCount = newMessages.length;
          } else if (newMessages.length === this.lastMessageCount) {
            // 消息数量没变，可能是内容更新（流式输出）
            const lastMsg = newMessages[newMessages.length - 1];
            if (lastMsg && lastMsg.type === 'streaming') {
              // 流式内容更新，智能滚动
              this.handleStreamingScroll();
            } else if (lastMsg && lastMsg.type === 'complete' && lastMsg.suggestions) {
              // 消息完成且有建议内容，等待DOM更新后滚动
              this.scrollToBottomAfterSuggestions();
            }
          }
        });
      },
      deep: true,
    },
  },

  mounted() {
    this.lastMessageCount = this.messages.length;

    // 添加滚动事件监听
    const el = this.$refs.msgList;
    if (el) {
      el.addEventListener('scroll', this.handleScroll, { passive: true });
    }

    // 如果有初始消息且最后一条是完成状态的AI消息，滚动到底部显示建议
    if (this.messages.length > 0) {
      const lastMsg = this.messages[this.messages.length - 1];
      if (lastMsg.from === 'ai' && lastMsg.type === 'complete' && lastMsg.suggestions) {
        this.scrollToBottomAfterSuggestions();
      }
    }
  },

  beforeDestroy() {
    // 清理定时器和事件监听
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }

    const el = this.$refs.msgList;
    if (el) {
      el.removeEventListener('scroll', this.handleScroll);
    }
  },
};
</script>

<style lang="less" scoped>
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 12px 8px 12px;
  background: #fafafa;
  scroll-behavior: smooth; /* 添加平滑滚动 */
  position: relative;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  .msg-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 14px;
    position: relative;
    animation: fadeIn 0.3s ease-in-out; /* 添加淡入动画 */

    &.ai {
      flex-direction: row;
      .avatar {
        margin-right: 28px;
        margin-top: 6px;
        .avatar-placeholder {
          width: 48px;
          height: 48px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          padding: 2px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

          .avatar-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 50%;
            }
          }
        }
      }
      .msg-bubble {
        background: rgba(255, 255, 255, 0.85);
        color: #333;
        border-radius: 16px 16px 16px 4px;
        align-items: flex-start;
        position: relative;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

        &::after {
          content: '';
          position: absolute;
          left: -14px;
          top: 16px;
          width: 0;
          height: 0;
          border-top: 14px solid transparent;
          border-bottom: 14px solid transparent;
          border-right: 14px solid rgba(255, 255, 255, 0.85);
        }
      }
    }
    &.user {
      flex-direction: row-reverse;
      .avatar.user-avatar {
        margin-left: 28px;
        margin-top: 6px;
        .avatar-placeholder {
          width: 48px;
          height: 48px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          padding: 2px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

          .avatar-inner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 50%;
            }
          }
        }
      }
      .msg-bubble {
        background: rgba(255, 255, 255, 0.85);
        color: #f6ad55;
        border-radius: 16px 16px 4px 16px;
        align-items: flex-end;
        position: relative;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);

        &::after {
          content: '';
          position: absolute;
          right: -13px;
          top: 16px;
          width: 0;
          height: 0;
          border-top: 14px solid transparent;
          border-bottom: 14px solid transparent;
          border-left: 14px solid rgba(255, 255, 255, 0.85);
        }
      }
    }
    .msg-bubble {
      max-width: 70vw;
      padding: 10px 16px;
      font-size: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease; /* 添加过渡效果 */
      position: relative;
      overflow: hidden;

      &.reveal-animation {
        .content-wrapper {
          opacity: 0.85;
          animation: reveal-content 1s forwards 0.1s;
        }

        .shimmer-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.8) 50%,
            rgba(255, 255, 255, 0) 100%
          );
          transform: skewX(-15deg);
          animation: sweep-shimmer 1.5s ease-out forwards;
          pointer-events: none;
          z-index: 2;
          box-shadow: 0 0 30px 30px rgba(255, 255, 255, 0.05);
        }
      }

      .content-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;

        .prompt-type-badge {
          display: inline-block;
          background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
          color: white;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 10px;
          margin-bottom: 6px;
          align-self: flex-start;
          font-weight: 500;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .msg-text {
          margin-bottom: 10px;
          word-break: break-all;
        }
        .msg-time {
          font-size: 18px;
          color: #a0aec0;
          align-self: flex-end;
        }
      }

      /* 建议内容样式 */
      .suggestions-container {
        margin-top: 12px;
        display: flex;
        flex-direction: column;
        width: 100%;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding-top: 8px;
        animation: fadeInUp 0.4s ease-out; /* 建议内容上浮淡入 */

        .suggestion-item {
          margin-top: 6px;
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.7);
          border-radius: 12px;
          font-size: 20px;
          font-weight: 500;
          color: #4a5568;
          cursor: pointer;
          text-align: left;
          display: flex;
          align-items: center;
          backdrop-filter: blur(4px);
          -webkit-backdrop-filter: blur(4px);

          /* 入场动画 */
          opacity: 0;
          transform: translateY(20px) scale(0.95);
          animation: suggestionSlideIn 0.6s ease-out forwards;
          transition: all 0.2s ease; /* 平滑过渡 */

          .suggestion-icon {
            color: #f6ad55;
            font-size: 18px;
            margin-right: 8px;
            opacity: 0;
            animation: iconFadeIn 0.4s ease-out forwards;
            animation-delay: inherit;
          }

          &:hover,
          &:active {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }
        }
      }
    }
  }
}

/* 消息淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 建议内容上浮淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 建议项滑入动画 */
@keyframes suggestionSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 图标淡入动画 */
@keyframes iconFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-10deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 添加淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 内容揭示动画 */
@keyframes reveal-content {
  0% {
    opacity: 0.85;
    filter: blur(0.5px);
  }
  70% {
    opacity: 0.95;
    filter: blur(0.2px);
  }
  100% {
    opacity: 1;
    filter: blur(0);
  }
}

/* 扫光动画 */
@keyframes sweep-shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  30% {
    transform: translateX(-30%) skewX(-15deg);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
    opacity: 0;
  }
}

/* AI聊天内容样式 */
/deep/ .ai-chat-content {
  line-height: 1.6;

  p {
    margin: 8px 0;
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }

  strong {
    font-weight: bold;
    color: #2c5aa0;
  }

  em {
    font-style: italic;
    color: #666;
  }

  ul,
  ol {
    margin: 8px 0;
    padding-left: 20px;

    li {
      margin: 4px 0;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    font-size: 22px;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f8f9fa;
      font-weight: bold;
      color: #2c5aa0;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }

  .fa {
    margin: 0 4px;
    color: #4299e1;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 12px 0 8px 0;
    color: #2c5aa0;

    &:first-child {
      margin-top: 0;
    }

    .fa {
      margin-right: 8px;
      color: #4299e1;
    }
  }

  h3 {
    font-size: 26px;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 4px;
  }

  h4 {
    font-size: 24px;
    color: #2d5b99;
  }

  blockquote {
    border-left: 4px solid #4299e1;
    padding-left: 12px;
    margin: 12px 0;
    color: #666;
    font-style: italic;
  }

  code {
    background-color: #f1f3f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 20px;
  }

  pre {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;

    code {
      background: none;
      padding: 0;
    }
  }

  /* 棋谱分析专用样式 */
  .analysis-section {
    margin: 16px 0;
    padding: 12px;
    background: rgba(66, 153, 225, 0.05);
    border-left: 4px solid #4299e1;
    border-radius: 0 8px 8px 0;
  }

  .coordinate-highlight {
    background: #fef5e7;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #d69e2e;
  }

  .move-analysis {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 10px;
    margin: 8px 0;

    .move-number {
      color: #2c5aa0;
      font-weight: bold;
      font-size: 20px;
    }

    .move-evaluation {
      color: #38a169;
      font-style: italic;
    }

    .move-error {
      color: #e53e3e;
      font-weight: bold;
    }
  }

  .win-rate-change {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 18px;
    font-weight: bold;

    &.positive {
      background: #c6f6d5;
      color: #22543d;
    }

    &.negative {
      background: #fed7d7;
      color: #742a2a;
    }
  }

  .score-table {
    .score-positive {
      color: #38a169;
      font-weight: bold;
    }

    .score-negative {
      color: #e53e3e;
      font-weight: bold;
    }

    .score-neutral {
      color: #4a5568;
    }
  }

  .recommendation-box {
    background: #edf2f7;
    border: 1px solid #cbd5e0;
    border-radius: 8px;
    padding: 12px;
    margin: 10px 0;

    .recommendation-title {
      color: #2c5aa0;
      font-weight: bold;
      margin-bottom: 6px;
    }

    .recommendation-content {
      color: #4a5568;
      line-height: 1.5;
    }
  }

  .ai-verification {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    border-radius: 8px;
    margin: 12px 0;

    .ai-icon {
      color: #ffd700;
      margin-right: 6px;
    }
  }

  /* 通用增强样式 - 即使AI没有使用特定类名也能生效 */
  span[style*='background'] {
    padding: 2px 6px;
    border-radius: 4px;
  }

  div[style*='border-left'] {
    padding: 10px;
    margin: 8px 0;
    border-radius: 0 8px 8px 0;
  }

  /* 为包含特定关键词的元素自动应用样式 */
  span:contains('手'),
  span:contains('目'),
  span:contains('%') {
    font-weight: bold;
  }

  /* 表格自动优化 */
  table:not([class]) {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    font-size: 22px;

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f8f9fa;
      font-weight: bold;
      color: #2c5aa0;
    }
  }
}

/* 新增HTML内容样式 */
.msg-html-content {
  width: 100%;

  // 为AI聊天内容添加专用样式
  :deep(.ai-chat-content) {
    line-height: 1.6;

    h3,
    h4 {
      color: #2c5aa0;
      margin: 12px 0 8px 0;
      font-weight: bold;
    }

    p {
      margin: 8px 0;
    }

    .fa {
      margin-right: 6px;
      color: #4299e1;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 12px 0;

      th,
      td {
        border: 1px solid #e2e8f0;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background: #f7fafc;
        font-weight: bold;
      }

      .score-positive {
        color: #38a169;
        font-weight: bold;
      }

      .score-negative {
        color: #e53e3e;
        font-weight: bold;
      }
    }

    .analysis-section {
      background: rgba(66, 153, 225, 0.05);
      border-left: 4px solid #4299e1;
      padding: 12px;
      margin: 12px 0;
      border-radius: 0 8px 8px 0;
    }

    .coordinate-highlight {
      background: #fed7d7;
      color: #c53030;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
      font-weight: bold;
    }

    .move-analysis {
      background: #f7fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 10px;
      margin: 8px 0;

      .move-number {
        background: #4299e1;
        color: white;
        padding: 6px 8px;
        border-radius: 12px;
        font-size: 16px;
        margin-right: 8px;
      }

      .move-evaluation {
        color: #2d3748;
        margin-right: 8px;
      }

      .move-error {
        color: #e53e3e;
        font-weight: bold;
      }
    }

    .win-rate-change {
      font-weight: bold;
      padding: 2px 6px;
      border-radius: 4px;

      &.positive {
        background: #c6f6d5;
        color: #22543d;
      }

      &.negative {
        background: #fed7d7;
        color: #742a2a;
      }
    }

    .recommendation-box {
      background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
      border: 1px solid #9ae6b4;
      border-radius: 8px;
      padding: 12px;
      margin: 12px 0;

      .recommendation-title {
        font-weight: bold;
        color: #22543d;
        margin-bottom: 6px;
      }

      .recommendation-content {
        color: #2f855a;
      }
    }

    .ai-verification {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px;
      border-radius: 8px;
      margin: 12px 0;

      .ai-icon {
        color: #ffd700;
        margin-right: 6px;
      }
    }
  }
}

/* 流式输出相关样式 */
.msg-item {
  .msg-bubble {
    &.streaming-animation {
      .content-wrapper {
        opacity: 1;
      }
    }
  }
}
</style>
