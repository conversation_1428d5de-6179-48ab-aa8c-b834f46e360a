<template>
  <div class="ai-chat-page">
    <div class="bg-animation">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
      <div class="light-spot light-spot-1"></div>
      <div class="light-spot light-spot-2"></div>
      <div class="light-spot light-spot-3"></div>
    </div>

    <div class="glass-container">
      <chat-header />
      <chat-messages
        :messages="messages"
        :ai-avatar="aiAvatar"
        @use-suggestion="handleUseSuggestion"
      />
      <quick-questions @ask="handleQuickAsk" />
      <chat-input @send="handleSend" />
    </div>
  </div>
</template>

<script>
import ChatHeader from './components/ChatHeader.vue';
import ChatMessages from './components/ChatMessages.vue';
import ChatInput from './components/ChatInput.vue';
import QuickQuestions from './components/QuickQuestions.vue';
import aiChat from '@/api/aiChat';
import aiConfig from '@/config/aiModel';
import { generateUUID } from '@/utils/util';

export default {
  name: 'AiChatPage',
  components: {
    ChatHeader,
    ChatMessages,
    ChatInput,
    QuickQuestions,
  },
  data() {
    return {
      aiAvatar:
        'https://tg-prod.oss-cn-beijing.aliyuncs.com/6017390e-0ffc-4c7f-9e82-103f10d051e4.png',
      messages: [
        {
          from: 'ai',
          text: '<div class="ai-chat-content"><p>你好呀！小朋友，我是棋精灵，可以和我聊围棋哦~ ⚫⚪</p></div>',
          time: this.getTime(),
          suggestions: ['围棋是什么呀？', '学习围棋有哪些好处？', '我想学围棋的基本规则'],
          type: 'complete',
          isHTML: true,
        },
      ],
      isLoading: false,
      streamingMessageId: null,
    };
  },
  methods: {
    async handleSend(text) {
      if (!text || this.isLoading) return;

      // 添加用户消息
      this.messages.push({
        from: 'user',
        text,
        time: this.getTime(),
      });

      this.isLoading = true;
      const thinkingMsgId = generateUUID();

      // 添加思考状态消息
      this.messages.push({
        id: thinkingMsgId,
        from: 'ai',
        type: 'thinking',
      });

      try {
        const allMessages = this.messages.filter((m) => m.type !== 'thinking');
        const contextMessages = allMessages.slice(1).slice(-aiConfig.contextMessages);

        console.log('开始流式请求...'); // 调试信息

        // 使用流式输出
        await aiChat.chatWithAIStream(
          contextMessages,
          // 进度回调
          (progressData) => {
            console.log('收到进度更新:', progressData.content.substring(0, 50) + '...'); // 调试信息

            const idx = this.messages.findIndex(
              (m) => m.id === thinkingMsgId || m.id === this.streamingMessageId,
            );
            if (idx !== -1) {
              if (this.messages[idx].type === 'thinking') {
                // 首次替换thinking状态
                console.log('替换thinking状态为streaming'); // 调试信息
                this.streamingMessageId = generateUUID();
                this.$set(this.messages, idx, {
                  id: this.streamingMessageId,
                  from: 'ai',
                  text: progressData.content,
                  time: this.getTime(),
                  promptType: progressData.promptType,
                  type: 'streaming',
                  animationId: this.streamingMessageId,
                  isHTML: progressData.isHTML || false,
                });
              } else {
                // 更新流式内容
                console.log('更新流式内容'); // 调试信息
                this.$set(this.messages[idx], 'text', progressData.content);
              }
            }
          },
          // 完成回调
          (finalData) => {
            console.log('流式请求完成'); // 调试信息
            const idx = this.messages.findIndex(
              (m) => m.id === this.streamingMessageId || m.id === thinkingMsgId,
            );
            if (idx !== -1) {
              this.$set(this.messages, idx, {
                from: 'ai',
                text: finalData.content,
                time: this.getTime(),
                suggestions: finalData.suggestions,
                animationId: generateUUID(),
                promptType: finalData.promptType,
                type: 'complete',
                isHTML: finalData.isHTML || false,
              });
            }
            this.isLoading = false;
            this.streamingMessageId = null;
          },
          // 错误回调
          (error) => {
            console.error('流式AI请求失败:', error);
            const idx = this.messages.findIndex(
              (m) => m.id === this.streamingMessageId || m.id === thinkingMsgId,
            );
            if (idx !== -1) {
              this.$set(this.messages, idx, {
                from: 'ai',
                text: '<div class="ai-chat-content"><p style="color: #e53e3e;"><i class="fa fa-exclamation-triangle"></i> 抱歉，我遇到了一些问题，请稍后再试。</p></div>',
                time: this.getTime(),
                animationId: generateUUID(),
                type: 'error',
                isHTML: true,
              });
            }
            this.isLoading = false;
            this.streamingMessageId = null;
          },
        );
      } catch (error) {
        console.error('AI请求失败:', error);
        setTimeout(() => {
          const idx = this.messages.findIndex((m) => m.id === thinkingMsgId);
          if (idx !== -1) {
            this.$set(this.messages, idx, {
              from: 'ai',
              text: '<div class="ai-chat-content"><p style="color: #e53e3e;"><i class="fa fa-exclamation-triangle"></i> 抱歉，我遇到了一些问题，请稍后再试。</p></div>',
              time: this.getTime(),
              animationId: generateUUID(),
              isHTML: true,
            });
          }
          this.isLoading = false;
        }, 300);
      }
    },

    handleQuickAsk(question) {
      this.handleSend(question);
    },
    handleUseSuggestion(suggestion) {
      this.handleSend(suggestion);
    },
    getTime() {
      const d = new Date();
      return `${d.getHours().toString().padStart(2, '0')}:${d
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;
    },
  },
};
</script>

<style lang="less" scoped>
.ai-chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #e6f5ff 0%, #f0f9ff 50%, #e5f6ff 100%);
  opacity: 0.95;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234299e1' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.8;
}

.light-spot {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.4;
  animation: float 15s infinite ease-in-out;

  &.light-spot-1 {
    width: 300px;
    height: 300px;
    background: rgba(144, 205, 244, 0.3);
    top: -100px;
    right: -50px;
    animation-delay: 0s;
  }

  &.light-spot-2 {
    width: 250px;
    height: 250px;
    background: rgba(246, 185, 59, 0.2);
    bottom: -80px;
    left: -60px;
    animation-delay: 5s;
  }

  &.light-spot-3 {
    width: 180px;
    height: 180px;
    background: rgba(159, 122, 234, 0.2);
    bottom: 30%;
    right: -80px;
    animation-delay: 10s;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-30px) translateX(20px);
  }
  50% {
    transform: translateY(10px) translateX(-20px);
  }
  75% {
    transform: translateY(20px) translateX(10px);
  }
}

.glass-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.7);
}
</style>
