<template>
  <div class="time-album-container">
    <div class="time-album-header">
      <div class="time-album-header-left">
        <img :src="avatar" />
        <div class="time-album-header-left-title">学员{{ student_name }}</div>
      </div>
      <div @click="datePickerShow = true" class="time-album-header-right">
        <div class="time-album-header-right-item">
          <img class="icon" src="@/assets/calar.png" alt="logo" />
          <span>{{ currentDateText }}</span>
        </div>
      </div>
      <!-- 路线背景 -->
      <div class="time-album-header-bg"></div>
    </div>
    <!-- 时光相册内容区域 -->
    <div class="time-album-content-wrapper">
      <div class="title-bg">
        <img
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/72679394-5024-44b6-82b1-8defc5c7f68e.png"
          alt=""
        />
      </div>
      <div class="time-album-content">
        <div class="time-album-content-item-wrapper" ref="scrollContainer" @scroll="handleScroll">
          <!-- 顶部加载动画 -->
          <div class="loading-container top-loading" v-if="isLoadingUp && list.length > 0">
            <div class="loading-spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
            <span class="loading-text">加载中...</span>
          </div>

          <div v-for="(item, index) in list" :key="item.id">
            <div
              v-if="(index + 1) % 2 === 1"
              :class="['time-album-content-item', index === 0 ? 'first' : '']"
            >
              <div @click="handleItemClick(item.id)" class="item-left">
                <div class="timeline-date">
                  <div class="timeline-date-year">{{ item.date.split('-')[0] }}</div>
                  <div class="timeline-date-month">
                    <span class="month">{{ item.date.split('-')[1] }}月</span>
                    <span class="day">{{ item.date.split('-')[2] }}</span>
                    <span class="mark-box">{{ item.mark_name }}</span>
                  </div>
                </div>
                <div class="ellipsis timeline-title">{{ item.title }}</div>
                <div class="ellipsis-2 timeline-content">
                  {{ item.content }}
                </div>
                <div class="ellipsis timeline-record-person">记录人：{{ item.record_person }}</div>
              </div>
              <div @click="handleImgBoxClick($event, index)" class="item-right">
                <div v-if="item.media_list.length" class="item-right-img-box">
                  <div class="item-right-img">
                    <img
                      v-if="item.media_list[0].video_url"
                      :src="
                        item.media_list[0].image_url || item.media_list[0].video_url + imageProcess
                      "
                    />
                    <img v-else :src="item.media_list[0].image_url" />
                    <div v-if="item.media_list[0].video_url" class="item-right-img-play-btn"></div>
                    <span class="img-nums">{{ item.media_list.length }}项</span>
                  </div>
                </div>
                <div v-if="item.media_list.length > 1" class="item-right-img-box">
                  <div class="item-right-img">
                    <img
                      v-if="item.media_list[1].video_url"
                      :src="
                        item.media_list[1].image_url || item.media_list[1].video_url + imageProcess
                      "
                    />
                    <img v-else :src="item.media_list[1].image_url" />
                  </div>
                </div>
              </div>
            </div>

            <div v-if="(index + 1) % 2 === 0" class="time-album-content-item even">
              <div @click="handleImgBoxClick($event, index)" class="item-right">
                <div v-if="item.media_list.length" class="item-right-img-box">
                  <div class="item-right-img">
                    <img
                      v-if="item.media_list[0].video_url"
                      :src="
                        item.media_list[0].image_url || item.media_list[0].video_url + imageProcess
                      "
                      alt=""
                    />
                    <img v-else :src="item.media_list[0].image_url" />
                    <div v-if="item.media_list[0].video_url" class="item-right-img-play-btn"></div>
                    <span class="img-nums">{{ item.media_list.length }}项</span>
                  </div>
                </div>
                <div v-if="item.media_list.length > 1" class="item-right-img-box">
                  <div class="item-right-img">
                    <img
                      v-if="item.media_list[1].video_url"
                      :src="
                        item.media_list[1].image_url || item.media_list[1].video_url + imageProcess
                      "
                      alt=""
                    />
                    <img v-else :src="item.media_list[1].image_url" />
                  </div>
                </div>
              </div>
              <div @click="handleItemClick(item.id)" class="item-left">
                <div class="timeline-date">
                  <div class="timeline-date-year">{{ item.date.split('-')[0] }}</div>
                  <div class="timeline-date-month">
                    <span class="month">{{ item.date.split('-')[1] }}月</span>
                    <span class="day">{{ item.date.split('-')[2] }}</span>
                    <span class="mark-box">{{ item.mark_name }}</span>
                  </div>
                </div>
                <div class="ellipsis timeline-title">{{ item.title }}</div>
                <div class="ellipsis-2 timeline-content">
                  {{ item.content }}
                </div>
                <div class="ellipsis timeline-record-person">记录人：{{ item.record_person }}</div>
              </div>
            </div>

            <!-- 导航路线指示图 -->
            <div
              v-if="index !== list.length - 1"
              :class="['content-item-line', index % 2 === 0 ? 'odd' : 'even']"
            ></div>
          </div>

          <div class="empty-animation-container">
            <empty-animation />
            <span>每一步成长都值得珍藏～</span>
          </div>

          <!-- 加载中动画 - 只有当加载更多数据或初始化时显示 -->
          <div class="loading-container" v-if="loading && (!isLoadingUp || list.length === 0)">
            <div class="loading-spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
            <span class="loading-text">加载中...</span>
          </div>
        </div>
        <!-- 向下滑动指示箭头 -->
        <div class="scroll-down-arrow" v-if="list.length > 0 && showScrollArrow"></div>
      </div>
    </div>
    <!-- 旗帜飘动 -->
    <div v-if="flagFlowShow" class="flag-flow"></div>
    <!-- 底部装饰背景 -->
    <div class="bg-bottom">
      <div class="bg-bottom-item bg1"></div>
      <div class="bg-bottom-item bg2"></div>
      <div class="bg-bottom-item bg3"></div>
      <div class="bg-bottom-item bg4"></div>
      <div class="bg-bottom-item bg5"></div>
    </div>

    <!-- 添加相册浏览组件 -->
    <album-swiper
      :visible.sync="albumSwiperVisible"
      :media-list="albumMediaList"
      :initial-index="initialMediaIndex"
      :origin-position="originPosition"
    />
    <div class="date-picker-container">
      <van-popup v-model="datePickerShow" position="bottom">
        <van-picker
          title="请选择记录日期"
          show-toolbar
          :columns="pickerColumns"
          :loading="pickerLoading"
          @confirm="handleDateConfirm"
          @cancel="handleDateCancel"
        />
      </van-popup>
    </div>
    <!-- 编辑按钮 -->
    <div v-if="from === '1'" @click="handleEditBtnClick" class="edit-btn">
      <img
        width="70"
        height="70"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/693f6c52-a9fe-4b38-9690-8c7768f50e7e.png"
        alt="发布/编辑"
      />
    </div>
  </div>
</template>

<script>
import { getUrlParams, formatDate } from '@/utils/util';
import { genders_list, album_type_list } from '@/utils/const';
import AlbumSwiper from './albumSwiper.vue';
import EmptyAnimation from './emptyAnimation.vue';
import { getTimeAlbumList, getTimeAlbumTimePointList } from '@/api/timeAlbum';
import { Toast } from 'vant';

export default {
  name: 'TimeAlbum',
  components: {
    AlbumSwiper,
    EmptyAnimation,
  },
  data() {
    return {
      currentDate: '',
      albumSwiperVisible: false,

      albumMediaList: [], // 当前显示的媒体列表
      initialMediaIndex: 0,
      originPosition: {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
      },
      datePickerShow: false,
      pickerColumns: [],
      pickerLoading: false,
      choose_head: '',
      stu_id: '',
      student_name: '',
      from: '',
      token: '',
      visitor: '',
      operation_id: '',
      list: [],
      flagFlowShow: false,
      imageProcess: '?x-oss-process=video/snapshot,t_2000,m_fast,w_320',
      loading: false,
      noMoreData: false,
      showScrollArrow: true,
      currentPage: 1,
      pageSize: 3,
      upPage: 1, // 顶部数据的页码
      downPage: 1, // 底部数据的页码
      isLoadingUp: false, // 是否正在向上加载数据
      noMoreUpData: false, // 是否没有更多顶部的数据
      lastScrollTop: 0, // 上次滚动位置，用于判断滚动方向
      scrollDirection: 'down', // 当前滚动方向：'up' or 'down'
    };
  },
  created() {
    this.preloadImage();
  },
  mounted() {
    this.init();
    // this.addItemClickEvent();
  },
  computed: {
    avatar() {
      if (this.choose_head) {
        return genders_list[+this.choose_head];
      }
      return genders_list[1];
    },
    currentDateText() {
      return formatDate(this.currentDate, 'YYYY.MM');
    },
  },
  methods: {
    // 预加载旗帜帧图片
    preloadImage() {
      const images = [
        '/static/imgage/flagFlow/00000.png',
        '/static/imgage/flagFlow/00003.png',
        '/static/imgage/flagFlow/00006.png',
        '/static/imgage/flagFlow/00009.png',
        '/static/imgage/flagFlow/00012.png',
        '/static/imgage/flagFlow/00015.png',
        '/static/imgage/flagFlow/00018.png',
        '/static/imgage/flagFlow/00021.png',
        '/static/imgage/flagFlow/00024.png',
        '/static/imgage/flagFlow/00027.png',
        '/static/imgage/flagFlow/00030.png',
        '/static/imgage/flagFlow/00033.png',
        '/static/imgage/flagFlow/00036.png',
        '/static/imgage/flagFlow/00039.png',
        '/static/imgage/flagFlow/00042.png',
      ];
      let count = 0;
      const totalImages = images.length;

      const handleImageLoad = () => {
        count++;
        console.log('图片加载进度', count);
        if (count === totalImages) {
          console.log('图片加载完成', count);
          this.flagFlowShow = true;
        }
      };

      images.forEach((item) => {
        const img = new Image();
        img.onload = handleImageLoad;
        img.onerror = () => {
          console.warn(`图片加载失败: ${item}`);
          handleImageLoad(); // 即使失败也计数，避免永远无法完成加载
        };
        img.src = item;
      });
    },
    async loadPickerData() {
      this.pickerLoading = true;
      const res = await getTimeAlbumTimePointList({
        student_id: this.stu_id,
        token: this.token,
        from: this.from,
        visitor: this.visitor,
        operation_id: this.operation_id,
      });
      this.pickerLoading = false;
      if (res.code === 0) {
        // 转换数据格式
        this.pickerColumns = res.data.map((item) => ({
          text: item.year,
          children: item.month.map((m) => ({
            text: m.padStart(2, '0'), // 确保月份是两位数
          })),
        }));
      }

      // setTimeout(() => {
      //   this.pickerLoading = false;
      //   this.pickerColumns = [
      //     {
      //       text: '2023',
      //       children: [{ text: '01' }, { text: '02' }, { text: '03' }],
      //     },
      //     {
      //       text: '2025',
      //       children: [{ text: '04' }, { text: '05' }, { text: '07' }],
      //     },
      //   ];
      // }, 2000);
    },
    init() {
      const token = getUrlParams('token');
      const choose_head = +getUrlParams('choose_head');
      const stu_id = getUrlParams('stu_id');
      const student_name = getUrlParams('student_name');
      const from = getUrlParams('from');
      const visitor = getUrlParams('visitor');
      const operation_id = getUrlParams('operation_id');
      this.choose_head = choose_head;
      this.stu_id = stu_id;
      this.from = from;
      this.student_name = student_name;
      this.token = token;
      this.visitor = visitor;
      this.operation_id = operation_id;

      console.table({ token, choose_head, stu_id, from, student_name });
      console.log('init');
      this.currentDate = formatDate(new Date(), 'YYYY-MM');
      // 初始使用参数2，表示加载底部数据
      console.log('初始化 - 加载底部数据');
      this.getTimeAlbumList(false, 2);
      this.loadPickerData();
    },
    albumTypeFilter(type) {
      return album_type_list.find((item) => item.id === type)?.name;
    },
    // 获取时光相册列表
    // isLoadMore: 是否加载更多
    // up_down_status: 1 往下滑动查询顶部数据 2 往上滑动查询底部数据
    async getTimeAlbumList(isLoadMore = false, up_down_status = 2) {
      console.log(
        `getTimeAlbumList调用 - isLoadMore: ${isLoadMore}, up_down_status: ${up_down_status}`,
      );

      // 如果不是加载更多，则重置分页参数
      if (!isLoadMore) {
        this.upPage = 1;
        this.downPage = 1;
        this.list = [];
        this.noMoreData = false;
        this.noMoreUpData = false;
        this.isLoadingUp = false; // 初始化时重置上拉加载状态
      }

      // 根据加载方向设置当前页码
      const currentPage = up_down_status === 1 ? this.upPage : this.downPage;
      console.log(`当前页码 - up_down_status: ${up_down_status}, currentPage: ${currentPage}`);

      // 如果已经在加载中或已经没有更多数据，则返回
      if (
        this.loading ||
        (isLoadMore && up_down_status === 1 && this.noMoreUpData) ||
        (isLoadMore && up_down_status === 2 && this.noMoreData)
      ) {
        console.log(
          '跳过加载，原因:',
          this.loading
            ? '正在加载中'
            : up_down_status === 1 && this.noMoreUpData
            ? '顶部没有更多数据'
            : '底部没有更多数据',
        );
        return;
      }

      // 设置对应方向的加载状态
      this.loading = true;
      console.log('设置加载状态: loading = true');
      if (up_down_status === 1) {
        this.isLoadingUp = true;
        console.log('设置顶部加载状态: isLoadingUp = true');
      }

      // 获取当前月份的最后一天作为查询时间
      const currentMonthLastDay = this.getLastDayOfMonth(this.currentDate);
      // 为了看到加载效果延迟1.5s
      await new Promise((resolve) => setTimeout(resolve, 1500));
      const res = await getTimeAlbumList({
        student_id: this.stu_id,
        start_year_month: currentMonthLastDay,
        page: currentPage,
        page_size: this.pageSize,
        token: this.token,
        from: this.from,
        visitor: this.visitor,
        operation_id: this.operation_id,
        up_down_status: up_down_status, // 1 往下滑动查询顶部数据 2 往上滑动查询底部数据
      });

      if (res.code === 0) {
        const arr = res?.data?.results?.map((item) => {
          return {
            id: item.id,
            title: item.album_title,
            content: item.album_values,
            record_person: item.employee_name,
            date: formatDate(item.event_time, 'YYYY-MM-DD'),
            mark_name: this.albumTypeFilter(item.album_type),
            media_list: item.album_picture_video,
          };
        });

        if (isLoadMore) {
          if (up_down_status === 1) {
            // 往下滑动查询顶部数据，追加数据到顶部
            // 记住当前滚动位置和高度
            const scrollContainer = this.$refs.scrollContainer;
            const oldScrollHeight = scrollContainer.scrollHeight;
            const oldScrollTop = scrollContainer.scrollTop;

            // 在顶部添加新数据
            this.list = [...(arr || []), ...this.list];

            // 判断是否还有更多数据
            this.noMoreUpData = !arr || arr.length < this.pageSize;

            // 如果有数据，增加上滑页码
            if (arr && arr.length > 0) {
              this.upPage += 1;

              // 在DOM更新后调整滚动位置，保持视觉上的稳定
              this.$nextTick(() => {
                const newScrollHeight = scrollContainer.scrollHeight;
                const heightDiff = newScrollHeight - oldScrollHeight;
                scrollContainer.scrollTop = oldScrollTop + heightDiff;
              });
            }
            console.log(
              '顶部数据加载完成，数据长度:',
              arr?.length || 0,
              '新的upPage:',
              this.upPage,
            );
          } else {
            // 往上滑动查询底部数据，追加数据到底部
            this.list = [...this.list, ...(arr || [])];
            // 判断是否还有更多数据
            this.noMoreData = !arr || arr.length < this.pageSize;
            // 如果有数据，增加下滑页码
            if (arr && arr.length > 0) {
              this.downPage += 1;
            }
            console.log(
              '底部数据加载完成，数据长度:',
              arr?.length || 0,
              '新的downPage:',
              this.downPage,
            );
          }
        } else {
          // 初始化数据
          this.list = arr || [];
          // 判断上下方向是否还有更多数据
          this.noMoreData = !arr || arr.length < this.pageSize;
          this.noMoreUpData = false; // 初始化时默认上方有更多数据

          // 如果有数据，设置初始页码
          if (arr && arr.length > 0) {
            this.downPage = 2; // 下一次下滑加载从第2页开始
          }
        }
      } else {
        // 请求出错时也标记为有更多数据
        if (up_down_status === 1) {
          this.noMoreData = false;
        } else {
          this.noMoreUpData = false;
        }
      }

      // 重置加载状态
      this.loading = false;
      console.log('重置加载状态: loading = false');

      if (up_down_status === 1) {
        this.isLoadingUp = false;
        console.log('重置顶部加载状态: isLoadingUp = false');
      }

      console.log('数据加载完成:', {
        listLength: this.list.length,
        noMoreData: this.noMoreData,
        noMoreUpData: this.noMoreUpData,
        upPage: this.upPage,
        downPage: this.downPage,
      });
    },

    // 获取指定月份的最后一天
    getLastDayOfMonth(dateStr) {
      const [year, month] = dateStr.split('-');
      // 创建下个月的第0天，即当前月的最后一天
      const date = new Date(parseInt(year), parseInt(month), 0);
      return formatDate(date, 'YYYY-MM-DD');
    },

    handleEditBtnClick() {
      console.log('编辑');
      wx.miniProgram.navigateTo({
        url: `/pages/student/subpages/timeAlbum/edit?from=${this.from}&choose_head=${this.choose_head}&stu_id=${this.stu_id}&student_name=${this.student_name}&type=create`,
      });
    },
    // 处理日期选择确认
    handleDateConfirm(value) {
      console.log(value);
      // 如果选择了新的日期
      const newDate = value.join('-');
      if (newDate !== this.currentDate) {
        this.currentDate = newDate;
        this.upPage = 1;
        this.downPage = 1;
        this.list = [];
        this.noMoreData = false;
        this.noMoreUpData = false;
        console.log('日期切换 - 加载底部数据');
        this.getTimeAlbumList(false, 2); // 切换日期后，使用参数2加载底部数据
      }
      this.handleDateCancel();
    },
    handleDateCancel() {
      this.datePickerShow = false;
    },

    handleItemClick(id) {
      console.log(id);
      const { avatar } = this;
      wx.miniProgram.navigateTo({
        url: `/pages/student/subpages/timeAlbum/detail?id=${id}&avatar=${avatar}&from=${this.from}&stu_id=${this.stu_id}&student_name=${this.student_name}&visitor=${this.visitor}&operation_id=${this.operation_id}`,
      });
    },
    // 为所有.item-right元素添加点击事件
    // addItemClickEvent() {
    //   console.log('添加点击事件');
    //   this.$nextTick(() => {
    //     const imgBoxes = document.querySelectorAll('.item-right');
    //     imgBoxes.forEach((box, index) => {
    //       box.addEventListener('click', (e) => {
    //         this.handleImgBoxClick(e, index);
    //       });
    //     });
    //   });
    // },

    // 处理图片盒子点击事件
    handleImgBoxClick(e, index) {
      // 阻止事件冒泡
      e.stopPropagation();

      // 设置为屏幕中心位置
      this.originPosition = {
        x: 0, // 屏幕中心
        y: 0, // 屏幕中心
        width: 100, // 小一点的初始大小
        height: 100,
      };

      // 根据点击的区域索引获取对应的媒体列表
      this.albumMediaList = this.list[index].media_list || [];

      // 设置初始索引
      this.initialMediaIndex = 0; // 每次打开从第一张开始

      // 显示弹窗
      this.albumSwiperVisible = true;
    },
    handleScroll(e) {
      const container = e.target;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const scrollThreshold = 200; // 底部滚动阈值，增加敏感度
      const topThreshold = 200; // 顶部滚动阈值，增加敏感度

      // 判断滚动方向
      if (scrollTop < this.lastScrollTop) {
        this.scrollDirection = 'up';
        Toast.clear();
      } else if (scrollTop > this.lastScrollTop) {
        this.scrollDirection = 'down';
      }
      this.lastScrollTop = scrollTop;

      // 修改箭头显示逻辑：只有接近底部时才隐藏箭头
      this.showScrollArrow = scrollTop + clientHeight < scrollHeight - 150;

      console.log(
        `滚动信息: 当前位置=${scrollTop.toFixed(0)}/${scrollHeight.toFixed(0)}, 方向=${
          this.scrollDirection
        }, 底部距离=${(scrollHeight - scrollTop - clientHeight).toFixed(0)}`,
      );

      // 检测是否接近底部，加载更多底部数据 (up_down_status=2)
      if (
        scrollTop + clientHeight >= scrollHeight - scrollThreshold &&
        this.scrollDirection === 'down'
      ) {
        console.log('已滚动到底部附近');
        if (!this.loading && !this.noMoreData) {
          console.log('触发底部加载，up_down_status=2');
          this.getTimeAlbumList(true, 2); // 参数2表示加载底部数据
        } else if (this.noMoreData) {
          Toast({
            message: '没有更多数据了',
            position: 'bottom',
            duration: 1500,
          });
        }
      }

      // 检测是否接近顶部，加载更多顶部数据 (up_down_status=1)
      if (scrollTop <= topThreshold && this.scrollDirection === 'up') {
        console.log('已滚动到顶部附近');
        if (!this.isLoadingUp && !this.noMoreUpData && !this.loading) {
          console.log('触发顶部加载，up_down_status=1');
          this.getTimeAlbumList(true, 1); // 参数1表示加载顶部数据
        } else {
          console.log(
            '未触发顶部加载，原因:',
            this.isLoadingUp
              ? 'isLoadingUp=true'
              : this.noMoreUpData
              ? 'noMoreUpData=true'
              : this.loading
              ? 'loading=true'
              : '未知原因',
          );
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.time-album-container {
  font-family: 'PingFang SC';
  max-width: 100%;
  height: 100vh;
  background-color: #fff;
  background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/58b47a94-d95f-4cbd-af9c-f521a73d7a10.jpg');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  .time-album-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 750px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 35px;
    padding-top: 30px;
    box-sizing: border-box;
    .time-album-header-bg {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/d21b2059-19e3-4973-98b7-46802ea483f4.png');
      background-size: cover;
      width: 388px;
      height: 198px;
      // z-index: -1;
    }
    .time-album-header-left {
      display: flex;
      align-items: center;
      img {
        width: 120px;
        height: 120px;
        box-sizing: border-box;
        margin-right: 24px;
      }
      .time-album-header-left-title {
        color: #b34c0f;
        font-size: @font-size-32;
        font-weight: 500;
        line-height: normal;
      }
    }
    .time-album-header-right {
      display: flex;
      align-items: center;
      width: 230px;
      height: 70px;
      flex-shrink: 0;
      border-radius: 44px 0px 0px 44px;
      opacity: 0.8;
      background: #fff;
      box-shadow: 0px 4px 30px 0px rgba(255, 155, 137, 0.34);
      backdrop-filter: blur(30px);
      .time-album-header-right-item {
        display: flex;
        align-items: center;
        margin-left: 10px;
        img {
          width: 40px;
          height: 40px;
          margin-left: 30px;
          margin-right: 10px;
        }
        span {
          color: #ff8411;
          font-family: 'PingFang SC';
          font-size: @font-size-32;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }
    }
  }
  .time-album-content-wrapper {
    position: absolute;
    top: 200px;
    left: 50%;
    transform: translateX(-50%);
    backdrop-filter: blur(23px);
    .title-bg {
      position: absolute;
      top: -22px;
      left: 50%;
      transform: translateX(-50%);
      overflow: hidden;
      z-index: 9;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        height: 100%;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.5) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        animation: shine-effect 5s infinite;
      }

      img {
        width: 320px;
        height: 80px;
        object-fit: cover;
        vertical-align: middle;
      }
    }
    .time-album-content {
      width: 686px;
      height: calc(100vh - 200px - 200px);
      border-radius: 30px;
      border: 1px solid #ff7b77;
      opacity: 0.8;
      background: #fff;
      backdrop-filter: blur(23px);
      overflow: hidden;
      // padding-top: 60px;

      .time-album-content-item-wrapper {
        height: calc(100vh - 200px - 200px - 60px);
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
        box-sizing: border-box;
        margin-top: 70px;
        &::-webkit-scrollbar {
          display: none;
        }
        .time-album-content-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 30px;
          .item-left {
            width: 320px;
            margin-right: 30px;
            .timeline-date {
              // display: flex;
              // align-items: center;
              .timeline-date-year {
                font-size: 30px;
                color: #333;
                font-weight: 600;
                width: 73px;
                background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
                background-size: 73px 13px;
                background-repeat: no-repeat;
                background-position: left 20px;
              }
              .timeline-date-month {
                display: flex;
                align-items: center;
                .month {
                  color: #333;
                  font-size: 24px;
                  font-style: normal;
                  font-weight: 500;
                }
                .day {
                  color: #333;
                  font-size: 34px;
                  font-weight: 600;
                }
                .mark-box {
                  margin-left: 20px;
                  font-size: 24px;
                  font-weight: 500;
                  padding: 3px 16px;
                  color: #fff;
                  border-radius: 16px 8px;
                  background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
                }
              }
            }
            .timeline-title {
              color: #333;
              font-size: 32px;
              font-weight: 500;
              line-height: 48px;
            }
            .timeline-content {
              color: #666;
              font-size: 26px;
              font-weight: 400;
              line-height: 38px;
            }
            .timeline-record-person {
              color: #999;
              font-size: 24px;
              font-weight: 400;
              line-height: 38px;
            }
          }
          .item-right {
            display: flex;
            align-items: center;
            flex: 1;
            position: relative;
            height: 258px;
            overflow: hidden;
            .item-right-img-box {
              position: absolute;
              top: 0;
              left: 0;
              background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/2aa66c05-b8e0-413f-8c86-f7c2e5db120c.png');
              background-size: cover;
              background-position: center;
              width: 200px;
              height: 258px;
              z-index: 5;
              &:nth-child(2) {
                transform: scale(0.85) rotate(20deg);
                transform-origin: bottom;
                z-index: 4;
                opacity: 0.7;
              }
              .item-right-img {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 176px;
                height: 234px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                img {
                  width: 100%;
                  height: auto;
                }
              }
              .item-right-img-play-btn {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png');
                background-size: cover;
                background-position: center;
              }
              .img-nums {
                display: block;
                position: absolute;
                bottom: 0px;
                right: 0px;
                color: #fff;
                font-size: 24px;
                font-weight: 500;
                line-height: 40px;
                width: 70px;
                height: 40px;
                text-align: center;

                border-radius: 16px 8px 0px 8px;
                background: rgba(0, 0, 0, 0.47);
              }
            }
          }
        }
        .time-album-content-item.first {
          padding-top: 6px;
        }
        .time-album-content-item.even {
          .item-left {
            margin-right: 0;
          }
          .item-right {
            margin-right: 30px;
          }
        }
        .time-album-content-item.odd {
          .item-left {
            margin-right: 70px;
          }
          .item-right {
            margin-right: 0;
          }
        }
        .content-item-line {
          &.odd {
            width: 188px;
            height: 90px;
            background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/1b35dcad-54f6-419c-adcb-d4b402e9b4a9.png');
            background-size: 100% 100%;
            background-position: center;
            margin-left: 160px;
            margin-top: -20px;
          }
          &.even {
            width: 180px;
            height: 90px;
            background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/80d07945-c4ac-4f1b-84fe-ffa038b24414.png');
            background-size: 100% 100%;
            margin-left: 190px;
            margin-top: 10px;
          }
        }
      }
      .scroll-down-arrow {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 30px;
        background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/8a4bd43d-d69c-4a73-80b1-7dc47e969ce5.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 10;
        animation: arrow-bounce 2s ease-in-out infinite;
        cursor: pointer;

        &:after {
          content: '';
          position: absolute;
          top: -10px;
          left: -10px;
          right: -10px;
          bottom: -10px;
          background: radial-gradient(
            circle,
            rgba(255, 132, 17, 0.2) 0%,
            rgba(255, 255, 255, 0) 70%
          );
          border-radius: 50%;
          z-index: -1;
          opacity: 0;
          animation: pulse-effect 2s ease-in-out infinite;
        }
      }
      .empty-animation-container {
        padding: 40px 0;
        text-align: center;
        span {
          display: block;
          color: #999;
          font-size: 28px;
          font-weight: 400;
          margin-top: 20px;
        }
      }

      .loading-container {
        padding: 20px 0;
        text-align: center;
        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;

          .bounce1,
          .bounce2,
          .bounce3 {
            width: 16px;
            height: 16px;
            background-color: #ff8411;
            border-radius: 100%;
            display: inline-block;
            margin: 0 4px;
            animation: sk-bouncedelay 1.4s infinite ease-in-out both;
          }

          .bounce1 {
            animation-delay: -0.32s;
          }

          .bounce2 {
            animation-delay: -0.16s;
          }
        }

        .loading-text {
          color: #ff8411;
          font-size: 24px;
          font-weight: 400;
        }

        &.top-loading {
          padding: 10px 0;
          position: sticky;
          top: 0;
          z-index: 5;
          background-color: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(5px);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          border-radius: 0 0 15px 15px;
          margin-bottom: 10px;

          .loading-spinner {
            margin-bottom: 0;
          }

          .loading-text {
            font-size: 20px;
            margin-left: 10px;
          }
        }
      }
    }
  }
  .flag-flow {
    position: absolute;
    top: 150px;
    right: 0;
    width: 168px;
    height: 224px;
    background-image: url('/static/imgage/flagFlow/00000.png');
    background-size: auto 100%;
    background-position: center;
    animation: flag-flow 2.25s steps(1) infinite;
  }
  .bg-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 136px;
    .bg-bottom-item {
      position: absolute;

      width: 750px;
      background-size: cover;
      background-position: center;
    }
    .bg1 {
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/f0224013-518c-48c4-95ea-0cacbb01f198.png');
      height: 136px;
      z-index: 1;
      left: 0;
      bottom: 0;
    }
    .bg2 {
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/db7c2f3f-5c3d-4429-b56c-165b217d6c75.png');
      height: 117px;
      z-index: 2;
      left: 0;
      bottom: 0;
    }
    .bg3 {
      width: 450px;
      height: 236px;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/9a17bbe9-ad65-4f5a-bd13-cdb6adbbe1ca.png');
      z-index: 3;
    }
    //左侧的花束
    .bg4 {
      width: 138px;
      height: 260px;
      bottom: 6px;
      left: -10px;
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/307ca3f0-1f71-4c36-907d-ed875c3c629a.png');
      z-index: 3;
      transform-origin: bottom center;
      animation: flower-flow 5s linear infinite;
      will-change: transform;
    }
    //右侧的花束
    .bg5 {
      width: 125px;
      height: 320px;
      right: 0;
      bottom: 0px;
      // transform: rotate(-3.037deg);
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/787e7146-82e1-4a05-82ea-57d0f9f48b03.png');
      z-index: 3;
      transform-origin: bottom center;
      animation: flower-flow-right 7s linear infinite;
      will-change: transform;
    }
  }
  /deep/ .date-picker-container {
    .van-popup {
      border-radius: 22px 22px 0 0;
      .van-picker {
        .van-picker__columns {
          padding: 0 200px;
        }
        .van-picker__confirm {
          color: #333;
        }
      }
    }
  }
  .edit-btn {
    position: absolute;
    bottom: 100px;
    right: 10px;
    z-index: 10;
  }
}
// 动画 花束随风飘动
@keyframes flower-flow {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(8deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

// 右侧花朵顺时针摆动
@keyframes flower-flow-right {
  0% {
    transform: translate(0, 0) rotate(0);
  }
  20% {
    transform: translate(2px, -2px) rotate(2deg);
  }
  40% {
    transform: translate(4px, -4px) rotate(4deg);
  }
  60% {
    transform: translate(3px, -3px) rotate(3deg);
  }
  80% {
    transform: translate(2px, -2px) rotate(2deg);
  }
  100% {
    transform: translate(0, 0) rotate(0);
  }
}

// 旗帜飘动序列帧动画，优化为15帧
@keyframes flag-flow {
  0% {
    background-image: url('/static/imgage/flagFlow/00000.png');
  }
  7.14% {
    background-image: url('/static/imgage/flagFlow/00003.png');
  }
  14.28% {
    background-image: url('/static/imgage/flagFlow/00006.png');
  }
  21.42% {
    background-image: url('/static/imgage/flagFlow/00009.png');
  }
  28.56% {
    background-image: url('/static/imgage/flagFlow/00012.png');
  }
  35.7% {
    background-image: url('/static/imgage/flagFlow/00015.png');
  }
  42.84% {
    background-image: url('/static/imgage/flagFlow/00018.png');
  }
  49.98% {
    background-image: url('/static/imgage/flagFlow/00021.png');
  }
  57.12% {
    background-image: url('/static/imgage/flagFlow/00024.png');
  }
  64.26% {
    background-image: url('/static/imgage/flagFlow/00027.png');
  }
  71.4% {
    background-image: url('/static/imgage/flagFlow/00030.png');
  }
  78.54% {
    background-image: url('/static/imgage/flagFlow/00033.png');
  }
  85.68% {
    background-image: url('/static/imgage/flagFlow/00036.png');
  }
  92.82% {
    background-image: url('/static/imgage/flagFlow/00039.png');
  }
  100% {
    background-image: url('/static/imgage/flagFlow/00042.png');
  }
}

// 扫光动画效果
@keyframes shine-effect {
  0% {
    left: -100%;
  }
  30% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

// 箭头弹跳动画
@keyframes arrow-bounce {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 0.5;
  }
}

// 脉冲效果
@keyframes pulse-effect {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
