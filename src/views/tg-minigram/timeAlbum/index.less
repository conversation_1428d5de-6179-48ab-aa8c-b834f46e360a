.time-album-container {
  font-family: 'PingFang SC';
  max-width: 100%;
  height: 100vh;
  background-color: #fff;
  background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/58b47a94-d95f-4cbd-af9c-f521a73d7a10.jpg');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  .time-album-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 750px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 35px;
    padding-top: 12px;
    box-sizing: border-box;
    .time-album-header-bg {
      position: absolute;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/2d723685-9b5b-4db0-9c11-0294aec504f9.png');
      background-size: cover;
      width: 272px;
      height: 198px;
      // z-index: -1;
    }
    .time-album-header-left {
      display: flex;
      align-items: center;
      img {
        width: 120px;
        height: 120px;
        box-sizing: border-box;
        margin-right: 16px;
      }
      .time-album-header-left-title {
        color: #b34c0f;
        font-size: @font-size-32;
        font-weight: 500;
        line-height: normal;
      }
    }
    .time-album-header-right {
      display: flex;
      align-items: center;
      width: 230px;
      height: 70px;
      flex-shrink: 0;
      border-radius: 44px 0px 0px 44px;
      opacity: 0.8;
      background: #fff;
      box-shadow: 0px 4px 30px 0px rgba(255, 155, 137, 0.34);
      backdrop-filter: blur(30px);
      .time-album-header-right-item {
        display: flex;
        align-items: center;
        margin-left: 10px;
        img {
          width: 40px;
          height: 40px;
          margin-left: 20px;
          margin-right: 10px;
        }
        span {
          color: #ff8411;
          font-family: 'PingFang SC';
          font-size: @font-size-32;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }
    }
  }
  .time-album-content-wrapper {
    position: absolute;
    top: 160px;
    left: 50%;
    transform: translateX(-50%);
    backdrop-filter: blur(23px);
    .title-bg {
      position: absolute;
      top: -22px;
      left: 50%;
      transform: translateX(-50%);
      overflow: hidden;
      z-index: 9;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 50%;
        height: 100%;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.5) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        animation: shine-effect 5s infinite;
      }

      img {
        width: 320px;
        height: 80px;
        object-fit: cover;
        vertical-align: middle;
      }
    }
    .time-album-content {
      width: 686px;
      height: calc(100vh - 200px - 150px);
      border-radius: 30px;
      border: 1px solid #ff7b77;
      opacity: 0.8;
      background: #fff;
      backdrop-filter: blur(23px);
      overflow: hidden;
      position: relative;

      // 下拉刷新提示
      .pull-down-tip {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 10;
        background-color: rgba(255, 132, 17, 0.1);
        border-radius: 20px;
        padding: 8px 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        animation: fade-in-out 2s ease-in-out infinite;

        .pull-down-arrow {
          width: 20px;
          height: 20px;
          margin-bottom: 5px;
          background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/8a4bd43d-d69c-4a73-80b1-7dc47e969ce5.png');
          background-size: contain;
          background-position: center;
          transform: rotate(180deg);
          animation: bounce-up-down 1.5s ease-in-out infinite;
        }

        span {
          color: #ff8411;
          font-size: 24px;
          font-weight: 500;
        }
      }

      .time-album-content-item-wrapper {
        height: calc(100vh - 200px - 200px - 10px);
        -webkit-overflow-scrolling: touch;
        overflow-y: auto;
        box-sizing: border-box;
        margin-top: 70px;
        &.no-data {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
        &::-webkit-scrollbar {
          display: none;
        }

        .time-album-content-item {
          will-change: transform;
          transform: translateZ(0);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 30px;
          .item-left {
            width: 340px;
            margin-right: 30px;
            .timeline-date {
              .timeline-date-year {
                font-size: 30px;
                color: #333;
                font-weight: 600;
                width: 73px;
                background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411ad 80%);
                background-size: 70px 10px;
                background-repeat: no-repeat;
                background-position: left 20px;
              }
              .timeline-date-month {
                display: flex;
                align-items: center;
                .month {
                  color: #333;
                  font-size: 24px;
                  font-style: normal;
                  font-weight: 500;
                }
                .day {
                  color: #333;
                  font-size: 34px;
                  font-weight: 600;
                }
                .mark-box {
                  margin-left: 20px;
                  font-size: 24px;
                  font-weight: 500;
                  padding: 3px 16px;
                  color: #fff;
                  border-radius: 16px 8px;
                  background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
                }
              }
            }
            .timeline-title {
              color: #333;
              font-size: 32px;
              font-weight: 500;
              line-height: 48px;
              margin-bottom: 4px;
            }
            .timeline-content {
              color: #333;
              font-size: 26px;
              font-weight: 400;
              line-height: 38px;
              margin-bottom: 4px;
            }
            .timeline-record-person {
              color: #999;
              font-size: 24px;
              font-weight: 400;
              line-height: 38px;
            }
          }
          .item-right {
            display: flex;
            align-items: center;
            flex: 1;
            position: relative;
            height: 258px;
            overflow: hidden;
            .img-box-wrap {
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              // border: 1px solid red;
              width: 256px;
              height: 258px;
              display: flex;
              align-items: center;
              justify-content: center;
              &.single-img {
                width: 200px;
              }
            }
            .item-right-img-box {
              position: absolute;
              top: 0;
              left: 0;
              background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/2aa66c05-b8e0-413f-8c86-f7c2e5db120c.png');
              background-size: cover;
              background-position: center;
              width: 200px;
              height: 258px;
              z-index: 5;

              &:nth-child(2) {
                transform: scale(0.85) rotate(20deg);
                transform-origin: bottom;
                z-index: 4;
                opacity: 0.7;
                top: -30px;
              }
              .item-right-img {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 176px;
                height: 234px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                img {
                  width: 100%;
                  height: auto;
                }
              }
              .item-right-img-play-btn {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png');
                background-size: cover;
                background-position: center;
              }
              .img-nums {
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                bottom: 0px;
                right: 0px;
                color: #fff;
                font-size: 24px;
                font-weight: 500;
                width: 70px;
                height: 40px;
                border-radius: 16px 8px 0px 8px;
                background: rgba(0, 0, 0, 0.47);
              }
            }
          }
        }
        .time-album-content-item.first {
          padding-top: 6px;
        }
        .time-album-content-item.even {
          .item-left {
            margin-right: 0;
          }
          .item-right {
            margin-right: 18px;
          }
        }
        .time-album-content-item.odd {
          .item-left {
            margin-right: 70px;
          }
          .item-right {
            margin-right: 0;
          }
        }
        .content-item-line {
          &.odd {
            width: 188px;
            height: 90px;
            background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/1b35dcad-54f6-419c-adcb-d4b402e9b4a9.png');
            background-size: 100% 100%;
            background-position: center;
            margin-left: 160px;
            margin-top: -20px;
          }
          &.even {
            width: 180px;
            height: 90px;
            background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/80d07945-c4ac-4f1b-84fe-ffa038b24414.png');
            background-size: 100% 100%;
            margin-left: 190px;
            margin-top: 10px;
          }
        }
      }
      .scroll-down-arrow {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 18px;
        background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/8a4bd43d-d69c-4a73-80b1-7dc47e969ce5.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 10;
        animation: arrow-bounce 2s ease-in-out infinite;
        cursor: pointer;

        &:after {
          content: '';
          position: absolute;
          top: -10px;
          left: -10px;
          right: -10px;
          bottom: -10px;
          background: radial-gradient(
            circle,
            rgba(255, 132, 17, 0.2) 0%,
            rgba(255, 255, 255, 0) 70%
          );
          border-radius: 50%;
          z-index: -1;
          opacity: 0;
          animation: pulse-effect 2s ease-in-out infinite;
        }
      }
      .empty-animation-container {
        padding: 40px 0;
        text-align: center;
        span {
          display: block;
          color: #999;
          font-size: 28px;
          font-weight: 400;
          margin-top: 20px;
        }
      }

      .loading-container {
        padding: 20px 0;
        text-align: center;
        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;

          .bounce1,
          .bounce2,
          .bounce3 {
            width: 16px;
            height: 16px;
            background-color: #ff8411;
            border-radius: 100%;
            display: inline-block;
            margin: 0 4px;
            animation: sk-bouncedelay 1.4s infinite ease-in-out both;
          }

          .bounce1 {
            animation-delay: -0.32s;
          }

          .bounce2 {
            animation-delay: -0.16s;
          }
        }

        .loading-text {
          color: #ff8411;
          font-size: 24px;
          font-weight: 400;
        }

        &.top-loading {
          padding: 10px 0;
          position: sticky;
          top: 0;
          z-index: 5;
          background-color: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(5px);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          border-radius: 0 0 15px 15px;
          margin-bottom: 10px;

          .loading-spinner {
            margin-bottom: 0;
          }

          .loading-text {
            font-size: 20px;
            margin-left: 10px;
          }
        }
      }
    }
  }
  .flag-flow {
    position: absolute;
    top: 120px;
    right: 0;
    width: 168px;
    height: 224px;
    background-image: url('/static/imgage/flagFlow/00000.png');
    background-size: auto 100%;
    background-position: center;
    animation: flag-flow 2.25s steps(1) infinite;
  }
  .bg-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 136px;
    .bg-bottom-item {
      position: absolute;

      width: 750px;
      background-size: cover;
      background-position: center;
    }
    .bg1 {
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/f0224013-518c-48c4-95ea-0cacbb01f198.png');
      height: 136px;
      z-index: 1;
      left: 0;
      bottom: 0;
    }
    .bg2 {
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/db7c2f3f-5c3d-4429-b56c-165b217d6c75.png');
      height: 117px;
      z-index: 2;
      left: 0;
      bottom: 0;
    }
    .bg3 {
      width: 450px;
      height: 236px;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/9a17bbe9-ad65-4f5a-bd13-cdb6adbbe1ca.png');
      z-index: 3;
    }
    //左侧的花束
    .bg4 {
      width: 138px;
      height: 260px;
      bottom: 6px;
      left: -10px;
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/307ca3f0-1f71-4c36-907d-ed875c3c629a.png');
      z-index: 3;
      transform-origin: bottom center;
      animation: flower-flow 5s linear infinite;
      will-change: transform;
    }
    //右侧的花束
    .bg5 {
      width: 125px;
      height: 320px;
      right: 0;
      bottom: 0px;
      background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/787e7146-82e1-4a05-82ea-57d0f9f48b03.png');
      z-index: 3;
      transform-origin: bottom center;
      animation: flower-flow-right 7s linear infinite;
      will-change: transform;
    }
  }
  /deep/ .date-picker-container {
    .van-popup {
      border-radius: 22px 22px 0 0;
      .van-picker {
        .van-picker__columns {
          padding: 0 200px;
        }
        .van-picker__confirm {
          color: #333;
        }
      }
    }
  }
  .edit-btn {
    position: absolute;
    bottom: 40px;
    right: 10px;
    z-index: 10;
  }
}
// 动画 花束随风飘动
@keyframes flower-flow {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(8deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

// 右侧花朵顺时针摆动
@keyframes flower-flow-right {
  0% {
    transform: translate(0, 0) rotate(0);
  }
  20% {
    transform: translate(2px, -2px) rotate(2deg);
  }
  40% {
    transform: translate(4px, -4px) rotate(4deg);
  }
  60% {
    transform: translate(3px, -3px) rotate(3deg);
  }
  80% {
    transform: translate(2px, -2px) rotate(2deg);
  }
  100% {
    transform: translate(0, 0) rotate(0);
  }
}

// 旗帜飘动序列帧动画，优化为15帧
@keyframes flag-flow {
  0% {
    background-image: url('/static/imgage/flagFlow/00000.png');
  }
  7.14% {
    background-image: url('/static/imgage/flagFlow/00003.png');
  }
  14.28% {
    background-image: url('/static/imgage/flagFlow/00006.png');
  }
  21.42% {
    background-image: url('/static/imgage/flagFlow/00009.png');
  }
  28.56% {
    background-image: url('/static/imgage/flagFlow/00012.png');
  }
  35.7% {
    background-image: url('/static/imgage/flagFlow/00015.png');
  }
  42.84% {
    background-image: url('/static/imgage/flagFlow/00018.png');
  }
  49.98% {
    background-image: url('/static/imgage/flagFlow/00021.png');
  }
  57.12% {
    background-image: url('/static/imgage/flagFlow/00024.png');
  }
  64.26% {
    background-image: url('/static/imgage/flagFlow/00027.png');
  }
  71.4% {
    background-image: url('/static/imgage/flagFlow/00030.png');
  }
  78.54% {
    background-image: url('/static/imgage/flagFlow/00033.png');
  }
  85.68% {
    background-image: url('/static/imgage/flagFlow/00036.png');
  }
  92.82% {
    background-image: url('/static/imgage/flagFlow/00039.png');
  }
  100% {
    background-image: url('/static/imgage/flagFlow/00042.png');
  }
}

// 扫光动画效果
@keyframes shine-effect {
  0% {
    left: -100%;
  }
  30% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

// 箭头弹跳动画
@keyframes arrow-bounce {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 0.5;
  }
}

// 下拉箭头上下弹跳动画
@keyframes bounce-up-down {
  0%,
  100% {
    transform: rotate(180deg) translateY(0);
  }
  50% {
    transform: rotate(180deg) translateY(-5px);
  }
}

// 下拉提示淡入淡出动画
@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

// 脉冲效果
@keyframes pulse-effect {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
