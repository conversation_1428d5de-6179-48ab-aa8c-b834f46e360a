<template>
  <div class="time-album-container">
    <div class="time-album-header">
      <div class="time-album-header-left">
        <img :src="avatar" />
        <div class="time-album-header-left-title">{{ student_name }}</div>
      </div>
      <div @click="datePickerShow = true" class="time-album-header-right">
        <div class="time-album-header-right-item">
          <img class="icon" src="@/assets/calar.png" alt="logo" />
          <span>{{ currentDateText }}</span>
        </div>
      </div>
      <!-- 路线背景 -->
      <div class="time-album-header-bg"></div>
    </div>
    <!-- 时光相册内容区域 -->
    <div class="time-album-content-wrapper">
      <div class="title-bg">
        <img
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/72679394-5024-44b6-82b1-8defc5c7f68e.png"
          alt=""
        />
      </div>
      <div class="time-album-content">
        <div
          :class="{ 'no-data': list.length === 0 }"
          class="time-album-content-item-wrapper"
          ref="scrollContainer"
          :style="pullStyle"
        >
          <!-- 顶部加载动画 -->
          <!-- <div class="loading-container top-loading" v-if="isLoadingUp && list.length > 0">
            <div class="loading-spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
            <span class="loading-text">加载中...</span>
          </div> -->

          <div v-for="(item, index) in list" :key="item.id">
            <div
              v-if="(index + 1) % 2 === 1"
              :class="['time-album-content-item', index === 0 ? 'first' : '']"
            >
              <div @click="handleItemClick(item.id)" class="item-left">
                <div class="timeline-date">
                  <div class="timeline-date-year">{{ item.date.split('-')[0] }}</div>
                  <div class="timeline-date-month">
                    <span class="month">{{ item.date.split('-')[1] }}月</span>
                    <span class="day">{{ item.date.split('-')[2] }}</span>
                    <span class="mark-box">{{ item.mark_name }}</span>
                  </div>
                </div>
                <div class="ellipsis timeline-title">{{ item.title }}</div>
                <div class="ellipsis-2 timeline-content">
                  {{ item.content }}
                </div>
                <div class="ellipsis timeline-record-person">
                  记录人：{{ item.record_person || '自动生成' }}
                </div>
              </div>
              <div @click="handleImgBoxClick($event, index)" class="item-right">
                <div class="img-box-wrap" :class="{ 'single-img': item.media_list.length === 1 }">
                  <div v-if="item.media_list.length" class="item-right-img-box">
                    <div class="item-right-img">
                      <img
                        v-if="item.media_list[0].video_url"
                        :src="
                          item.media_list[0].image_url ||
                          item.media_list[0].video_url + imageProcess
                        "
                      />
                      <img v-else :src="item.media_list[0].image_url" />
                      <div
                        v-if="item.media_list[0].video_url"
                        class="item-right-img-play-btn"
                      ></div>
                      <span class="img-nums">{{ item.media_list.length }}项</span>
                    </div>
                  </div>
                  <div v-if="item.media_list.length > 1" class="item-right-img-box">
                    <div class="item-right-img">
                      <img
                        v-if="item.media_list[1].video_url"
                        :src="
                          item.media_list[1].image_url ||
                          item.media_list[1].video_url + imageProcess
                        "
                      />
                      <img v-else :src="item.media_list[1].image_url" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="(index + 1) % 2 === 0" class="time-album-content-item even">
              <div @click="handleImgBoxClick($event, index)" class="item-right">
                <div class="img-box-wrap" :class="{ 'single-img': item.media_list.length === 1 }">
                  <div v-if="item.media_list.length" class="item-right-img-box">
                    <div class="item-right-img">
                      <img
                        v-if="item.media_list[0].video_url"
                        :src="
                          item.media_list[0].image_url ||
                          item.media_list[0].video_url + imageProcess
                        "
                        alt=""
                      />
                      <img v-else :src="item.media_list[0].image_url" />
                      <div
                        v-if="item.media_list[0].video_url"
                        class="item-right-img-play-btn"
                      ></div>
                      <span class="img-nums">{{ item.media_list.length }}项</span>
                    </div>
                  </div>
                  <div v-if="item.media_list.length > 1" class="item-right-img-box">
                    <div class="item-right-img">
                      <img
                        v-if="item.media_list[1].video_url"
                        :src="
                          item.media_list[1].image_url ||
                          item.media_list[1].video_url + imageProcess
                        "
                        alt=""
                      />
                      <img v-else :src="item.media_list[1].image_url" />
                    </div>
                  </div>
                </div>
              </div>
              <div @click="handleItemClick(item.id)" class="item-left">
                <div class="timeline-date">
                  <div class="timeline-date-year">{{ item.date.split('-')[0] }}</div>
                  <div class="timeline-date-month">
                    <span class="month">{{ item.date.split('-')[1] }}月</span>
                    <span class="day">{{ item.date.split('-')[2] }}</span>
                    <span class="mark-box">{{ item.mark_name }}</span>
                  </div>
                </div>
                <div class="ellipsis timeline-title">{{ item.title }}</div>
                <div class="ellipsis-2 timeline-content">
                  {{ item.content }}
                </div>
                <div class="ellipsis timeline-record-person">
                  记录人：{{ item.record_person || '自动生成' }}
                </div>
              </div>
            </div>

            <!-- 导航路线指示图 -->
            <div
              v-if="index !== list.length - 1"
              :class="['content-item-line', index % 2 === 0 ? 'odd' : 'even']"
            ></div>
          </div>

          <div class="empty-animation-container">
            <empty-animation />
            <span>每一步成长都值得珍藏～</span>
          </div>

          <!-- 加载中动画 - 只有当加载更多数据或初始化时显示 -->
          <div class="loading-container" v-if="loading && (!isLoadingUp || list.length === 0)">
            <div class="loading-spinner">
              <div class="bounce1"></div>
              <div class="bounce2"></div>
              <div class="bounce3"></div>
            </div>
            <span class="loading-text">加载中...</span>
          </div>
        </div>
        <!-- 向下滑动指示箭头 -->
        <div class="scroll-down-arrow" v-if="list.length > 3 && showScrollArrow"></div>
      </div>
    </div>
    <!-- 旗帜飘动 -->
    <div v-if="flagFlowShow" class="flag-flow"></div>
    <!-- 底部装饰背景 -->
    <div class="bg-bottom">
      <div class="bg-bottom-item bg1"></div>
      <div class="bg-bottom-item bg2"></div>
      <div class="bg-bottom-item bg3"></div>
      <div class="bg-bottom-item bg4"></div>
      <div v-if="from === '2'" class="bg-bottom-item bg5"></div>
    </div>

    <!-- 添加相册浏览组件 -->
    <album-swiper
      :visible.sync="albumSwiperVisible"
      :media-list="albumMediaList"
      :initial-index="initialMediaIndex"
      :origin-position="originPosition"
    />
    <div class="date-picker-container">
      <van-popup v-model="datePickerShow" position="bottom">
        <van-picker
          title="请选择记录日期"
          show-toolbar
          :columns="pickerColumns"
          :loading="pickerLoading"
          @confirm="handleDateConfirm"
          @cancel="handleDateCancel"
        />
      </van-popup>
    </div>
    <!-- 编辑按钮 -->
    <div v-if="from === '1'" @click="handleEditBtnClick" class="edit-btn">
      <img
        width="70"
        height="70"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/693f6c52-a9fe-4b38-9690-8c7768f50e7e.png"
        alt="发布/编辑"
      />
    </div>
  </div>
</template>

<script>
import { getUrlParams, formatDate, throttle } from '@/utils/util';
import { genders_list, album_type_list } from '@/utils/const';
import AlbumSwiper from './albumSwiper.vue';
import EmptyAnimation from './emptyAnimation.vue';
import { getTimeAlbumList, getTimeAlbumTimePointList } from '@/api/timeAlbum';
import { Toast } from 'vant';

export default {
  name: 'TimeAlbum',
  components: {
    AlbumSwiper,
    EmptyAnimation,
  },
  data() {
    return {
      currentDate: '',
      albumSwiperVisible: false,

      albumMediaList: [], // 当前显示的媒体列表
      initialMediaIndex: 0,
      originPosition: {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
      },
      datePickerShow: false,
      pickerColumns: [],
      pickerLoading: false,
      choose_head: '',
      stu_id: '',
      student_name: '',
      from: '',
      token: '',
      visitor: '',
      operation_id: '',
      list: [],
      flagFlowShow: false,
      imageProcess: '?x-oss-process=video/snapshot,t_2000,m_fast,w_320,ar_auto',
      loading: false,
      noMoreData: false,
      showScrollArrow: true,
      currentPage: 1,
      pageSize: 20,
      upPage: 1, // 顶部数据的页码
      downPage: 1, // 底部数据的页码
      isLoadingUp: false, // 是否正在向上加载数据
      lastScrollTop: 0, // 上次滚动位置，用于判断滚动方向
      scrollDirection: 'down', // 当前滚动方向：'up' or 'down'
      throttledHandleScroll: null, // 用于存储节流后的函数
      // 下拉刷新相关
      startY: 0,
      currentY: 0,
      isPulling: false,
      pullDistance: 0,
      maxPullDistance: 100,
      refreshThreshold: 60,
      refreshing: false,
      showRefreshTip: false,
    };
  },
  created() {
    this.preloadImage();
    // 创建节流后的滚动处理函数
    this.throttledHandleScroll = throttle(this.handleScroll, 200);
  },
  mounted() {
    this.init();
    // 使用节流处理滚动事件
    this.$refs.scrollContainer.addEventListener('scroll', this.throttledHandleScroll);

    // 添加触摸事件监听器实现下拉刷新
    this.$refs.scrollContainer.addEventListener('touchstart', this.handleTouchStart, {
      passive: false,
    });
    this.$refs.scrollContainer.addEventListener('touchmove', this.handleTouchMove, {
      passive: false,
    });
    this.$refs.scrollContainer.addEventListener('touchend', this.handleTouchEnd, {
      passive: false,
    });
    //监听小程序发来的消息
    // window.addEventListener('message', function (e) {
    //   alert('Receivedmessage:', e.data);
    //   console.log('Receivedmessage:', e.data);
    // });

    // 检查是否有更早的数据并提示用户
    // this.checkEarlierData();
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听，防止内存泄漏
    if (this.$refs.scrollContainer) {
      this.$refs.scrollContainer.removeEventListener('scroll', this.throttledHandleScroll);
      this.$refs.scrollContainer.removeEventListener('touchstart', this.handleTouchStart);
      this.$refs.scrollContainer.removeEventListener('touchmove', this.handleTouchMove);
      this.$refs.scrollContainer.removeEventListener('touchend', this.handleTouchEnd);
    }
  },
  computed: {
    avatar() {
      if (this.choose_head) {
        return genders_list[+this.choose_head];
      }
      return genders_list[1];
    },
    currentDateText() {
      return formatDate(this.currentDate, 'YYYY.MM');
    },
    // 计算下拉距离转换为的样式
    pullStyle() {
      if (!this.isPulling) return {};
      const translateY = Math.min(this.pullDistance * 0.6, 80);
      return {
        transform: `translateY(${translateY}px)`,
        transition: this.isPulling ? 'none' : 'transform 0.3s ease',
      };
    },
  },
  methods: {
    // 预加载旗帜帧图片
    preloadImage() {
      const images = [
        '/static/imgage/flagFlow/00000.png',
        '/static/imgage/flagFlow/00003.png',
        '/static/imgage/flagFlow/00006.png',
        '/static/imgage/flagFlow/00009.png',
        '/static/imgage/flagFlow/00012.png',
        '/static/imgage/flagFlow/00015.png',
        '/static/imgage/flagFlow/00018.png',
        '/static/imgage/flagFlow/00021.png',
        '/static/imgage/flagFlow/00024.png',
        '/static/imgage/flagFlow/00027.png',
        '/static/imgage/flagFlow/00030.png',
        '/static/imgage/flagFlow/00033.png',
        '/static/imgage/flagFlow/00036.png',
        '/static/imgage/flagFlow/00039.png',
        '/static/imgage/flagFlow/00042.png',
      ];
      let count = 0;
      const totalImages = images.length;

      const handleImageLoad = () => {
        count++;
        if (count === totalImages) {
          console.log('图片加载完成', count);
          this.flagFlowShow = true;
        }
      };

      images.forEach((item) => {
        const img = new Image();
        img.onload = handleImageLoad;
        img.onerror = () => {
          console.warn(`图片加载失败: ${item}`);
          handleImageLoad(); // 即使失败也计数，避免永远无法完成加载
        };
        img.src = item;
      });
    },
    async loadPickerData() {
      if (!this.stu_id) {
        return;
      }
      this.pickerLoading = true;
      const res = await getTimeAlbumTimePointList({
        student_id: this.stu_id,
        token: this.token,
        from: this.from,
        visitor: this.visitor,
        operation_id: this.operation_id,
      });
      this.pickerLoading = false;
      if (res.code === 0) {
        // 转换数据格式
        this.pickerColumns = res.data.map((item) => ({
          text: item.year,
          children: item.month.map((m) => ({
            text: m.padStart(2, '0'), // 确保月份是两位数
          })),
        }));
      }

      // setTimeout(() => {
      //   this.pickerLoading = false;
      //   this.pickerColumns = [
      //     {
      //       text: '2023',
      //       children: [{ text: '01' }, { text: '02' }, { text: '03' }],
      //     },
      //     {
      //       text: '2025',
      //       children: [{ text: '04' }, { text: '05' }, { text: '07' }],
      //     },
      //   ];
      // }, 2000);
    },
    init() {
      const token = getUrlParams('token');
      const choose_head = +getUrlParams('choose_head');
      const stu_id = getUrlParams('stu_id');
      const student_name = getUrlParams('student_name');
      const from = getUrlParams('from');
      const visitor = getUrlParams('visitor');
      const operation_id = getUrlParams('operation_id');
      this.choose_head = choose_head;
      this.stu_id = stu_id;
      this.from = from;
      this.student_name = student_name;
      this.token = token;
      this.visitor = visitor;
      this.operation_id = operation_id;

      console.table({ token, choose_head, stu_id, from, student_name });
      console.log('init');
      this.currentDate = formatDate(new Date(), 'YYYY-MM');
      // 初始使用参数2，表示加载底部数据
      console.log('初始化 - 加载底部数据');
      this.getList(false, 2);
      this.loadPickerData();
    },
    albumTypeFilter(type) {
      return album_type_list.find((item) => item.id === type)?.name;
    },
    // 获取时光相册列表
    // isLoadMore: 是否加载更多
    // up_down_status: 1 往下滑动查询顶部数据 2 往上滑动查询底部数据
    async getList(isLoadMore = false, up_down_status = 2) {
      // 如果不是加载更多，则重置分页参数
      if (!isLoadMore) {
        this.upPage = 1;
        this.downPage = 1;
        this.list = [];
        this.noMoreData = false;
        this.isLoadingUp = false; // 初始化时重置上拉加载状态
      }

      // 根据加载方向设置当前页码
      const currentPage = up_down_status === 1 ? this.upPage : this.downPage;
      console.log(`当前页码 - up_down_status: ${up_down_status}, currentPage: ${currentPage}`);

      // 如果已经在加载中或已经没有更多数据，则返回
      if (this.loading || (isLoadMore && up_down_status === 2 && this.noMoreData)) {
        return;
      }

      if (!this.stu_id) {
        Toast({
          message: '暂无数据',
          position: 'center',
          duration: 1500,
        });
        return;
      }
      // 设置对应方向的加载状态
      this.loading = true;

      // 获取当前月份的最后一天作为查询时间
      const currentMonthLastDay = this.getLastDayOfMonth(this.currentDate);
      // 为了看到加载效果延迟1.5s
      // await new Promise((resolve) => setTimeout(resolve, 300));

      const res = await getTimeAlbumList({
        student_id: this.stu_id,
        start_year_month: currentMonthLastDay,
        page: currentPage,
        page_size: this.pageSize,
        token: this.token,
        from: this.from,
        visitor: parseInt(this.visitor),
        operation_id: this.operation_id,
        up_down_status: up_down_status, // 1 往下滑动查询顶部数据 2 往上滑动查询底部数据
      });

      if (res.code === 0) {
        const arr = res?.data?.results?.map((item) => {
          return {
            id: item.id,
            title: item.album_title,
            content: item.album_values,
            record_person: item.final_employee_name,
            date: formatDate(item.event_time, 'YYYY-MM-DD'),
            mark_name: this.albumTypeFilter(item.album_type),
            media_list: item.album_picture_video,
          };
        });

        if (isLoadMore) {
          if (up_down_status === 1) {
            // 往下滑动查询顶部数据，追加数据到顶部
            // 记住当前滚动位置和高度
            const scrollContainer = this.$refs.scrollContainer;
            const oldScrollHeight = scrollContainer.scrollHeight;
            const oldScrollTop = scrollContainer.scrollTop;

            // 在顶部添加新数据
            this.list = [...(arr || []), ...this.list];

            // 如果有数据，增加上滑页码
            if (arr && arr.length > 0) {
              this.upPage += 1;
              // Toast('刷新成功');
              // 在DOM更新后调整滚动位置，保持视觉上的稳定
              this.$nextTick(() => {
                const newScrollHeight = scrollContainer.scrollHeight;
                const heightDiff = newScrollHeight - oldScrollHeight;
                scrollContainer.scrollTop = oldScrollTop + heightDiff;
              });
            } else {
              // Toast('没有更多数据了');
            }
          } else {
            // 往上滑动查询底部数据，追加数据到底部
            this.list = [...this.list, ...(arr || [])];
            // 判断是否还有更多数据
            this.noMoreData = !arr || arr.length < this.pageSize;
            // 如果有数据，增加下滑页码
            if (arr && arr.length > 0) {
              this.downPage += 1;
            }
          }
        } else {
          // 初始化数据
          this.list = arr || [];
          // 判断上下方向是否还有更多数据
          this.noMoreData = !arr || arr.length < this.pageSize;

          // 如果有数据，设置初始页码
          if (arr && arr.length > 0) {
            this.downPage = 2; // 下一次下滑加载从第2页开始
          }
        }
      } else {
        // 请求出错时也标记为有更多数据
        if (up_down_status === 1) {
          this.noMoreData = false;
        }
      }
      // 重置加载状态
      this.loading = false;
      if (up_down_status === 1) {
        this.isLoadingUp = false;
      }
    },

    // 获取指定月份的最后一天
    getLastDayOfMonth(dateStr) {
      const [year, month] = dateStr.split('-');
      // 创建下个月的第0天，即当前月的最后一天
      const date = new Date(parseInt(year), parseInt(month), 0);
      return formatDate(date, 'YYYY-MM-DD');
    },

    handleEditBtnClick() {
      console.log('编辑');
      wx.miniProgram.navigateTo({
        url: `/pages/student/subpages/timeAlbum/edit?from=${this.from}&choose_head=${this.choose_head}&stu_id=${this.stu_id}&student_name=${this.student_name}&type=create`,
      });
    },
    // 处理日期选择确认
    handleDateConfirm(value) {
      console.log(value);
      // 如果选择了新的日期
      const newDate = value.join('-');
      // if (newDate !== this.currentDate) {
      this.currentDate = newDate;
      this.upPage = 1;
      this.downPage = 1;
      this.list = [];
      this.noMoreData = false;
      console.log('日期切换 - 加载底部数据');
      this.getList(false, 2); // 切换日期后，使用参数2加载底部数据
      // }
      this.handleDateCancel();
    },
    handleDateCancel() {
      this.datePickerShow = false;
    },

    handleItemClick(id) {
      console.log(id);
      const { avatar } = this;
      wx.miniProgram.navigateTo({
        url: `/pages/student/subpages/timeAlbum/detail?id=${id}&avatar=${avatar}&from=${this.from}&stu_id=${this.stu_id}&student_name=${this.student_name}&visitor=${this.visitor}&operation_id=${this.operation_id}`,
      });
    },
    // 为所有.item-right元素添加点击事件
    // addItemClickEvent() {
    //   console.log('添加点击事件');
    //   this.$nextTick(() => {
    //     const imgBoxes = document.querySelectorAll('.item-right');
    //     imgBoxes.forEach((box, index) => {
    //       box.addEventListener('click', (e) => {
    //         this.handleImgBoxClick(e, index);
    //       });
    //     });
    //   });
    // },

    // 处理图片盒子点击事件
    handleImgBoxClick(e, index) {
      // 阻止事件冒泡
      e.stopPropagation();

      // 设置为屏幕中心位置
      this.originPosition = {
        x: 0, // 屏幕中心
        y: 0, // 屏幕中心
        width: 100, // 小一点的初始大小
        height: 100,
      };

      // 根据点击的区域索引获取对应的媒体列表
      this.albumMediaList = this.list[index].media_list || [];

      // 设置初始索引
      this.initialMediaIndex = 0; // 每次打开从第一张开始

      // 显示弹窗
      this.albumSwiperVisible = true;
    },
    handleScroll(e) {
      const container = e.target;
      const scrollTop = container.scrollTop;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const scrollThreshold = 200; // 底部滚动阈值，增加敏感度
      const topThreshold = 200; // 顶部滚动阈值，增加敏感度

      // 判断滚动方向
      if (scrollTop < this.lastScrollTop) {
        this.scrollDirection = 'up';
        Toast.clear();
      } else if (scrollTop > this.lastScrollTop) {
        this.scrollDirection = 'down';
      }
      this.lastScrollTop = scrollTop;

      // 修改箭头显示逻辑：只有接近底部时才隐藏箭头
      this.showScrollArrow = scrollTop + clientHeight < scrollHeight - 150;

      // console.log(
      //   `滚动信息: 当前位置=${scrollTop.toFixed(0)}/${scrollHeight.toFixed(0)}, 方向=${
      //     this.scrollDirection
      //   }, 底部距离=${(scrollHeight - scrollTop - clientHeight).toFixed(0)}`,
      // );

      // 检测是否接近底部，加载更多底部数据 (up_down_status=2)
      if (
        scrollTop + clientHeight >= scrollHeight - scrollThreshold &&
        this.scrollDirection === 'down'
      ) {
        console.log('已滚动到底部附近');
        if (!this.loading && !this.noMoreData) {
          console.log('触发底部加载，up_down_status=2');
          this.getList(true, 2); // 参数2表示加载底部数据
        } else if (this.noMoreData) {
          Toast({
            message: '没有更多数据了',
            position: 'bottom',
            duration: 1500,
          });
        }
      }

      // 检测是否接近顶部，加载更多顶部数据 (up_down_status=1)
      if (scrollTop <= topThreshold && this.scrollDirection === 'up') {
        console.log('已滚动到顶部附近');
        if (!this.isLoadingUp && !this.loading) {
          console.log('触发顶部加载，up_down_status=1');
          this.getList(true, 1); // 参数1表示加载顶部数据
        } else {
          console.log(
            '未触发顶部加载，原因:',
            this.isLoadingUp ? 'isLoadingUp=true' : this.loading ? 'loading=true' : '未知原因',
          );
        }
      }
    },

    // 触摸事件处理 - 开始
    handleTouchStart(e) {
      const scrollTop = this.$refs.scrollContainer.scrollTop;

      // 只有在滚动区域顶部才启用下拉刷新
      if (scrollTop <= 0) {
        this.startY = e.touches[0].clientY;
        this.isPulling = true;
      }
    },

    // 触摸事件处理 - 移动
    handleTouchMove(e) {
      if (!this.isPulling || this.refreshing) return;

      const scrollTop = this.$refs.scrollContainer.scrollTop;
      if (scrollTop > 0) {
        this.isPulling = false;
        this.pullDistance = 0;
        return;
      }

      // 计算下拉距离
      this.currentY = e.touches[0].clientY;
      const pullDistance = this.currentY - this.startY;

      // 只处理下拉，不处理上拉
      if (pullDistance <= 0) {
        this.isPulling = false;
        this.pullDistance = 0;
        return;
      }

      // 添加阻尼效果，让下拉变得更有弹性
      this.pullDistance = Math.pow(pullDistance, 0.8);

      // 超过阈值时阻止默认滚动行为
      if (this.pullDistance > 10) {
        e.preventDefault();
      }
    },

    // 触摸事件处理 - 结束
    handleTouchEnd() {
      if (!this.isPulling) return;

      // 如果下拉距离超过阈值，触发刷新
      if (this.pullDistance >= this.refreshThreshold) {
        this.refreshing = true;

        // 执行数据加载
        this.loadTopData();
      }

      // 重置下拉状态
      this.isPulling = false;
      this.pullDistance = 0;
    },

    // 加载顶部数据
    async loadTopData() {
      if (this.isLoadingUp) return;

      // 先将刷新状态置为true，显示加载动画
      this.refreshing = true;

      try {
        // 调用加载顶部数据的API
        await this.getList(true, 1);
      } catch (err) {
        console.error('加载数据失败:', err);
        Toast('刷新失败，请重试');
      } finally {
        // 无论成功失败，都重置刷新状态
        this.refreshing = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
