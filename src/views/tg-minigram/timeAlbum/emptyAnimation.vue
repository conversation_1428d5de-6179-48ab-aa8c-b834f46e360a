<template>
  <div>
    <div class="empty-animation-container">
      <div class="node node-1"></div>
      <div class="node node-2"></div>
      <div class="node node-3"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyAnimation',
};
</script>

<style lang="less" scoped>
.empty-animation-container {
  margin: 0 auto;
  width: 302px;
  height: 228px;
  position: relative;
  .node {
    margin-top: 0 auto;
    position: absolute;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
  }
  .node-1 {
    left: 0;
    bottom: 0;
    width: 210px;
    height: 174px;
    background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/b07e706b-2fc7-4bf6-8d1b-af350c7494d0.png');
    z-index: 1;
  }
  /**小猫手臂节点 */
  .node-2 {
    left: 60px;
    top: 58px;
    width: 96px;
    height: 96px;
    background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/8534da2e-5950-40ea-9b01-7b8a95d52409.png');
    z-index: 2;
  }
  .node-3 {
    right: 0;
    bottom: 0;
    width: 184px;
    height: 226px;
    background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/ac137ee5-0333-4b90-aa39-7914ef47ec9f.png');
    z-index: 3;
  }
  /**小猫手臂旋转晃动动画 */
  @keyframes shake {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(-8deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
  .node-2 {
    transform-origin: 90% 90%;
    animation: shake 2s ease-in-out infinite;
  }
}
</style>
