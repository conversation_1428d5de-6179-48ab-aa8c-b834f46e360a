<template>
  <transition name="album-swiper-fade">
    <div v-if="visible" class="album-swiper-container" @click.self="handleClose">
      <div class="album-swiper-backdrop"></div>
      <div class="album-swiper-content" :style="contentAnimationStyle" ref="swiperContent">
        <div class="swiper-wrapper" ref="swiperWrapper">
          <!-- 左侧控制按钮 -->
          <!-- <div class="swiper-nav-button prev" @click="prev" v-if="mediaList.length > 1">
            <div class="arrow-left"></div>
          </div> -->

          <!-- 轮播项目 -->
          <!-- :poster="currentMedia.image_url || currentMedia.video_url + imageProcess" -->
          <div class="swiper-items-container">
            <transition :name="transitionName">
              <div class="swiper-item" :key="currentIndex">
                <!-- <video
                  style="width: 100%"
                  v-if="currentMedia.video_url"
                  :src="currentMedia.video_url"
                  :poster="currentMedia.video_url + imageProcess"
                  autoplay
                  class="video-content"
                ></video> -->
                <div v-if="currentMedia.video_url" id="myAlbumPlayer"></div>
                <div v-else>
                  <div v-for="(item, index) in mediaList" :key="index" class="image-list">
                    <img
                      v-show="index === currentIndex"
                      v-if="item.image_url"
                      :src="item.image_url"
                      alt="相册图片"
                      preview="image"
                      class="image-content"
                    />
                  </div>
                </div>
              </div>
            </transition>
          </div>

          <!-- 右侧控制按钮 -->
          <!-- <div class="swiper-nav-button next" @click="next" v-if="mediaList.length > 1">
            <div class="arrow-right"></div>
          </div> -->
        </div>

        <!-- 指示器 -->
        <div class="swiper-indicators" v-if="mediaList.length > 1">
          <div
            v-for="(item, index) in mediaList"
            :key="index"
            class="indicator-dot"
            :class="{ active: currentIndex === index }"
            @click="goToSlide(index)"
          ></div>
        </div>

        <!-- 关闭按钮 -->
        <div class="swiper-close-btn" @click="handleClose">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/4e53eaf2-d5b5-49f0-80b1-a54b89323374.png"
            alt="关闭"
          />
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import Hammer from 'hammerjs';

export default {
  name: 'AlbumSwiper',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 媒体列表
    mediaList: {
      type: Array,
      default: () => [],
    },
    // 初始索引
    initialIndex: {
      type: Number,
      default: 0,
    },
    // 缩放时初始位置
    originPosition: {
      type: Object,
      default: () => ({ x: 0, y: 0, width: 0, height: 0 }),
    },
    // 是否循环切换
    loop: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentIndex: 0,
      hammer: null,
      contentAnimationStyle: {},
      transitionName: 'slide-left', // 默认向左滑动
      imageProcess: '?x-oss-process=video/snapshot,t_2000,m_fast,w_320,ar_auto',
    };
  },
  computed: {
    // 获取当前显示的媒体对象
    currentMedia() {
      console.log(this.mediaList);
      return this.mediaList[this.currentIndex] || {};
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.currentIndex = this.initialIndex;
        this.$nextTick(() => {
          if (this.originPosition.width) {
            this.animateOpen();
          }
          this.initXGPlayer();
          this.initHammer();
        });
      } else {
        // 关闭时暂停所有视频
        // this.pauseAllVideos();
      }
    },
    initialIndex(newVal) {
      this.currentIndex = newVal;
    },
    currentIndex() {
      // 索引变化时暂停所有视频
      this.$nextTick(() => {
        this.$previewRefresh();
        this.pauseAllVideos();
        this.initXGPlayer();
      });
    },
  },
  mounted() {},
  methods: {
    initXGPlayer() {
      this.player = new window.Player({
        id: 'myAlbumPlayer',
        url: this.currentMedia.video_url,
        poster: this.currentMedia.video_url + this.imageProcess,
        width: '100%',
        height: '100%',
        autoplay: true,
        autoplayMuted: true,
        controls: true,
        fluid: true,
        loop: true,
        muted: true,
        // 'x5-video-player-type': 'h5',
      });
    },
    initHammer() {
      if (this.hammer) {
        this.hammer.destroy();
      }

      const swiperWrapper = this.$refs.swiperWrapper;
      if (!swiperWrapper) return;

      this.hammer = new Hammer(swiperWrapper);
      this.hammer.get('swipe').set({ direction: Hammer.DIRECTION_HORIZONTAL });

      this.hammer.on('swipeleft', () => {
        this.next();
      });

      this.hammer.on('swiperight', () => {
        this.prev();
      });
    },

    animateOpen() {
      const swiperContent = this.$refs.swiperContent;

      if (!swiperContent) return;

      // 初始状态 - 从中心缩小
      this.contentAnimationStyle = {
        transform: 'scale(0.2)',
        opacity: 0,
      };

      // 强制重绘
      swiperContent.offsetHeight;

      // 过渡到最终状态
      setTimeout(() => {
        this.contentAnimationStyle = {
          transform: 'scale(1)',
          opacity: 1,
        };
      }, 20);
    },

    next() {
      if (this.mediaList.length <= 1) return;

      this.transitionName = 'slide-left';

      if (this.currentIndex < this.mediaList.length - 1) {
        this.currentIndex++;
      } else if (this.loop) {
        // 循环到第一张
        this.currentIndex = 0;
      }
    },

    prev() {
      if (this.mediaList.length <= 1) return;

      this.transitionName = 'slide-right';

      if (this.currentIndex > 0) {
        this.currentIndex--;
      } else if (this.loop) {
        // 循环到最后一张
        this.currentIndex = this.mediaList.length - 1;
      }
    },

    goToSlide(index) {
      if (index === this.currentIndex || this.mediaList.length <= 1) return;

      // 根据目标索引与当前索引的关系确定过渡动画方向
      this.transitionName = index > this.currentIndex ? 'slide-left' : 'slide-right';
      this.currentIndex = index;
    },

    handleClose() {
      // 关闭前暂停所有视频
      this.pauseAllVideos();

      const swiperContent = this.$refs.swiperContent;

      if (swiperContent) {
        // 添加关闭类，使其在关闭动画期间不影响交互
        swiperContent.classList.add('closing');

        this.contentAnimationStyle = {
          transform: 'scale(0.2)',
          opacity: 0,
        };

        setTimeout(() => {
          this.$emit('update:visible', false);
          this.contentAnimationStyle = {};
          // 移除关闭类
          if (swiperContent) {
            swiperContent.classList.remove('closing');
          }
        }, 300);
      } else {
        this.$emit('update:visible', false);
      }

      if (this.hammer) {
        this.hammer.destroy();
        this.hammer = null;
      }
    },

    // 暂停所有视频
    pauseAllVideos() {
      if (this.$refs.swiperWrapper) {
        const videos = this.$refs.swiperWrapper.querySelectorAll('video');
        videos.forEach((video) => {
          if (!video.paused) {
            video.pause();
          }
        });
      }
    },
  },
  beforeDestroy() {
    // 组件销毁前暂停所有视频
    this.pauseAllVideos();

    if (this.hammer) {
      this.hammer.destroy();
      this.hammer = null;
    }

    if (this.player) {
      this.player.destroy();
      this.player = null;
    }
  },
};
</script>

<style lang="less" scoped>
.album-swiper-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .album-swiper-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
  }

  .album-swiper-content {
    position: relative;
    width: 600px;
    height: 70vh;
    z-index: 1000;
    margin-bottom: 10vh;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease;

    &.closing {
      pointer-events: none;
    }

    .swiper-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: 12px;
      background-color: #fff;
      display: flex;
      align-items: center;
    }

    .swiper-items-container {
      flex: 1;
      height: 100%;
      position: relative;
      overflow: hidden;
    }

    .swiper-item {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .image-content {
        object-fit: cover;
        max-height: 100%;
        max-width: 100%;
      }

      .video-content {
        width: 80vw;
        max-height: 100%;
        object-fit: contain;
      }
    }

    .swiper-nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 10;

      &.prev {
        left: 10px;
      }

      &.next {
        right: 10px;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.8);
      }

      .arrow-left,
      .arrow-right {
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
      }

      .arrow-left {
        border-right: 12px solid #333;
        margin-right: 4px;
      }

      .arrow-right {
        border-left: 12px solid #333;
        margin-left: 4px;
      }
    }

    .swiper-indicators {
      position: absolute;
      bottom: -24px;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .indicator-dot {
        width: 10px;
        height: 10px;
        background-color: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        margin: 0 6px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          width: 20px;
          background-color: #ffffff;
          border-radius: 5px;
        }
      }
    }

    .swiper-close-btn {
      position: absolute;
      bottom: -160px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 60px;
        height: 60px;
      }
    }
  }
}

// 过渡动画
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
  position: absolute;
  width: 100%;
  height: 100%;
}

// 向左滑动效果（下一张）
.slide-left-enter {
  transform: translateX(100%);
}
.slide-left-leave-to {
  transform: translateX(-100%);
}

// 向右滑动效果（上一张）
.slide-right-enter {
  transform: translateX(-100%);
}
.slide-right-leave-to {
  transform: translateX(100%);
}

// 整体淡入淡出
.album-swiper-fade-enter-active,
.album-swiper-fade-leave-active {
  transition: opacity 0.3s;
}

.album-swiper-fade-enter,
.album-swiper-fade-leave-to {
  opacity: 0;
}
</style>
