<template>
  <div>
    <div :style="{ backgroundImage: `url(${bgImgUrl})` }" class="share-card-container"></div>
    <div class="share-card-dialog">
      <div ref="shareCard" class="share-card">
        <div class="img-container" :class="{ small: isSmall }">
          <van-image width="100%" height="100%" :src="bgImgUrl" fit="cover" />
          <img
            class="top-img"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/727bcae6-85b7-40e1-aafe-c41bb6d35bb1.png"
          />
        </div>
        <div class="share-card-content">
          <div class="share-card-title">
            <img class="avatar-img" :src="avatar || defaultAvatar" alt="" />
            <span class="title">{{ info.album_title }}</span>
            <span class="tag">{{ info.album_type }}</span>
          </div>
          <div class="share-card-desc">
            <span class="desc ellipsis-2">{{ info.album_values }}</span>
          </div>
          <div class="share-card-time">
            <span>记录人：{{ info.final_employee_name || '自动生成' }}</span>
            <span>{{ info.event_time }}</span>
          </div>
        </div>
        <div class="line"></div>

        <div class="minigram-mark">
          <span>聂道少儿围棋，只为智慧成长</span>
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c5927512-25dd-47f5-83ef-4b8b6183e8a6.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div v-if="imgUrl" class="share-card-img">
      <img :src="imgUrl" />
      <div class="save-img">长按图片保存到相册</div>
    </div>
  </div>
</template>

<script>
import { getUrlParams } from '@/utils/util';
import { Toast } from 'vant';
import { getTimeAlbumDetail } from '@/api/timeAlbum';
import { album_type_list } from '@/utils/const';
export default {
  name: 'ShareCard',
  data() {
    return {
      bgImgUrl: '',
      imgUrl: '',
      index: 0,
      token: '',
      avatar: '',
      visitor: '',
      operation_id: '',
      defaultAvatar:
        'https://tg-prod.oss-cn-beijing.aliyuncs.com/707bbace-3dbe-47e0-8523-67eef4cbf618.png',
      info: {
        album_title: '',
        album_values: '',
        employee_name: '',
        event_time: '',
        album_type: '',
      },
      isSmall: false,
      imageProcess: '?x-oss-process=video/snapshot,t_2000,m_fast,w_750,ar_auto',
    };
  },
  created() {
    const token = getUrlParams('token');
    const id = getUrlParams('id');
    const from = getUrlParams('from');
    const avatar = getUrlParams('avatar');
    const index = getUrlParams('index');
    const visitor = getUrlParams('visitor');
    const operation_id = getUrlParams('operation_id');

    this.token = token;
    this.id = id;
    this.from = from;
    this.avatar = avatar;
    this.index = index ? parseInt(index) : 0;
    this.visitor = visitor;
    this.operation_id = operation_id;
    this.getInfo();
  },
  mounted() {},
  methods: {
    async getInfo() {
      Toast.loading({
        message: '生成中...',
        forbidClick: true,
      });
      const res = await getTimeAlbumDetail({
        id: this.id,
        token: this.token,
        from: this.from,
        visitor: this.visitor,
        operation_id: this.operation_id,
        student_id: getUrlParams('stu_id'),
      });
      if (res.code === 0) {
        this.info = res.data;
        if (this.info.album_picture_video.length) {
          const obj = this.info.album_picture_video[this.index];
          if (obj.width && obj.height) {
            if (obj.width > obj.height) {
              console.log('object :>> ', 11111);
              this.isSmall = true;
            }
          }
          this.bgImgUrl = obj.video_url
            ? obj.image_url || obj.video_url + this.imageProcess
            : obj.image_url;
        }
        this.info.album_type = album_type_list.find(
          (item) => item.id === this.info.album_type,
        )?.name;
        Toast.loading({
          message: '生成中...',
          forbidClick: true,
        });
        setTimeout(() => {
          this.captureElement();
          Toast.clear();
        }, 1000);
      } else {
        Toast.fail('生成失败,请重试');
      }
    },
    async captureElement() {
      // 等待图片加载完成
      await this.$nextTick();
      const element = this.$refs.shareCard;
      const canvas = await html2canvas(element, {
        backgroundColor: 'transparent', // 设置背景为透明,显示圆角图片
        scale: window.devicePixelRatio * 2, // 使用更高的缩放比例, // 高清截图
        imageTimeout: 0, // 不限制图片加载时间
        useCORS: true,
        logging: false, // 关闭日志
        scrollX: 0,
        scrollY: 0,
      });
      this.imgUrl = canvas.toDataURL('image/png', 1);
    },
  },
};
</script>

<style lang="less" scoped>
.share-card-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  // background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png');
  background-size: 200% 200%;
  background-repeat: no-repeat;
  background-position: center;
  filter: blur(100px);
  z-index: 1;
}

.share-card-dialog {
  position: fixed;
  top: 50%;
  left: 200%;
  // left: 50%;
  z-index: 2;
  transform: translate(0%, -50%);
  transform-style: preserve-3d;
}
.share-card {
  padding: 24px;
  background: #fff;
  border-radius: 24px;
  //   overflow: hidden;
  .img-container {
    position: relative;
    width: 568px;
    height: 755px;

    // background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    &.small {
      width: 568px;
      height: 390px;
    }
    // overflow: hidden;
    .van-image {
      border-radius: 24px 24px 0px 0px;
      overflow: hidden;
    }
    .top-img {
      position: absolute;
      top: 0;
      left: -2px;
      // background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/727bcae6-85b7-40e1-aafe-c41bb6d35bb1.png');
      // background-size: cover;
      // background-repeat: no-repeat;
      // background-position: center;
      width: 180px;
      height: 51px;
    }
  }
  .share-card-content {
    .share-card-title {
      position: relative;
      display: flex;
      align-items: center;
      font-size: 28px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
      margin-top: 10px;
      padding-left: 80px;
      .avatar-img {
        position: absolute;
        bottom: -4px;
        left: 0;
        z-index: 3;
        width: 74px;
        height: 74px;
      }
      .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px 8px;
        background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
        color: #fff;
        font-size: 24px;
        font-weight: 500;
        padding: 0px 16px;
        margin-left: 12px;
        height: 38px;
        padding-bottom: 4px;
      }
    }
    .share-card-desc {
      font-size: 28px;
      font-weight: 400;
      color: #666;
      line-height: normal;
    }
    .share-card-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 26px;
      font-weight: 400;
      color: #999;
      margin: 8px 0 18px 0;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: #eeeeee;
    margin-top: 20px;
  }
  .minigram-mark {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: #333;
    img {
      width: 94px;
      height: 94px;
      margin-left: 20px;
    }
  }
}

.share-card-img {
  position: absolute;
  top: 50%;
  left: 50%;

  z-index: 5;
  transform: translate(-50%, -50%);
  img {
    width: 616px;
  }
  .save-img {
    color: #fff;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-align: center;
    margin-top: 30px;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.23);
  }
}
</style>
