<template>
  <div>
    <img @click="preview" :src="src" alt="" />
  </div>
</template>

<script>
import { getUrlParams } from '@/utils/util';
import { ImagePreview } from 'vant';
export default {
  data() {
    return {
      src: '',
    };
  },
  created() {
    const src = getUrlParams('url');
    const title = decodeURIComponent(getUrlParams('title'));
    document.title = title;
    this.src = src;
  },
  mounted() {},
  methods: {
    preview() {
      ImagePreview([this.src]);
    },
  },
};
</script>

<style lang="less" scoped>
img {
  width: 100%;
  height: auto;
  vertical-align: middle;
  padding: 0;
  margin: 0;
}
</style>
