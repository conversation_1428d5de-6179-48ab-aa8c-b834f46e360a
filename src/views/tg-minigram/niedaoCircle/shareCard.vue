<template>
  <div
    id="shareCard"
    class="shareCard"
    :style="{ backgroundImage: `url(${defaultImg}) no-repeat` }"
  >
    <img :src="defaultImg" class="shareCard-bg" alt="" />
    <div class="shareCard-bg"></div>
    <div class="generate-share-img-wrap">
      <div class="generate-share-img" :style="{ width: shareImgW, height: shareImgH }">
        <img :src="shareUrl" alt="" />
      </div>
      <div class="tips">长按图片保存到相册</div>
    </div>
    <div class="shareCard-wrap" id="shareCardWrap" ref="shareCardWrap">
      <div class="card-content" @click="shareScreenshot">
        <div class="share-img">
          <div class="logo-wrap">
            <img src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6bd8a98c-565e-4efd-b70f-1bfa916988b0.png" alt="">
          </div>
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/727bcae6-85b7-40e1-aafe-c41bb6d35bb1.png"
            class="logo"
            alt=""
          />
          <img
            :src="defaultImg"
            :class="['share-img-bg', isSmall ? 'small' : '']"
          />
        </div>
        <div class="teacher-info">
          <div class="teacher-avatar">
            <img :src="momentDetail.title_img" alt="" />
          </div>
          <div class="teacher-name">
            <span>{{ momentDetail.operator_name }}</span>
          </div>
        </div>
        <div class="share-content">
          {{ momentDetail.content || '发布了一篇内容'}}
        </div>
      </div>
      <div class="line"></div>
      <div class="qrcode">
        <div class="qrcode-text">聂道少儿围棋，只为智慧成长</div>
        <img
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c5927512-25dd-47f5-83ef-4b8b6183e8a6.png"
          class="qrcode-img"
          alt=""
        />
      </div>
    </div>
  </div>
</template>
<script>
import { Toast } from 'vant';
import { shareDetail } from '@/api/niedaoCicle';
export default {
  data() {
    return {
      shareUrl: '',
      shareImgW: 0,
      shareImgH: 0,
      momentDetail: {
        title_img:
          'https://tg-prod.oss-cn-beijing.aliyuncs.com/28332296-bfee-4554-854c-090687d61de3.png',
        operator_name: '张三',
        content:
          '这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容这是一条分享内容',
      },
      defaultImg:
        'https://tg-prod.oss-cn-beijing.aliyuncs.com/50e99c07-ae26-4a69-ad0a-785ab4a2454f.png',
    };
  },
  mounted() {
    this.getShareDetail();
    // this.shareScreenshot();
  },
  computed: {
    isSmall() {
      if (this.momentDetail.image_url && this.momentDetail.image_url.length) {
        const { width, height } = this.momentDetail?.image_url[0];
        return width > height;
      } else {
        return false;
      }
    },
  },
  methods: {
    async getShareDetail() {
      const { code, data, message } = await shareDetail(this.$route.query);
      if (code === 0) {
        if (data.image_url?.length) {
          if (data.image_type === 1) {
            if (data.image_url?.length) {
              this.defaultImg = data.image_url[0].url;
            }
          } else if (data.image_type === 2) {
            if (data.cover_url) {
              this.defaultImg = data.cover_url;
            } else {
              this.defaultImg =
                data.image_url[0].url + '?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast';
            }
          }
        } else {
          this.defaultImg =
            'https://tg-prod.oss-cn-beijing.aliyuncs.com/50e99c07-ae26-4a69-ad0a-785ab4a2454f.png';
        }
        this.momentDetail = data;
      } else {
        Toast(message);
      }
      this.shareScreenshot();
    },
    async captureElement() {
      try {
        // 等待图片加载完成
        await this.$nextTick();

        const element = this.$refs.shareCardWrap;
        const pixelRatio = window.devicePixelRatio || 1;
        const canvas = await html2canvas(element, {
          scale: pixelRatio, // 高清截图
          useCORS: true,
          logging: false, // 关闭日志
          scrollX: 0,
          scrollY: 0,
        });

        return canvas.toDataURL('image/png', 0.9); // 使用JPEG格式，90%质量
      } catch (error) {
        console.error('截图失败:', error);
        return null;
      }
    },

    async shareScreenshot() {
      const imgUrl = await this.captureElement();
      if (!imgUrl) return;
      this.shareUrl = imgUrl;
      const { width, height } = this.$refs.shareCardWrap.getBoundingClientRect();
      this.shareImgW = width + 'px';
      this.shareImgH = height + 'px';
      // 分享图片到微信
      if (window.wx && wx.miniProgram) {
        wx.miniProgram.postMessage({
          data: {
            type: 'shareImage',
            imgUrl: imgUrl,
          },
        });
      }
    },
  },
};
</script>
<style scoped lang="less">
.shareCard {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-size: cover;
  padding-top: 100px;
  background-size: 100% 100%;
  box-sizing: border-box;
  .shareCard-bg {
    width: 100%;
    height: 100%;
    backdrop-filter: blur(100px); /* 关键属性 - 模糊效果 */
    -webkit-backdrop-filter: blur(20px); /* Safari 支持 */
    background: rgba(255, 255, 255, 0.5);
    box-shadow: inset 0px 0 75px 25px rgba(255, 255, 255, 1);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  .generate-share-img-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .generate-share-img {
    width: 568px;
    height: 500px;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    img {
      border-radius: 24px;
      width: 100%;
      height: 100%;
    }
  }
  .tips {
    text-align: center;
    position: relative;
    font-size: 28px;
    font-weight: 400;
    color: #fff;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.23);
    margin-top: 24px;
  }
  .shareCard-wrap {
    position: absolute;
    top: 9999px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    border-radius: 24px;
    background: #fff;
    padding: 24px;
    margin: 0 auto;
    // 投影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    .card-content {
      border-radius: 24px;
      
      .share-img {
        width: 100%;
        position: relative;
        border-radius: 24px 24px 0 0;
        .logo-wrap{
          width: 100%;
          height: 100%;
          position: absolute;
          top: -0.5px;
          left: -0.5px;
          z-index: 99;
        img{
          width: 200px;
          height: 57px;
        }
      }
        .logo {
          width: 180px;
          height: 51px;
          position: absolute;
          top: 0;
          left: 0;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .share-img-bg {
          width: 568px;
          height: 748px;
          image-rendering: -webkit-optimize-contrast; /* Chrome */
          image-rendering: crisp-edges; /* Firefox */
          image-rendering: pixelated; /* 像素图时有效 */
          transform: translateZ(0); /* 开启GPU加速，有时可提升渲染质量 */
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          border-top-left-radius: 3.2vw;
          border-top-right-radius: 3.2vw;
          &.small {
            width: 568px;
            height: 390px;
          }
        }
      }
    }
    .teacher-info {
      width: 100%;
      display: flex;
      align-items: flex-end;
      margin-bottom: 12px;
      margin-top: -38px;
      .teacher-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        position: relative;
        z-index: 1;
        border: 1px solid #fff;
        img {
          border-radius: 50%;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .teacher-name {
        font-size: 30px;
        font-weight: 500;
        color: #333;
        margin-left: 10px;
      }
    }
    .share-content {
      color: #666;
      font-size: 26px;
      width: 100%;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      line-height: 1.5;
      text-overflow: ellipsis;
      word-break: break-word; /* 优先在单词间断行 */
      line-break: anywhere;
    }
    .qrcode {
      display: flex;
      align-items: center;
      margin-top: 18px;
      justify-content: center;
      .qrcode-img {
        width: 88px;
        height: 88px;
        margin-left: 20px;
      }
      .qrcode-text {
        font-size: 24px;
        color: #333;
        font-weight: 400;
      }
    }
  }
}
.line {
    width: 100%;
    height: 0.5px;
    background: #eeeeee;
    margin-top: 20px;
  }
</style>
