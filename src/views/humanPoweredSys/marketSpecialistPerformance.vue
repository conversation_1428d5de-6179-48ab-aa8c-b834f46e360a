<template>
  <div class="market-specialist-performance">
    <!-- 加载状态 -->
    <SkeletonLoader v-if="loading" />

    <!-- 正常内容 -->
    <div v-else class="content">
      <!-- 头部信息 -->
      <div class="header-section">
        <div class="header-content">
          <div class="user-info">
            <h1 class="system-title">聂道人力动态驱动系统</h1>
            <p class="user-role">才佳林 · 市场专员</p>
          </div>
          <!-- 校区切换按钮 -->
          <div class="campus-selector">
            <button 
              @click="toggleCampusDropdown" 
              class="campus-btn"
            >
            <div class="user-avatar">
            <i class="fas fa-user-cog"></i>
          </div>
              <!-- <i class="fas fa-building"></i>
              <span>{{ selectedCampus }}</span>
              <i class="fas fa-chevron-down"></i> -->
            </button>
            <!-- 下拉菜单 -->
            <div v-show="showCampusDropdown" class="campus-dropdown">
              <div class="dropdown-content">
                <button
                  v-for="campus in campusList"
                  :key="campus"
                  @click="selectCampus(campus)"
                  class="campus-option"
                  :class="{ active: campus === selectedCampus }"
                >
                  {{ campus }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间筛选模块 -->
      <TimeFilter
        :default-period="defaultTimePeriod"
        :default-start-date="defaultStartDate"
        :default-end-date="defaultEndDate"
        @change="onTimeFilterChange"
      />

      <!-- 目标模块 -->
      <div class="target-section">
        <div class="section-header">
          <i class="fas fa-bullseye"></i>
          <span class="section-title">目标</span>
        </div>
        <div class="target-grid">
          <!-- 表单数量 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper blue">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber
                    :value="targetData.formCount.current"
                    :delay="200"
                    :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px"
                  />
                </div>
                <div class="target">/ {{ targetData.formCount.target }}</div>
              </div>
            </div>
            <div class="card-title">表单数量</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div 
                  class="progress-fill blue" 
                  :style="{ width: targetData.formCount.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text blue">{{ targetData.formCount.percentage }}%</span>
            </div>
          </div>

          <!-- 正课新招人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper green">
                <i class="fas fa-user-plus"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber
                    :value="targetData.newStudents.current"
                    :delay="400"
                    :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px"
                  />
                </div>
                <div class="target">/ {{ targetData.newStudents.target }}</div>
              </div>
            </div>
            <div class="card-title">正课新招人数</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div 
                  class="progress-fill green" 
                  :style="{ width: targetData.newStudents.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text green">{{ targetData.newStudents.percentage }}%</span>
            </div>
          </div>

          <!-- 当月到店试听人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper purple">
                <i class="fas fa-headphones"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber
                    :value="targetData.trialListeners.current"
                    :delay="600"
                    :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px"
                  />
                </div>
                <div class="target">/ {{ targetData.trialListeners.target }}</div>
              </div>
            </div>
            <div class="card-title">当月到店试听人数</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div 
                  class="progress-fill purple" 
                  :style="{ width: targetData.trialListeners.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text purple">{{ targetData.trialListeners.percentage }}%</span>
            </div>
          </div>

          <!-- 有效客户信息数量 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper orange">
                <i class="fas fa-users"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber
                    :value="targetData.validCustomers.current"
                    :delay="800"
                    :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px"
                  />
                </div>
                <div class="target">/ {{ targetData.validCustomers.target }}</div>
              </div>
            </div>
            <div class="card-title">有效客户信息数量</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div 
                  class="progress-fill orange" 
                  :style="{ width: targetData.validCustomers.percentage + '%' }"
                ></div>
              </div>
              <span class="progress-text orange">{{ targetData.validCustomers.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 过程指标模块 -->
      <div class="process-section">
        <div class="section-header">
          <i class="fas fa-chart-pie"></i>
          <span class="section-title">过程指标</span>
        </div>
        
        <!-- ECharts漏斗图容器 -->
        <div class="chart-container">
          <div 
            ref="funnelChart" 
            class="funnel-chart"
            :style="{ width: '100%', height: '280px' }"
          ></div>
        </div>
      </div>

      <!-- 热力值模块 -->
      <div class="heat-section">
        <div class="section-header">
          <i class="fas fa-fire"></i>
          <span class="section-title">热力值</span>
        </div>
        
        <!-- 实际热力值汇总 -->
        <div class="heat-summary-card">
          <div class="heat-header">
            <div class="heat-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="heat-info">
              <div class="heat-label">本月实际热力值</div>
              <div class="heat-value">
                <AnimatedNumber
                  :value="heatData.totalValue"
                  :delay="1000"
                  :use-intersection-observer="true"
                  root-margin="0px 0px -50px 0px"
                  prefix="¥"
                />
              </div>
            </div>
          </div>
          
          <!-- 热力值组成明细 -->
          <div class="heat-details">
            <div class="heat-item-row">
              <div class="heat-item">
                <div class="item-header">
                  <span class="item-name">采单热力值</span>
                  <span class="item-value text-red">¥{{ heatData.formHeat.toLocaleString() }}</span>
                </div>
                <div class="item-percentage">占比 {{ heatData.formHeatPercentage }}%</div>
              </div>
              <div class="heat-item">
                <div class="item-header">
                  <span class="item-name">拉访热力值</span>
                  <span class="item-value text-blue">¥{{ heatData.visitHeat.toLocaleString() }}</span>
                </div>
                <div class="item-percentage">占比 {{ heatData.visitHeatPercentage }}%</div>
              </div>
            </div>
            <div class="heat-item single">
              <div class="item-header">
                <span class="item-name">系统课热力值</span>
                <span class="item-value text-orange">¥{{ heatData.systemHeat.toLocaleString() }}</span>
              </div>
              <div class="item-percentage">占比 {{ heatData.systemHeatPercentage }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SkeletonLoader from '@/views/humanPoweredSys/common/SkeletonLoader.vue';
import TimeFilter from '@/views/humanPoweredSys/common/TimeFilter.vue';
import AnimatedNumber from '@/views/humanPoweredSys/common/AnimatedNumber.vue';

export default {
  name: 'MarketSpecialistPerformance',
  components: {
    SkeletonLoader,
    TimeFilter,
    AnimatedNumber,
  },
  data() {
    return {
      loading: true,
      showCampusDropdown: false,
      selectedCampus: '北京刘家窑',
      campusList: ['北京刘家窑', '北京德胜门', '北京军博', '北京双井'],
      // 时间筛选相关
      defaultTimePeriod: '日',
      defaultStartDate: '',
      defaultEndDate: '',
      currentTimeFilter: {
        period: '日',
        startDate: '',
        endDate: '',
      },
      
      // 动画定时器数组
      animationTimers: [],
      
      // 进度条视窗检测器
      progressObserver: null,
      
      // 防止重复调用的标志
      lastTimeFilter: null,
      // 目标数据
      targetData: {
        formCount: {
          current: 156,
          target: 200,
          percentage: 78,
        },
        newStudents: {
          current: 42,
          target: 50,
          percentage: 84,
        },
        trialListeners: {
          current: 68,
          target: 80,
          percentage: 85,
        },
        validCustomers: {
          current: 285,
          target: 350,
          percentage: 81.4,
        },
      },
      // 漏斗图数据 - 按原型页面数据配置
      funnelData: [
        {
          name: '表单有效率',
          current: 156,
          target: 200,
          value: 78,
        },
        {
          name: '邀约率',
          current: 68,
          target: 80,
          value: 85,
        },
        {
          name: '渠道转化率',
          current: 42,
          target: 50,
          value: 44,
        },
        {
          name: '试听转化率',
          current: 35,
          target: 42,
          value: 83,
        },
      ],
      // 热力值数据
      heatData: {
        totalValue: 6580,
        formHeat: 2303,
        formHeatPercentage: 35,
        visitHeat: 1974,
        visitHeatPercentage: 30,
        systemHeat: 2303,
        systemHeatPercentage: 35,
      },
      funnelChart: null,
    };
  },
  mounted() {
    this.initData();
    // 点击外部关闭下拉菜单
    document.addEventListener('click', this.handleOutsideClick);
  },
  beforeDestroy() {
    // 清除图表实例
    if (this.funnelChart) {
      this.funnelChart.dispose();
    }
    
    // 清除所有动画定时器
    this.clearAnimationTimers();
    
    // 清除进度条观察器
    this.destroyProgressObserver();
    
    document.removeEventListener('click', this.handleOutsideClick);
  },
  methods: {
    // 初始化数据加载
    initData() {
      this.loading = true;
      
      // 模拟数据加载完成
      setTimeout(() => {
        this.loading = false;
        this.$nextTick(() => {
          this.initFunnelChart();
          this.initProgressObserver();
        });
      }, 1000);
    },
    
    // 时间筛选变更事件
    onTimeFilterChange(timeFilter) {
      // 检查是否与上次的时间筛选相同，避免重复调用
      const timeFilterStr = JSON.stringify(timeFilter);
      if (this.lastTimeFilter === timeFilterStr) {
        console.log('时间筛选未变化，跳过重复调用');
        return;
      }
      
      console.log('时间筛选变更:', timeFilter);
      this.lastTimeFilter = timeFilterStr;
      this.currentTimeFilter = timeFilter;
      this.initData();
    },
    
    // 切换校区下拉菜单
    toggleCampusDropdown() {
      this.showCampusDropdown = !this.showCampusDropdown;
    },
    
    // 选择校区
    selectCampus(campus) {
      this.selectedCampus = campus;
      this.showCampusDropdown = false;
      // 这里可以调用API获取对应校区的数据
      console.log('选择校区:', campus);
    },
    
    // 点击外部关闭下拉菜单
    handleOutsideClick(event) {
      const campusSelector = this.$el.querySelector('.campus-selector');
      if (campusSelector && !campusSelector.contains(event.target)) {
        this.showCampusDropdown = false;
      }
    },
    
    // 初始化漏斗图
    initFunnelChart() {
      this.$nextTick(() => {
        if (!this.$refs.funnelChart) return;
        
        // 确保echarts已加载
        if (typeof window.echarts === 'undefined') {
          console.error('ECharts未加载');
          return;
        }
        
        this.funnelChart = window.echarts.init(this.$refs.funnelChart);
        
        const option = {
          backgroundColor: 'transparent',
          title: {
            show: false,
          },
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: {
              color: '#fff',
              fontSize: 12,
            },
            formatter: function(params) {
              const data = this.funnelData[params.dataIndex];
              return `${params.name}<br/>当前完成: ${data.current}人<br/>目标: ${data.target}人<br/>完成率: ${params.value}%`;
            }.bind(this),
          },
          legend: {
            show: false,
          },
          series: [
            {
              name: '过程指标',
              type: 'funnel',
              left: 'center',
              top: 20,
              bottom: 20,
              width: '80%',
              min: 0,
              max: 100,
              minSize: '10%',
              maxSize: '90%',
              sort: 'descending',
              gap: 1,
              funnelAlign: 'center',
              label: {
                show: true,
                position: 'inside',
                formatter: function(params) {
                  const data = this.funnelData[params.dataIndex];
                  return `${params.name}\n${data.current}/${data.target}人\n${params.value}%`;
                }.bind(this),
                fontSize: 11,
                color: '#fff',
                fontWeight: 'bold',
              },
              labelLine: {
                length: 10,
                lineStyle: {
                  width: 1,
                  type: 'solid',
                },
              },
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
              },
              emphasis: {
                label: {
                  fontSize: 12,
                },
                itemStyle: {
                  shadowBlur: 20,
                  shadowOffsetX: 0,
                  shadowOffsetY: 10,
                  shadowColor: 'rgba(0, 0, 0, 0.3)',
                },
              },
              data: this.funnelData.map((item, index) => {
                return {
                  value: item.value,
                  name: item.name,
                  current: item.current,
                  target: item.target,
                  itemStyle: {
                    color: this.getFunnelColor(index),
                  },
                };
              }),
            },
          ],
        };
        
        this.funnelChart.setOption(option);
        
        // 响应式处理
        window.addEventListener('resize', () => {
          if (this.funnelChart) {
            this.funnelChart.resize();
          }
        });
      });
    },
    
    // 获取漏斗图颜色
    getFunnelColor(index) {
      const colors = [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#6366f1' },
            { offset: 0.5, color: '#4f46e5' },
            { offset: 1, color: '#3730a3' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#10b981' },
            { offset: 0.5, color: '#059669' },
            { offset: 1, color: '#047857' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#8b5cf6' },
            { offset: 0.5, color: '#7c3aed' },
            { offset: 1, color: '#5b21b6' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#f97316' },
            { offset: 0.5, color: '#ea580c' },
            { offset: 1, color: '#c2410c' },
          ],
        },
      ];
      
      return colors[index % colors.length];
    },
    
    // 清除动画定时器
    clearAnimationTimers() {
      this.animationTimers.forEach((timer) => {
        clearTimeout(timer);
      });
      this.animationTimers = [];
    },
    
    // 初始化进度条视窗检测器
    initProgressObserver() {
      // 检查浏览器是否支持 Intersection Observer
      if (!window.IntersectionObserver) {
        // 不支持则直接开始动画
        this.animateProgressBars();
        return;
      }
      
      const options = {
        root: null,
        rootMargin: '0px 0px -50px 0px', // 提前50px触发
        threshold: 0.1,
      };
      
      this.progressObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !entry.target.hasAttribute('data-animated')) {
            // 标记已动画，避免重复触发
            entry.target.setAttribute('data-animated', 'true');
            this.animateProgressBar(entry.target);
          }
        });
      }, options);
      
      // 观察所有进度条元素
      this.$nextTick(() => {
        const progressBars = this.$el.querySelectorAll('.progress-fill');
        progressBars.forEach((bar) => {
          // 初始化进度条状态
          const width = bar.style.width;
          bar.setAttribute('data-target-width', width);
          bar.style.width = '0%';
          bar.style.transition = 'none';
          
          // 开始观察
          this.progressObserver.observe(bar);
        });
      });
    },
    
    // 销毁进度条观察器
    destroyProgressObserver() {
      if (this.progressObserver) {
        this.progressObserver.disconnect();
        this.progressObserver = null;
      }
    },
    
    // 单个进度条动画
    animateProgressBar(bar) {
      const targetWidth = bar.getAttribute('data-target-width');
      
      // 添加过渡效果
      bar.style.transition = 'width 1s ease-out';
      
      // 稍微延迟一下再开始动画，确保过渡效果生效
      setTimeout(() => {
        bar.style.width = targetWidth;
      }, 50);
    },
    
    // 进度条动画（备用方法，用于不支持 Intersection Observer 的浏览器）
    animateProgressBars() {
      // 先清除之前的定时器
      this.clearAnimationTimers();
      
      this.$nextTick(() => {
        const progressBars = this.$el.querySelectorAll('.progress-fill');
        progressBars.forEach((bar, index) => {
          const width = bar.style.width;
          bar.style.width = '0%';
          
          const timer = setTimeout(() => {
            bar.style.transition = 'width 1s ease-out';
            bar.style.width = width;
          }, index * 200 + 500);
          
          // 将定时器添加到数组中以便清除
          this.animationTimers.push(timer);
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.market-specialist-performance {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', sans-serif;
  
  .content {
    background: #f8fafc;
    min-height: 100vh;
  }
  
  // 头部样式
  .header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24px;
    color: white;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .user-info {
      flex: 1;
      
      .system-title {
        font-size: 40px;
        font-weight: bold;
        margin: 0 0 16px 0;
        line-height: 1.2;
      }
      
      .user-role {
        font-size: 28px;
        opacity: 0.9;
        margin: 0;
      }
    }
    
    .campus-selector {
      position: relative;
      
      .campus-btn {
        // background: rgba(255, 255, 255, 0.2);
        // backdrop-filter: blur(10px);
        // border: none;
        // border-radius: 12px;
        // padding: 8px 16px;
        // color: white;
        // font-size: @font-size-sm;
        // font-weight: 500;
        // display: flex;
        // align-items: center;
        // gap: 8px;
        // cursor: pointer;
        // transition: all 0.2s ease;
        .user-avatar {
            width: 96px;
            height: 96px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }
        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
        
        // i:last-child {
        //   font-size: 10px;
        // }
      }
      
      .campus-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 8px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-width: 128px;
        z-index: 100;
        overflow: hidden;
        
        .dropdown-content {
          padding: 8px 0;
        }
        
        .campus-option {
          width: 100%;
          padding: 8px 16px;
          background: none;
          border: none;
          text-align: left;
          font-size: @font-size-sm;
          color: @text-color;
          cursor: pointer;
          transition: background-color 0.2s ease;
          
          &:hover {
            background: #f7f8fa;
          }
          
          &.active {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
          }
        }
      }
    }
  }
  
  // 通用区块样式
  .target-section,
  .process-section,
  .heat-section {
    background: white;
    margin-top: 1px;
    padding: 32px;
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 32px;
      
      i {
        font-size: 32px;
        
        &.fa-bullseye {
          color: #ef4444;
        }
        &.fa-chart-pie {
          color: rgb(168 85 247 / var(--tw-text-opacity, 1));
        }
        &.fa-fire {
          color: rgb(239 68 68 / var(--tw-text-opacity, 1));
        }
      }
      
      .section-title {
        font-weight: 600;
        color: #1f2937;
        font-size: 32px;
      }
    }
  }
  
  // 目标卡片网格
  .target-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .target-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    
    .icon-wrapper {
      width: 88px;
      height: 88px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
      
      &.blue {
        background: #2563eb;
        color: white;
      }
      &.green {
        background: #16a34a;
        color: white;
      }
      &.purple {
        background: #8b5cf6;
        color: white;
      }
      &.orange {
        background: #f97316;
        color: white;
      }
    }
    
    .card-value {
      text-align: right;
      
      .value {
        font-size: 48px;
        font-weight: bold;
        color: #1f2937;
        line-height: 1;
      }
      
      .target {
        font-size: 24px;
        color: #6b7280;
        margin-top: 4px;
      }
    }
    
    .card-title {
      font-size: 28px;
      color: #6b7280;
      margin-bottom: 16px;
    }
    
    .progress-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
    }
    
    .progress-track {
      flex: 1;
      height: 16px;
      background: #e5e7eb;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: inherit;
      border-radius: 3px;
      transition: width 0.8s ease;
      
      &.blue {
        background: #3b82f6;
      }
      &.green {
        background: #10b981;
      }
      &.purple {
        background: #8b5cf6;
      }
      &.orange {
        background: #f97316;
      }
    }
    
    .progress-text {
      font-size: 24px;
      font-weight: 600;
      
      &.blue {
        color: #3b82f6;
      }
      &.green {
        color: #10b981;
      }
      &.purple {
        color: #8b5cf6;
      }
      &.orange {
        color: #f97316;
      }
    }
  }
  
  // 图表容器
  .chart-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    
    // 背景装饰
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.05) 0%, 
        rgba(139, 92, 246, 0.03) 50%, 
        rgba(249, 115, 22, 0.05) 100%);
      border-radius: 12px;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.1) 0%, 
        rgba(139, 92, 246, 0.1) 100%);
      border-radius: 50%;
      filter: blur(20px);
    }
    
    .funnel-chart {
      position: relative;
      z-index: 1;
    }
  }
  
  // 热力值样式
  .heat-summary-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    
    // 背景装饰
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, 
        rgba(249, 115, 22, 0.05) 0%, 
        rgba(239, 68, 68, 0.03) 50%, 
        rgba(236, 72, 153, 0.05) 100%);
      border-radius: 12px;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, 
        rgba(249, 115, 22, 0.1) 0%, 
        rgba(239, 68, 68, 0.1) 100%);
      border-radius: 50%;
      filter: blur(20px);
    }
    
    .heat-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      position: relative;
      z-index: 1;
      
      .heat-icon {
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, #f97316, #ef4444);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        i {
          color: white;
          font-size: @font-size-xl;
        }
      }
      
      .heat-info {
        flex: 1;
        
        .heat-label {
          font-size: @font-size-sm;
          color: #6b7280;
          margin-bottom: 4px;
        }
        
        .heat-value {
          font-size: @font-size-28;
          font-weight: bold;
          color: @text-color;
          line-height: 1;
        }
      }
    }
    
    .heat-details {
      position: relative;
      z-index: 1;
      
      .heat-item-row {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-bottom: 12px;
      }
      
      .heat-item {
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(249, 115, 22, 0.1);
        border-radius: 8px;
        padding: 12px;
        
        &.single {
          margin-bottom: 0;
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          
          .item-name {
            font-size: @font-size-sm;
            color: #6b7280;
          }
          
          .item-value {
            font-size: @font-size-sm;
            font-weight: bold;
            color: @text-color;
          }
          .text-red {
            color: #ef4444;
          }
          .text-blue {
            color: #2563eb;
          }
          .text-orange {
            color: #f97316;
          }
        }
        
        .item-percentage {
          font-size: 10px;
          color: #9ca3af;
        }
      }
    }
  }
}

// 时间筛选组件白色背景
/deep/ .time-filter {
  background: white;
  border-top: 1px solid #e5e7eb;
}
</style>