<template>
    <div class="principal">
        <!-- 加载状态 -->
        <SkeletonLoader v-if="skeletonLoading" />

        <!-- 正常内容 -->
        <div v-else class="content">
            <!-- 头部信息 -->
            <div class="header-section">
                <div class="header-content">
                    <div class="user-info">
                        <h1 class="system-title">聂道人力动态驱动系统</h1>
                        <p class="user-role">才佳林 · 校长</p>
                        <p class="user-campus"><i class="fas fa-building"></i>北京刘家窑</p>
                    </div>
                </div>
            </div>

            <!-- 角色切换模块 -->
            <div class="role-section">
                <div class="section-header">
                    <i class="fas fa-users-cog"></i>
                    <span class="section-title">角色切换</span>
                </div>
                <div class="role-buttons">
                    <button v-for="role in roleList" :key="role.key" class="role-btn"
                        :class="{ active: selectedRole === role.key }" @click="selectRole(role.key)">
                        {{ role.name }}
                    </button>
                </div>
            </div>

            <!-- 时间筛选模块 -->
            <TimeFilter @change="onTimeFilterChange" />

            <!-- 目标模块 -->
            <div class="target-section">
                <div class="section-header">
                    <i class="fas fa-bullseye"></i>
                    <span class="section-title">目标</span>
                </div>
                <div class="target-grid">
                    <!-- 现金流 -->
                    <div class="target-card">
                        <div class="card-header">
                            <div class="icon-wrapper green">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="card-value">
                                <div class="value">
                                    <AnimatedNumber :value="targetData.cashFlow.current" prefix="¥" suffix="K"
                                        :use-grouping="false" :delay="200" :use-intersection-observer="true"
                                        root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="target">/ ¥{{ targetData.cashFlow.target }}K</div>
                            </div>
                        </div>
                        <div class="card-title">现金流</div>
                        <div class="progress-bar">
                            <div class="progress-fill green" :style="{ width: targetData.cashFlow.percentage + '%' }">
                            </div>
                        </div>
                        <div class="progress-text">{{ targetData.cashFlow.percentage }}%</div>
                    </div>

                    <!-- 利润 -->
                    <div class="target-card">
                        <div class="card-header">
                            <div class="icon-wrapper blue">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-value">
                                <div class="value">
                                    <AnimatedNumber :value="targetData.profit.current" prefix="¥" suffix="K"
                                        :use-grouping="false" :delay="400" :use-intersection-observer="true"
                                        root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="target">/ ¥{{ targetData.profit.target }}K</div>
                            </div>
                        </div>
                        <div class="card-title">利润</div>
                        <div class="progress-bar">
                            <div class="progress-fill blue" :style="{ width: targetData.profit.percentage + '%' }">
                            </div>
                        </div>
                        <div class="progress-text">{{ targetData.profit.percentage }}%</div>
                    </div>
                </div>

                <!-- 人数 - 占据一行 -->
                <div class="target-card full-width">
                    <div class="card-header">
                        <div class="card-left">
                            <div class="icon-wrapper purple">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-info">
                                <div class="value">
                                    <AnimatedNumber :value="targetData.people.current" :delay="600"
                                        :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="card-title">人数</div>
                            </div>
                        </div>
                        <div class="card-right">
                            <div class="target-info">目标: {{ targetData.people.target }}人</div>
                            <div class="progress-container">
                                <div class="progress-bar small">
                                    <div class="progress-fill purple"
                                        :style="{ width: targetData.people.percentage + '%' }"></div>
                                </div>
                                <div class="progress-text">{{ targetData.people.percentage }}%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预算完成度模块 -->
            <div class="budget-section">
                <div class="section-header">
                    <i class="fas fa-calculator"></i>
                    <span class="section-title">预算完成度</span>
                </div>

                <!-- 业绩 -->
                <div class="budget-main-card">
                    <div class="card-header">
                        <div class="icon-wrapper orange">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="card-value">
                            <div class="value">
                                <AnimatedNumber :value="budgetData.performance.current" prefix="¥" suffix="K"
                                    :use-grouping="false" :delay="800" :use-intersection-observer="true"
                                    root-margin="0px 0px -50px 0px" />
                            </div>
                            <div class="target">/ ¥{{ budgetData.performance.target }}K</div>
                        </div>
                    </div>
                    <div class="card-title">业绩</div>
                    <div class="progress-bar">
                        <div class="progress-fill orange" :style="{ width: budgetData.performance.percentage + '%' }">
                        </div>
                    </div>
                    <div class="progress-text">{{ budgetData.performance.percentage }}%</div>

                    <!-- 业绩分支 -->
                    <div class="performance-breakdown">
                        <div class="breakdown-item">
                            <div class="breakdown-label">新签</div>
                            <div class="breakdown-value blue">¥{{ budgetData.performance.newSign }}K</div>
                            <div class="breakdown-percent">{{ budgetData.performance.newSignPercent }}%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="breakdown-label">续费</div>
                            <div class="breakdown-value green">¥{{ budgetData.performance.renewal }}K</div>
                            <div class="breakdown-percent">{{ budgetData.performance.renewalPercent }}%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="breakdown-label">其他</div>
                            <div class="breakdown-value purple">¥{{ budgetData.performance.other }}K</div>
                            <div class="breakdown-percent">{{ budgetData.performance.otherPercent }}%</div>
                        </div>
                    </div>
                </div>

                <div class="budget-grid">
                    <!-- 营收（税后） -->
                    <div class="budget-card">
                        <div class="icon-wrapper teal">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="card-title">营收（税后）</div>
                        <div class="card-value">
                            <div class="value">
                                <AnimatedNumber :value="budgetData.revenue.current" prefix="¥" suffix="K"
                                    :use-grouping="false" :delay="1000" :use-intersection-observer="true"
                                    root-margin="0px 0px -50px 0px" />
                            </div>
                            <div class="target">/ ¥{{ budgetData.revenue.target }}K</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill teal" :style="{ width: budgetData.revenue.percentage + '%' }">
                            </div>
                        </div>
                        <div class="progress-text">{{ budgetData.revenue.percentage }}%</div>
                    </div>

                    <!-- 成本 -->
                    <div class="budget-card">
                        <div class="icon-wrapper rose">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <div class="card-title">成本</div>
                        <div class="card-value">
                            <div class="value">
                                <AnimatedNumber :value="budgetData.cost.current" prefix="¥" suffix="K"
                                    :use-grouping="false" :delay="1200" :use-intersection-observer="true"
                                    root-margin="0px 0px -50px 0px" />
                            </div>
                            <div class="target">/ ¥{{ budgetData.cost.target }}K</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill rose" :style="{ width: budgetData.cost.percentage + '%' }"></div>
                        </div>
                        <div class="progress-text">{{ budgetData.cost.percentage }}%</div>
                    </div>
                </div>
            </div>

            <!-- 校区健康度模块 -->
            <div class="health-section">
                <div class="section-header">
                    <i class="fas fa-heartbeat"></i>
                    <span class="section-title">校区健康度</span>
                </div>

                <div class="health-grid">
                    <!-- 净现金流 -->
                    <div class="health-card">
                        <div class="card-header">
                            <div class="icon-wrapper emerald">
                                <i class="fas fa-water"></i>
                            </div>
                            <div class="card-value">
                                <div class="value emerald">
                                    +
                                    <AnimatedNumber :value="healthData.netCashFlow.value" prefix="¥" suffix="K"
                                        :use-grouping="false" :delay="1400" :use-intersection-observer="true"
                                        root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="status emerald">{{ healthData.netCashFlow.status }}</div>
                            </div>
                        </div>
                        <div class="card-title">净现金流</div>
                        <div class="card-desc">
                            <div class="status-dot emerald"></div>
                            <span>{{ healthData.netCashFlow.desc }}</span>
                        </div>
                    </div>

                    <!-- 净利润 -->
                    <div class="health-card">
                        <div class="card-header">
                            <div class="icon-wrapper blue">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="card-value">
                                <div class="value blue">
                                    +
                                    <AnimatedNumber :value="healthData.netProfit.value" prefix="¥" suffix="K"
                                        :use-grouping="false" :delay="1600" :use-intersection-observer="true"
                                        root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="status blue">{{ healthData.netProfit.status }}</div>
                            </div>
                        </div>
                        <div class="card-title">净利润</div>
                        <div class="card-desc">
                            <div class="status-dot blue"></div>
                            <span>{{ healthData.netProfit.desc }}</span>
                        </div>
                    </div>
                </div>

                <!-- 人数净增长 - 占据一行 -->
                <div class="health-card full-width">
                    <div class="card-header">
                        <div class="card-left">
                            <div class="icon-wrapper purple">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="card-info">
                                <div class="value purple">
                                    +
                                    <AnimatedNumber :value="healthData.netGrowth.value" suffix="人" :delay="1800"
                                        :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                                </div>
                                <div class="card-title">人数净增长</div>
                            </div>
                        </div>
                        <div class="card-right">
                            <div class="growth-info">环比 {{ healthData.netGrowth.growth }}</div>
                            <div class="card-desc">
                                <div class="status-dot purple"></div>
                                <span>{{ healthData.netGrowth.desc }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TimeFilter from './common/TimeFilter.vue';
import AnimatedNumber from './common/AnimatedNumber.vue';
import SkeletonLoader from './common/SkeletonLoader.vue';
import { getPrincipalList } from '@/api/humanPoweredSys';
import dayjs from 'dayjs';

export default {
    name: 'Principal',
    components: {
        TimeFilter,
        AnimatedNumber,
        SkeletonLoader,
    },
    data() {
        return {
            // 时间筛选相关
            defaultTimePeriod: '日',
            currentTimeFilter: {
                period: '日',
                startDate: '',
                endDate: '',
            },

            // 页面状态
            skeletonLoading: true,
            isFirstLoad: true,
            lastTimeFilter: null,

            // 角色切换
            selectedRole: 'principal',
            roleList: [
                { key: 'principal', name: '校长' },
                { key: 'market', name: '市场' },
                { key: 'advisor', name: '课程顾问' },
                { key: 'educational', name: '教务' },
                { key: 'teacher', name: '老师' },
            ],

            // 接口数据
            principalData: null,
        };
    },

    computed: {
        // 目标数据
        targetData() {
            if (!this.principalData) {
                return {
                    cashFlow: { current: 0, target: 0, percentage: 0 },
                    profit: { current: 0, target: 0, percentage: 0 },
                    people: { current: 0, target: 0, percentage: 0 },
                };
            }
            return {
                cashFlow: {
                    current: Math.round((this.principalData.cash_flow || 0) / 1000),
                    target: Math.round((this.principalData.cash_flow_target || 0) / 1000),
                    percentage: Math.round(this.principalData.cash_flow_finish_ratio || 0),
                },
                profit: {
                    current: Math.round((this.principalData.profit || 0) / 1000),
                    target: Math.round((this.principalData.profit_target || 0) / 1000),
                    percentage: Math.round(this.principalData.profit_finish_ratio || 0),
                },
                people: {
                    current: this.principalData.people || 0,
                    target: this.principalData.people_target || 0,
                    percentage: Math.round(this.principalData.people_finish_ratio || 0),
                },
            };
        },

        // 预算完成度数据
        budgetData() {
            if (!this.principalData) {
                return {
                    performance: {
                        current: 0, target: 0, percentage: 0,
                        newSign: 0, newSignPercent: 0,
                        renewal: 0, renewalPercent: 0,
                        other: 0, otherPercent: 0,
                    },
                    revenue: { current: 0, target: 0, percentage: 0 },
                    cost: { current: 0, target: 0, percentage: 0 },
                };
            }

            const performanceCurrent = Math.round((this.principalData.performance || 0) / 1000);
            const newSign = Math.round((this.principalData.new_sign || 0) / 1000);
            const renewal = Math.round((this.principalData.renewal || 0) / 1000);
            const other = Math.round((this.principalData.other || 0) / 1000);

            return {
                performance: {
                    current: performanceCurrent,
                    target: Math.round((this.principalData.performance_target || 0) / 1000),
                    percentage: Math.round(this.principalData.performance_finish_ratio || 0),
                    newSign,
                    newSignPercent: performanceCurrent > 0 ? Math.round((newSign / performanceCurrent) * 100) : 0,
                    renewal,
                    renewalPercent: performanceCurrent > 0 ? Math.round((renewal / performanceCurrent) * 100) : 0,
                    other,
                    otherPercent: performanceCurrent > 0 ? Math.round((other / performanceCurrent) * 100) : 0,
                },
                revenue: {
                    current: Math.round((this.principalData.revenue || 0) / 1000),
                    target: Math.round((this.principalData.revenue_target || 0) / 1000),
                    percentage: Math.round(this.principalData.revenue_finish_ratio || 0),
                },
                cost: {
                    current: Math.round((this.principalData.cost || 0) / 1000),
                    target: Math.round((this.principalData.cost_target || 0) / 1000),
                    percentage: Math.round(this.principalData.cost_finish_ratio || 0),
                },
            };
        },

        // 校区健康度数据
        healthData() {
            if (!this.principalData) {
                return {
                    netCashFlow: { value: 0, status: '健康', desc: '流入 > 流出' },
                    netProfit: { value: 0, status: '良好', desc: '利润率 0%' },
                    netGrowth: { value: 0, growth: '+0%', desc: '新增 > 流失' },
                };
            }
            return {
                netCashFlow: {
                    value: Math.round((this.principalData.net_cash_flow || 0) / 1000),
                    status: this.principalData.net_cash_flow_status || '健康',
                    desc: this.principalData.net_cash_flow_desc || '流入 > 流出',
                },
                netProfit: {
                    value: Math.round((this.principalData.net_profit || 0) / 1000),
                    status: this.principalData.net_profit_status || '良好',
                    desc: `利润率 ${this.principalData.profit_rate || 0}%`,
                },
                netGrowth: {
                    value: this.principalData.net_growth || 0,
                    growth: `+${this.principalData.growth_rate || 0}%`,
                    desc: this.principalData.net_growth_desc || '新增 > 流失',
                },
            };
        },
    },

    mounted() {
        this.currentTimeFilter.startDate = dayjs().format('YYYY-MM-DD');
        this.currentTimeFilter.endDate = dayjs().format('YYYY-MM-DD');
        this.initData();
    },

    methods: {
        // 初始化数据加载
        async initData() {
            if (this.isFirstLoad) {
                this.skeletonLoading = true;
            }

            await this.getData();
        },

        // 获取校长数据
        async getData() {
            try {
                const res = await getPrincipalList({
                    search_start: this.currentTimeFilter.startDate,
                    search_end: this.currentTimeFilter.endDate,
                    corp_user_id: 1,
                });
                console.log('校长数据:', res);

                // 处理接口返回的数据
                if (res && res.code === 0 && res.data) {
                    this.principalData = res.data;
                    console.log('处理后的校长数据:', this.principalData);
                }

                // 只有首次加载时才关闭骨架屏
                if (this.isFirstLoad) {
                    this.skeletonLoading = false;
                    this.isFirstLoad = false; // 标记首次加载完成
                }
            } catch (error) {
                console.error('获取校长数据失败:', error);

                // 发生错误时也要关闭骨架屏
                if (this.isFirstLoad) {
                    this.skeletonLoading = false;
                    this.isFirstLoad = false;
                }
                throw error;
            }
        },

        // 时间筛选变更事件
        async onTimeFilterChange(timeFilter) {
            const timeFilterStr = JSON.stringify(timeFilter);
            if (this.lastTimeFilter === timeFilterStr) {
                console.log('时间筛选未变化，跳过重复调用');
                return;
            }

            if (!timeFilter.startDate || !timeFilter.endDate) {
                return;
            }

            if (this.isFirstLoad) {
                return;
            }

            console.log('时间筛选变更:', timeFilter);
            this.lastTimeFilter = timeFilterStr;
            this.currentTimeFilter = timeFilter;

            await this.initData();
        },

        // 角色切换
        selectRole(roleKey) {
            this.selectedRole = roleKey;
            console.log('切换角色:', roleKey);
            // 这里可以添加角色切换的业务逻辑，比如跳转到对应页面
        },
    },
};
</script>

<style lang="less" scoped>
.principal {
    min-height: 100vh;
    background: #f8fafc;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', sans-serif;
}

// 头部信息
.header-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 48px;
    color: white;
    position: relative;

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .user-info {
        flex: 1;

        .system-title {
            font-size: 40px;
            font-weight: bold;
            margin: 0 0 16px 0;
        }

        .user-role {
            font-size: 28px;
            opacity: 0.9;
            margin: 0 0 8px 0;
        }

        .user-campus {
            font-size: 22px;
            margin: 0;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 16px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
                transform: translateY(-1px);
            }

            i {
                font-size: 18px;
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }
}

// 通用区块样式
.role-section,
.target-section,
.budget-section,
.health-section {
    background: white;
    margin-top: 1px;
    padding: 32px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;

    i {
        font-size: 32px;

        &.fa-users-cog {
            color: #8b5cf6;
        }

        &.fa-bullseye {
            color: #ef4444;
        }

        &.fa-calculator {
            color: #f59e0b;
        }

        &.fa-heartbeat {
            color: #ec4899;
        }
    }

    .section-title {
        font-weight: 600;
        color: #1f2937;
        font-size: 32px;
    }
}

// 角色切换模块
.role-buttons {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;

    &::-webkit-scrollbar {
        height: 4px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 2px;
    }
}

.role-btn {
    padding: 16px 32px;
    background: #f1f5f9;
    color: #64748b;
    border: none;
    border-radius: 16px;
    font-size: 28px;
    font-weight: 500;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background: #e2e8f0;
        transform: translateY(-2px);
    }

    &.active {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
    }
}

// 目标模块
.target-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.target-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
    }

    &.full-width {
        grid-column: 1 / -1;
    }
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.card-left {
    display: flex;
    align-items: center;
    gap: 24px;
}

.card-right {
    text-align: right;
}

.icon-wrapper {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    flex-shrink: 0;

    &.green {
        background: linear-gradient(135deg, #10b981, #047857);
        color: white;
    }

    &.blue {
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
    }

    &.purple {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
    }

    &.orange {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    &.teal {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
        color: white;
    }

    &.rose {
        background: linear-gradient(135deg, #f43f5e, #e11d48);
        color: white;
    }

    &.emerald {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }
}

.card-value {
    text-align: right;

    .value {
        font-size: 48px;
        font-weight: bold;
        color: #1f2937;
        line-height: 1;

        &.emerald {
            color: #059669;
        }

        &.blue {
            color: #1e40af;
        }

        &.purple {
            color: #7c3aed;
        }
    }

    .target {
        font-size: 24px;
        color: #6b7280;
        margin-top: 4px;
    }

    .status {
        font-size: 20px;
        margin-top: 4px;

        &.emerald {
            color: #059669;
        }

        &.blue {
            color: #1e40af;
        }
    }
}

.card-info {
    .value {
        font-size: 56px;
        font-weight: bold;
        color: #1f2937;
        line-height: 1;

        &.purple {
            color: #7c3aed;
        }
    }
}

.card-title {
    font-size: 28px;
    color: #6b7280;
    margin-bottom: 16px;
}

.target-info {
    font-size: 20px;
    color: #6b7280;
    margin-bottom: 8px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

.progress-bar {
    background: #e5e7eb;
    border-radius: 8px;
    height: 12px;
    flex: 1;
    overflow: hidden;

    &.small {
        width: 160px;
        flex: none;
    }

    .progress-fill {
        height: 100%;
        border-radius: 8px;
        transition: width 1.2s ease-out;

        &.green {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        &.blue {
            background: linear-gradient(90deg, #3b82f6, #1e40af);
        }

        &.purple {
            background: linear-gradient(90deg, #8b5cf6, #7c3aed);
        }

        &.orange {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        &.teal {
            background: linear-gradient(90deg, #14b8a6, #0d9488);
        }

        &.rose {
            background: linear-gradient(90deg, #f43f5e, #e11d48);
        }
    }
}

.progress-text {
    font-size: 24px;
    font-weight: 600;
    color: #374151;
}

// 预算完成度模块
.budget-main-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
                rgba(249, 115, 22, 0.03) 0%,
                rgba(239, 68, 68, 0.02) 50%,
                rgba(236, 72, 153, 0.03) 100%);
        border-radius: 24px;
        pointer-events: none;
    }

    .card-header,
    .card-title,
    .progress-bar,
    .progress-text,
    .performance-breakdown {
        position: relative;
        z-index: 1;
    }
}

.performance-breakdown {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-top: 24px;
}

.breakdown-item {
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(249, 115, 22, 0.1);
    border-radius: 16px;
    padding: 24px;
    text-align: center;

    .breakdown-label {
        font-size: 24px;
        color: #6b7280;
        margin-bottom: 8px;
    }

    .breakdown-value {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 4px;

        &.blue {
            color: #1e40af;
        }

        &.green {
            color: #059669;
        }

        &.purple {
            color: #7c3aed;
        }
    }

    .breakdown-percent {
        font-size: 20px;
        color: #6b7280;
    }
}

.budget-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.budget-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .icon-wrapper {
        margin-bottom: 24px;
    }

    .card-title {
        margin-bottom: 8px;
    }
}

// 校区健康度模块
.health-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.health-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    &.full-width {
        grid-column: 1 / -1;
    }

    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s;
    }

    &:hover::after {
        left: 100%;
    }
}

.card-desc {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 24px;
    color: #9ca3af;

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.emerald {
            background: #10b981;
        }

        &.blue {
            background: #3b82f6;
        }

        &.purple {
            background: #8b5cf6;
        }
    }
}

.growth-info {
    font-size: 20px;
    color: #8b5cf6;
    margin-bottom: 8px;
}
</style>
