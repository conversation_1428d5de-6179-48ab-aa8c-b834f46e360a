<template>
  <div class="skeleton-loader">
    <!-- 头部骨架 -->
    <div class="skeleton-header">
      <div class="skeleton-avatar"></div>
      <div class="skeleton-header-content">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
      </div>
    </div>

    <!-- 时间筛选骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-section-header">
        <div class="skeleton-icon"></div>
        <div class="skeleton-section-title"></div>
      </div>
      <div class="skeleton-time-buttons">
        <div v-for="i in 6" :key="i" class="skeleton-time-btn"></div>
      </div>
      <div class="skeleton-date-inputs">
        <div class="skeleton-date-input"></div>
        <div class="skeleton-date-input"></div>
      </div>
    </div>

    <!-- 目标模块骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-section-header">
        <div class="skeleton-icon"></div>
        <div class="skeleton-section-title"></div>
      </div>
      <div class="skeleton-target-grid">
        <div v-for="i in 4" :key="i" class="skeleton-target-card">
          <div class="skeleton-card-header">
            <div class="skeleton-card-icon"></div>
            <div class="skeleton-card-value">
              <div class="skeleton-value"></div>
              <div class="skeleton-target"></div>
            </div>
          </div>
          <div class="skeleton-card-title"></div>
          <div class="skeleton-progress-bar">
            <div class="skeleton-progress-track"></div>
            <div class="skeleton-progress-text"></div>
          </div>
        </div>
      </div>
      <!-- 收入卡片骨架 -->
      <div class="skeleton-revenue-card">
        <div class="skeleton-card-header">
          <div class="skeleton-card-icon large"></div>
          <div class="skeleton-card-value">
            <div class="skeleton-value large"></div>
            <div class="skeleton-target"></div>
          </div>
        </div>
        <div class="skeleton-card-title"></div>
        <div class="skeleton-progress-bar">
          <div class="skeleton-progress-track large"></div>
          <div class="skeleton-progress-text"></div>
        </div>
      </div>
    </div>

    <!-- 过程指标骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-section-header">
        <div class="skeleton-icon"></div>
        <div class="skeleton-section-title"></div>
      </div>
      <!-- 漏斗图骨架 -->
      <div class="skeleton-funnel-container">
        <div class="skeleton-funnel-chart"></div>
        <div class="skeleton-metrics-grid">
          <div v-for="i in 4" :key="i" class="skeleton-metric-card">
            <div class="skeleton-metric-header">
              <div class="skeleton-metric-dot"></div>
              <div class="skeleton-metric-name"></div>
            </div>
            <div class="skeleton-metric-value"></div>
            <div class="skeleton-metric-desc"></div>
          </div>
        </div>
      </div>
      <!-- 过程指标子项骨架 -->
      <div class="skeleton-process-items">
        <div v-for="i in 3" :key="i" class="skeleton-process-item">
          <div class="skeleton-item-content">
            <div class="skeleton-item-icon"></div>
            <div class="skeleton-item-info">
              <div class="skeleton-item-title"></div>
              <div class="skeleton-item-subtitle"></div>
            </div>
          </div>
          <div class="skeleton-item-progress">
            <div class="skeleton-progress-value"></div>
            <div class="skeleton-progress-mini"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热力值骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-section-header">
        <div class="skeleton-icon"></div>
        <div class="skeleton-section-title"></div>
      </div>
      <!-- 主热力值卡片骨架 -->
      <div class="skeleton-main-heat-card">
        <div class="skeleton-main-heat-icon"></div>
        <div class="skeleton-main-heat-content">
          <div class="skeleton-main-heat-title"></div>
          <div class="skeleton-main-heat-value"></div>
        </div>
      </div>
      <!-- 热力值详情骨架 -->
      <div class="skeleton-heat-detail-grid">
        <div v-for="i in 2" :key="i" class="skeleton-heat-detail-card">
          <div class="skeleton-heat-detail-title"></div>
          <div class="skeleton-heat-detail-value"></div>
          <div class="skeleton-heat-detail-rate"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SkeletonLoader',
};
</script>

<style lang="less" scoped>
.skeleton-loader {
  min-height: 100vh;
  background: #f8fafc;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

// 骨架屏基础样式
.skeleton-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8px;
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 头部骨架
.skeleton-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 48px;
  display: flex;
  align-items: center;
  gap: 24px;
}

.skeleton-avatar {
  width: 96px;
  height: 96px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.skeleton-header-content {
  flex: 1;
}

.skeleton-title {
  .skeleton-base();
  height: 40px;
  width: 60%;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.3);
}

.skeleton-subtitle {
  .skeleton-base();
  height: 28px;
  width: 40%;
  background: rgba(255, 255, 255, 0.2);
}

// 通用区块骨架
.skeleton-section {
  background: white;
  margin-top: 1px;
  padding: 32px;
}

.skeleton-section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.skeleton-icon {
  .skeleton-base();
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

.skeleton-section-title {
  .skeleton-base();
  height: 32px;
  width: 120px;
}

// 时间筛选骨架
.skeleton-time-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 8px;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 50px;
}

.skeleton-time-btn {
  .skeleton-base();
  flex: 1;
  height: 56px;
  border-radius: 42px;
}

.skeleton-date-inputs {
  display: flex;
  gap: 16px;
  align-items: center;
}

.skeleton-date-input {
  .skeleton-base();
  flex: 1;
  height: 56px;
  border-radius: 16px;
}

// 目标模块骨架
.skeleton-target-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.skeleton-target-card,
.skeleton-revenue-card {
  .skeleton-base();
  border-radius: 24px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.9);
}

.skeleton-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.skeleton-card-icon {
  .skeleton-base();
  width: 64px;
  height: 64px;
  border-radius: 16px;

  &.large {
    width: 80px;
    height: 80px;
  }
}

.skeleton-card-value {
  text-align: right;
}

.skeleton-value {
  .skeleton-base();
  height: 48px;
  width: 120px;
  margin-bottom: 4px;

  &.large {
    height: 56px;
    width: 160px;
  }
}

.skeleton-target {
  .skeleton-base();
  height: 24px;
  width: 80px;
}

.skeleton-card-title {
  .skeleton-base();
  height: 28px;
  width: 80%;
  margin-bottom: 16px;
}

.skeleton-progress-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.skeleton-progress-track {
  .skeleton-base();
  flex: 1;
  height: 16px;
  border-radius: 8px;

  &.large {
    height: 24px;
    border-radius: 12px;
  }
}

.skeleton-progress-text {
  .skeleton-base();
  width: 48px;
  height: 24px;
}

// 漏斗图骨架
.skeleton-funnel-container {
  .skeleton-base();
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.9);
}

.skeleton-funnel-chart {
  .skeleton-base();
  height: 400px;
  border-radius: 16px;
  margin-bottom: 32px;
}

.skeleton-metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.skeleton-metric-card {
  .skeleton-base();
  padding: 24px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.5);
}

.skeleton-metric-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.skeleton-metric-dot {
  .skeleton-base();
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.skeleton-metric-name {
  .skeleton-base();
  height: 24px;
  width: 80px;
}

.skeleton-metric-value {
  .skeleton-base();
  height: 28px;
  width: 60px;
  margin-bottom: 4px;
}

.skeleton-metric-desc {
  .skeleton-base();
  height: 20px;
  width: 50px;
}

// 过程指标子项骨架
.skeleton-process-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.skeleton-process-item {
  .skeleton-base();
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.9);
}

.skeleton-item-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.skeleton-item-icon {
  .skeleton-base();
  width: 80px;
  height: 80px;
  border-radius: 16px;
}

.skeleton-item-info {
  flex: 1;
}

.skeleton-item-title {
  .skeleton-base();
  height: 32px;
  width: 120px;
  margin-bottom: 4px;
}

.skeleton-item-subtitle {
  .skeleton-base();
  height: 28px;
  width: 80px;
}

.skeleton-item-progress {
  text-align: right;
}

.skeleton-progress-value {
  .skeleton-base();
  height: 36px;
  width: 80px;
  margin-bottom: 8px;
}

.skeleton-progress-mini {
  .skeleton-base();
  width: 128px;
  height: 16px;
  border-radius: 8px;
}

// 热力值骨架
.skeleton-main-heat-card {
  .skeleton-base();
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
  background: rgba(255, 255, 255, 0.9);
}

.skeleton-main-heat-icon {
  .skeleton-base();
  width: 96px;
  height: 96px;
  border-radius: 24px;
  flex-shrink: 0;
}

.skeleton-main-heat-content {
  flex: 1;
}

.skeleton-main-heat-title {
  .skeleton-base();
  height: 28px;
  width: 140px;
  margin-bottom: 8px;
}

.skeleton-main-heat-value {
  .skeleton-base();
  height: 56px;
  width: 180px;
}

.skeleton-heat-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.skeleton-heat-detail-card {
  .skeleton-base();
  border-radius: 24px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.9);
}

.skeleton-heat-detail-title {
  .skeleton-base();
  height: 24px;
  width: 100px;
  margin-bottom: 16px;
}

.skeleton-heat-detail-value {
  .skeleton-base();
  height: 40px;
  width: 120px;
  margin-bottom: 8px;
}

.skeleton-heat-detail-rate {
  .skeleton-base();
  height: 20px;
  width: 80px;
}
</style>
