<template>
  <div class="time-filter-section">
    <div class="section-header">
      <i class="fas fa-calendar-alt"></i>
      <span class="section-title">时间筛选</span>
    </div>
    <div class="time-buttons-container">
      <div class="time-buttons">
        <div
          class="active-indicator"
          :style="{
            transform: `translateX(${activeIndicatorPosition}px)`,
            width: `${activeIndicatorWidth}px`,
          }"
        ></div>
        <button
          v-for="(period, index) in timePeriods"
          :key="index"
          :ref="`timeBtn${index}`"
          :class="['time-btn', { active: activePeriod === period }]"
          @click="selectTimePeriod(period, index)"
        >
          {{ period }}
        </button>
      </div>
    </div>
    <div class="date-range">
      <input v-model="startDate" type="date" class="date-input" @change="onDateChange" />
      <span class="date-separator">至</span>
      <input v-model="endDate" type="date" class="date-input" @change="onDateChange" />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'TimeFilter',
  props: {
    // 默认选中的时间周期
    defaultPeriod: {
      type: String,
      default: '日',
    },
    // 默认开始日期
    defaultStartDate: {
      type: String,
      default: '',
    },
    // 默认结束日期
    defaultEndDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      timePeriods: ['日', '周', '月', '季', '半年', '年'],
      activePeriod: this.defaultPeriod,
      startDate: this.defaultStartDate || dayjs().format('YYYY-MM-DD'),
      endDate: this.defaultEndDate || dayjs().format('YYYY-MM-DD'),
      activeIndicatorPosition: 0,
      activeIndicatorWidth: 0,
    };
  },

  mounted() {
    // 初始化时根据默认周期设置日期范围
    if (!this.defaultStartDate || !this.defaultEndDate) {
      this.updateDateRange(this.activePeriod);
    }
    // 触发初始值事件
    // this.emitChange();
    // 初始化活动指示器位置
    this.$nextTick(() => {
      this.updateActiveIndicatorPosition();
    });
  },

  watch: {
    // 监听开始日期变化
    startDate(newVal) {
      if (newVal && this.endDate && newVal > this.endDate) {
        this.endDate = newVal;
      }
    },
  },

  methods: {
    // 选择时间周期
    selectTimePeriod(period, index) {
      this.activePeriod = period;
      this.updateDateRange(period);
      this.updateActiveIndicatorPosition(index);
      this.emitChange();
    },

    // 根据时间周期更新日期范围
    updateDateRange(period) {
      const now = dayjs();

      switch (period) {
        case '日': {
          this.startDate = now.format('YYYY-MM-DD');
          this.endDate = now.format('YYYY-MM-DD');
          break;
        }
        case '周': {
          // 获取本周的开始和结束日期（周一到周日）
          const weekStart = now.startOf('week').add(1, 'day'); // dayjs 默认周日为一周开始，这里调整为周一
          const weekEnd = now.endOf('week').add(1, 'day');
          this.startDate = weekStart.format('YYYY-MM-DD');
          this.endDate = weekEnd.format('YYYY-MM-DD');
          break;
        }
        case '月': {
          this.startDate = now.startOf('month').format('YYYY-MM-DD');
          this.endDate = now.endOf('month').format('YYYY-MM-DD');
          break;
        }
        case '季': {
          // 手动计算季度，确保准确性
          const currentMonth = now.month(); // 0-11
          const quarterStartMonth = Math.floor(currentMonth / 3) * 3;
          const quarterStart = now.month(quarterStartMonth).startOf('month');
          const quarterEnd = quarterStart.add(2, 'month').endOf('month');

          this.startDate = quarterStart.format('YYYY-MM-DD');
          this.endDate = quarterEnd.format('YYYY-MM-DD');
          break;
        }
        case '半年': {
          // 根据当前月份判断是上半年还是下半年
          const currentMonth = now.month(); // 0-11
          if (currentMonth < 6) {
            // 上半年：1-6月
            this.startDate = now.startOf('year').format('YYYY-MM-DD');
            this.endDate = now.startOf('year').add(5, 'month').endOf('month').format('YYYY-MM-DD');
          } else {
            // 下半年：7-12月
            this.startDate = now.startOf('year').add(6, 'month').format('YYYY-MM-DD');
            this.endDate = now.endOf('year').format('YYYY-MM-DD');
          }
          break;
        }
        case '年': {
          this.startDate = now.startOf('year').format('YYYY-MM-DD');
          this.endDate = now.endOf('year').format('YYYY-MM-DD');
          break;
        }
      }
    },

    // 日期手动更改时触发
    onDateChange() {
      this.validateDateRange();
      this.emitChange();
    },

    // 验证日期范围
    validateDateRange() {
      if (this.startDate && this.endDate && this.startDate > this.endDate) {
        this.endDate = this.startDate;
      }
    },

    // 触发变更事件
    emitChange() {
      this.$emit('change', {
        period: this.activePeriod,
        startDate: this.startDate,
        endDate: this.endDate,
      });
    },

    // 更新活动指示器位置
    updateActiveIndicatorPosition(targetIndex) {
      this.$nextTick(() => {
        let index = targetIndex;
        if (index === undefined) {
          // 如果没有传入索引，根据当前活动周期计算
          index = this.timePeriods.findIndex((period) => period === this.activePeriod);
        }

        if (index >= 0) {
          const buttons = this.$el.querySelectorAll('.time-btn');
          const container = this.$el.querySelector('.time-buttons');

          if (buttons[index] && container) {
            const button = buttons[index];

            // 由于按钮是 flex: 1 均分的，我们可以直接计算位置
            const buttonWidth = button.offsetWidth;
            const buttonLeft = button.offsetLeft;

            // 计算指示器位置（相对于容器内部）
            this.activeIndicatorPosition = buttonLeft;
            this.activeIndicatorWidth = buttonWidth;
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.time-filter-section {
  background: white;
  margin-top: 1px;
  padding: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;

  i {
    font-size: 32px;
    color: #3b82f6;
  }

  .section-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 32px;
  }
}

// 时间筛选
.time-buttons-container {
  margin-bottom: 24px;
  overflow-x: auto;
  padding-bottom: 8px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.time-buttons {
  position: relative;
  display: flex;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 50px;
  padding: 8px;
  width: 100%;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.active-indicator {
  position: absolute;
  top: 8px;
  left: 0px;
  height: calc(100% - 16px);
  width: 80px; // 默认宽度，会被动态设置覆盖
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 42px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  z-index: 1;
}

.time-btn {
  position: relative;
  z-index: 2;
  flex: 1; // 让按钮均分容器宽度
  padding: 12px 8px;
  border-radius: 42px;
  border: none;
  font-size: 28px;
  font-weight: 500;
  white-space: nowrap;
  background: transparent;
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  text-align: center;

  &:hover {
    color: #374151;
    transform: translateY(-1px);
  }

  &.active {
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.date-range {
  display: flex;
  align-items: center;
  gap: 16px;

  .date-input {
    flex: 1;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    font-size: 28px;

    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.1);
    }
  }

  .date-separator {
    color: #9ca3af;
    font-size: 28px;
  }
}
</style>
