<template>
  <span class="animated-number">{{ displayValue }}</span>
</template>

<script>
export default {
  name: 'AnimatedNumber',
  props: {
    // 目标数值
    value: {
      type: [Number, String],
      required: true,
    },
    // 动画持续时间（毫秒）
    duration: {
      type: Number,
      default: 1000,
    },
    // 小数位数
    decimals: {
      type: Number,
      default: 0,
    },
    // 是否自动开始动画
    autoStart: {
      type: Boolean,
      default: true,
    },
    // 延迟开始时间（毫秒）
    delay: {
      type: Number,
      default: 0,
    },
    // 前缀（如 ¥ 符号）
    prefix: {
      type: String,
      default: '',
    },
    // 后缀（如 % 符号）
    suffix: {
      type: String,
      default: '',
    },
    // 是否使用千分位分隔符
    useGrouping: {
      type: Boolean,
      default: false,
    },
    // 是否启用视窗滚动检测
    useIntersectionObserver: {
      type: Boolean,
      default: true,
    },
    // 视窗检测的根边距（提前多少像素开始动画）
    rootMargin: {
      type: String,
      default: '0px 0px -100px 0px',
    },
    // 视窗检测的阈值
    threshold: {
      type: Number,
      default: 0.1,
    },
  },
  data() {
    return {
      displayValue: '',
      currentValue: 0,
      animationId: null,
      timeoutId: null,
      intersectionObserver: null,
      hasAnimated: false, // 标记是否已经动画过
    };
  },
  computed: {
    // 目标数值（转换为数字）
    targetValue() {
      const num = parseFloat(this.value);
      return isNaN(num) ? 0 : num;
    },
  },
  watch: {
    value: {
      handler() {
        if (this.autoStart) {
          this.startAnimation();
        }
      },
      immediate: false,
    },
  },
  mounted() {
    if (this.useIntersectionObserver && this.autoStart) {
      this.initIntersectionObserver();
    } else if (this.autoStart) {
      this.startAnimation();
    } else {
      this.updateDisplay(this.targetValue);
    }
  },
  beforeDestroy() {
    this.clearAnimation();
    this.destroyIntersectionObserver();
  },
  methods: {
    // 开始动画
    startAnimation() {
      this.clearAnimation();

      if (this.delay > 0) {
        this.timeoutId = setTimeout(() => {
          this.animate();
        }, this.delay);
      } else {
        this.animate();
      }
    },

    // 执行动画
    animate() {
      const startValue = this.currentValue;
      const endValue = this.targetValue;
      const startTime = Date.now();
      const duration = this.duration;

      const step = () => {
        const now = Date.now();
        const progress = Math.min((now - startTime) / duration, 1);

        // 使用缓动函数（ease-out）
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        this.currentValue = startValue + (endValue - startValue) * easeProgress;
        this.updateDisplay(this.currentValue);

        if (progress < 1) {
          this.animationId = requestAnimationFrame(step);
        } else {
          this.currentValue = endValue;
          this.updateDisplay(endValue);
          this.animationId = null;
        }
      };

      this.animationId = requestAnimationFrame(step);
    },

    // 更新显示值
    updateDisplay(value) {
      let formattedValue = value.toFixed(this.decimals);

      // 添加千分位分隔符
      if (this.useGrouping) {
        const parts = formattedValue.split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = parts.join('.');
      }

      this.displayValue = this.prefix + formattedValue + this.suffix;
    },

    // 清除动画
    clearAnimation() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.timeoutId = null;
      }
    },

    // 重置动画
    reset() {
      this.clearAnimation();
      this.currentValue = 0;
      this.updateDisplay(0);
    },

    // 手动触发动画
    trigger() {
      this.startAnimation();
    },

    // 初始化 Intersection Observer
    initIntersectionObserver() {
      // 检查浏览器是否支持 Intersection Observer
      if (!window.IntersectionObserver) {
        // 不支持则直接开始动画
        this.startAnimation();
        return;
      }

      const options = {
        root: null, // 使用视窗作为根
        rootMargin: this.rootMargin,
        threshold: this.threshold,
      };

      this.intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !this.hasAnimated) {
            // 元素进入视窗且未动画过，开始动画
            this.hasAnimated = true;
            this.startAnimation();
            // 动画开始后可以停止观察（可选）
            // this.intersectionObserver.unobserve(entry.target);
          }
        });
      }, options);

      // 开始观察当前元素
      this.intersectionObserver.observe(this.$el);
    },

    // 销毁 Intersection Observer
    destroyIntersectionObserver() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
        this.intersectionObserver = null;
      }
    },

    // 重置动画状态（包括视窗检测状态）
    resetWithIntersection() {
      this.hasAnimated = false;
      this.reset();
      if (this.useIntersectionObserver) {
        this.destroyIntersectionObserver();
        this.initIntersectionObserver();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.animated-number {
  display: inline-block;
  font-variant-numeric: tabular-nums;
  transition: color 0.3s ease;
  color: inherit; // 确保继承父元素的颜色
  font-size: inherit; // 确保继承父元素的字体大小
  font-weight: inherit; // 确保继承父元素的字体粗细
}
</style>
