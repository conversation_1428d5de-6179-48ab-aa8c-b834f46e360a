<template>
  <div class="instructor">
    <!-- 加载状态 -->
    <SkeletonLoader v-if="skeletonLoading" />

    <!-- 正常内容 -->
    <div v-else class="content">
      <!-- 头部信息 -->
      <div class="header-section">
        <div class="header-content">
          <div class="user-info">
            <h1 class="system-title">聂道人力动态驱动系统</h1>
            <p class="user-role">才佳林 · 老师</p>
            <p class="user-campus"><i class="fas fa-building"></i>北京刘家窑</p>
          </div>
        </div>
      </div>

      <!-- 时间筛选模块 -->
      <TimeFilter @change="onTimeFilterChange" />

      <!-- 目标模块 -->
      <div class="target-section">
        <div class="section-header">
          <i class="fas fa-bullseye"></i>
          <span class="section-title">目标</span>
        </div>
        <div class="target-grid">
          <!-- 应出勤人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper blue">
                <i class="fas fa-user-check"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="targetData.attendancePeople.value" :delay="200"
                    :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                </div>
                <div class="unit">人</div>
              </div>
            </div>
            <div class="card-title">应出勤人数</div>
            <div class="card-desc">
              <div class="desc-dot blue"></div>
              <span>天工实际排课人数</span>
            </div>
          </div>

          <!-- 试听课应转化人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper green">
                <i class="fas fa-exchange-alt"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="targetData.trialConversionPeople.value" :delay="400"
                    :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                </div>
                <div class="unit">人</div>
              </div>
            </div>
            <div class="card-title">试听课应转化人数</div>
            <div class="card-desc">
              <div class="desc-dot green"></div>
              <span>天工中实际排试听课人数</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 过程指标模块 -->
      <div class="process-section">
        <div class="section-header">
          <i class="fas fa-chart-line"></i>
          <span class="section-title">过程指标</span>
        </div>

        <div class="process-grid">
          <!-- 实际出勤人数 -->
          <div class="process-card">
            <div class="process-icon blue">
              <i class="fas fa-users"></i>
            </div>
            <div class="process-content">
              <div class="process-title">实际出勤人数</div>
              <div class="process-value">
                <AnimatedNumber :value="processData.actualAttendance.value" :delay="600"
                  :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
              </div>
              <div class="process-unit">人</div>
            </div>
          </div>

          <!-- 已完成试听转化人数 -->
          <div class="process-card">
            <div class="process-icon green">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="process-content">
              <div class="process-title">已完成试听转化</div>
              <div class="process-value">
                <AnimatedNumber :value="processData.completedTrialConversion.value" :delay="800"
                  :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
              </div>
              <div class="process-unit">人</div>
            </div>
          </div>

          <!-- 流失人数 -->
          <div class="process-card">
            <div class="process-icon red">
              <i class="fas fa-user-times"></i>
            </div>
            <div class="process-content">
              <div class="process-title">流失人数</div>
              <div class="process-value">
                <AnimatedNumber :value="processData.lostStudents.value" :delay="1000" :use-intersection-observer="true"
                  root-margin="0px 0px -50px 0px" />
              </div>
              <div class="process-unit">人</div>
            </div>
          </div>

          <!-- 课程总结&作业 -->
          <div class="process-card">
            <div class="process-icon purple">
              <i class="fas fa-clipboard-check"></i>
            </div>
            <div class="process-content">
              <div class="process-title">课程总结&作业</div>
              <div class="process-value">
                <AnimatedNumber :value="processData.courseSummary.value" :delay="1200" :use-intersection-observer="true"
                  root-margin="0px 0px -50px 0px" />
              </div>
              <div class="process-unit">份</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热力值模块 -->
      <div class="heat-section">
        <div class="section-header">
          <i class="fas fa-coins"></i>
          <span class="section-title">热力值</span>
        </div>

        <!-- 实际热力值汇总 -->
        <div class="main-heat-card">
          <div class="heat-bg-decoration"></div>
          <div class="heat-bg-circle"></div>

          <div class="heat-content">
            <div class="heat-header">
              <div class="heat-icon">
                <i class="fas fa-fire"></i>
              </div>
              <div class="heat-info">
                <div class="heat-title">本月实际热力值</div>
                <div class="heat-value">
                  <AnimatedNumber :value="heatData.totalValue" prefix="¥" :use-grouping="true" :delay="1400"
                    :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                </div>
              </div>
            </div>

            <!-- 热力值组成明细 -->
            <div class="heat-detail-grid">
              <div class="heat-detail-card">
                <div class="heat-detail-header">
                  <span class="heat-detail-title">课消热力值</span>
                  <span class="heat-detail-value blue">
                    <AnimatedNumber :value="heatData.courseConsumption" prefix="¥" :use-grouping="true" :delay="1600"
                      :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                  </span>
                </div>
                <div class="heat-detail-rate">占比 {{ heatData.courseConsumptionRate }}%</div>
              </div>
              <div class="heat-detail-card">
                <div class="heat-detail-header">
                  <span class="heat-detail-title">试听课热力值</span>
                  <span class="heat-detail-value green">
                    <AnimatedNumber :value="heatData.trialCourse" prefix="¥" :use-grouping="true" :delay="1800"
                      :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                  </span>
                </div>
                <div class="heat-detail-rate">占比 {{ heatData.trialCourseRate }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TimeFilter from './common/TimeFilter.vue';
import AnimatedNumber from './common/AnimatedNumber.vue';
import SkeletonLoader from './common/SkeletonLoader.vue';
import { getInstructorList } from '@/api/humanPoweredSys';
import dayjs from 'dayjs';

export default {
  name: 'Instructor',
  components: {
    TimeFilter,
    AnimatedNumber,
    SkeletonLoader,
  },
  data() {
    return {
      // 时间筛选相关
      defaultTimePeriod: '日',
      currentTimeFilter: {
        period: '日',
        startDate: '',
        endDate: '',
      },

      // 页面状态
      skeletonLoading: true,
      isFirstLoad: true,
      lastTimeFilter: null,

      // 接口数据
      instructorData: null,
    };
  },

  computed: {
    // 目标数据
    targetData() {
      if (!this.instructorData) {
        return {
          attendancePeople: { value: 0 },
          trialConversionPeople: { value: 0 },
        };
      }
      return {
        attendancePeople: { value: this.instructorData.should_attendance_people || 0 },
        trialConversionPeople: { value: this.instructorData.should_transfer_people || 0 },
      };
    },

    // 过程指标数据
    processData() {
      if (!this.instructorData) {
        return {
          actualAttendance: { value: 0 },
          completedTrialConversion: { value: 0 },
          lostStudents: { value: 0 },
          courseSummary: { value: 0 },
        };
      }
      return {
        actualAttendance: { value: this.instructorData.attendance_people || 0 },
        completedTrialConversion: { value: this.instructorData.transfer_people || 0 },
        lostStudents: { value: this.instructorData.churn_people || 0 },
        courseSummary: { value: this.instructorData.course_work || 0 },
      };
    },

    // 热力值数据
    heatData() {
      if (!this.instructorData) {
        return {
          totalValue: 0,
          courseConsumption: 0,
          trialCourse: 0,
          courseConsumptionRate: 0,
          trialCourseRate: 0,
        };
      }

      const totalValue = this.instructorData.actual_hot_value || 0;
      const courseConsumption = this.instructorData.deduct_hot_value || 0;
      const trialCourse = this.instructorData.audition_hot_value || 0;

      // 计算占比
      const courseConsumptionRate =
        totalValue > 0 ? Math.round((courseConsumption / totalValue) * 100 * 100) / 100 : 0;
      const trialCourseRate =
        totalValue > 0 ? Math.round((trialCourse / totalValue) * 100 * 100) / 100 : 0;

      return {
        totalValue,
        courseConsumption,
        trialCourse,
        courseConsumptionRate,
        trialCourseRate,
      };
    },
  },

  mounted() {
    this.currentTimeFilter.startDate = dayjs().format('YYYY-MM-DD');
    this.currentTimeFilter.endDate = dayjs().format('YYYY-MM-DD');
    this.initData();
  },

  methods: {
    // 初始化数据加载
    async initData() {
      if (this.isFirstLoad) {
        this.skeletonLoading = true;
      }

      await this.getData();
    },

    // 获取授课老师数据
    async getData() {
      try {
        const res = await getInstructorList({
          search_start: this.currentTimeFilter.startDate,
          search_end: this.currentTimeFilter.endDate,
          corp_user_id: 2,
        });
        console.log('授课老师数据:', res);

        // 处理接口返回的数据
        if (res && res.code === 0 && res.data) {
          this.instructorData = res.data;
          console.log('处理后的授课老师数据:', this.instructorData);
        }

        // 只有首次加载时才关闭骨架屏
        if (this.isFirstLoad) {
          this.skeletonLoading = false;
          this.isFirstLoad = false; // 标记首次加载完成
        }
      } catch (error) {
        console.error('获取授课老师数据失败:', error);

        // 发生错误时也要关闭骨架屏
        if (this.isFirstLoad) {
          this.skeletonLoading = false;
          this.isFirstLoad = false;
        }
        throw error;
      }
    },

    // 时间筛选变更事件
    async onTimeFilterChange(timeFilter) {
      const timeFilterStr = JSON.stringify(timeFilter);
      if (this.lastTimeFilter === timeFilterStr) {
        console.log('时间筛选未变化，跳过重复调用');
        return;
      }

      if (!timeFilter.startDate || !timeFilter.endDate) {
        return;
      }

      if (this.isFirstLoad) {
        return;
      }

      console.log('时间筛选变更:', timeFilter);
      this.lastTimeFilter = timeFilterStr;
      this.currentTimeFilter = timeFilter;

      await this.initData();
    },
  },
};
</script>

<style lang="less" scoped>
.instructor {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', sans-serif;
}

// 头部信息
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  color: white;
  position: relative;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    flex: 1;

    .system-title {
      font-size: 40px;
      font-weight: bold;
      margin: 0 0 16px 0;
    }

    .user-role {
      font-size: 28px;
      opacity: 0.9;
      margin: 0 0 8px 0;
    }

    .user-campus {
      font-size: 22px;
      margin: 0;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 8px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      i {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

// 通用区块样式
.target-section,
.process-section,
.heat-section {
  background: white;
  margin-top: 1px;
  padding: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;

  i {
    font-size: 32px;

    &.fa-bullseye {
      color: #ef4444;
    }

    &.fa-chart-line {
      color: #3b82f6;
    }

    &.fa-coins {
      color: #eab308;
    }
  }

  .section-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 32px;
  }
}

// 目标模块
.target-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.target-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;

  &.blue {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
  }

  &.green {
    background: linear-gradient(135deg, #10b981, #047857);
    color: white;
  }
}

.card-value {
  text-align: right;

  .value {
    font-size: 48px;
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
  }

  .unit {
    font-size: 24px;
    color: #6b7280;
    margin-top: 4px;
  }
}

.card-title {
  font-size: 28px;
  color: #6b7280;
  margin-bottom: 16px;
}

.card-desc {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  color: #9ca3af;

  .desc-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.blue {
      background: #3b82f6;
    }

    &.green {
      background: #10b981;
    }
  }
}

// 过程指标模块
.process-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.process-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.process-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 24px;

  &.blue {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
  }

  &.green {
    background: linear-gradient(135deg, #10b981, #047857);
    color: white;
  }

  &.red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
  }

  &.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
  }
}

.process-content {
  .process-title {
    font-size: 28px;
    color: #6b7280;
    margin-bottom: 8px;
  }

  .process-value {
    font-size: 56px;
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
  }

  .process-unit {
    font-size: 24px;
    color: #6b7280;
    margin-top: 4px;
  }
}

// 热力值模块
.main-heat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  position: relative;
  overflow: hidden;
}

.heat-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.03) 0%,
      rgba(99, 102, 241, 0.02) 50%,
      rgba(139, 92, 246, 0.03) 100%);
  border-radius: 24px;
  pointer-events: none;
}

.heat-bg-circle {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 160px;
  height: 160px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
  border-radius: 50%;
  filter: blur(40px);
  pointer-events: none;
}

.heat-content {
  position: relative;
  z-index: 1;
}

.heat-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
}

.heat-icon {
  width: 112px;
  height: 112px;
  border-radius: 32px;
  background: linear-gradient(135deg, #f97316, #ef4444);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: white;
  flex-shrink: 0;
}

.heat-info {
  flex: 1;

  .heat-title {
    font-size: 28px;
    color: #6b7280;
    margin-bottom: 8px;
  }

  .heat-value {
    font-size: 72px;
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
  }
}

.heat-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.heat-detail-card {
  background: rgba(255, 255, 255, 0.6);
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.heat-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .heat-detail-title {
    font-size: 24px;
    color: #6b7280;
  }

  .heat-detail-value {
    font-size: 28px;
    font-weight: bold;

    &.blue {
      color: #1e40af;
    }

    &.green {
      color: #047857;
    }
  }
}

.heat-detail-rate {
  font-size: 20px;
  color: #9ca3af;
}
</style>
