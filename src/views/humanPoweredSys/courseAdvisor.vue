<template>
  <div class="course-advisor">
    <!-- 加载状态 -->
    <SkeletonLoader v-if="skeletonLoading" />

    <!-- 正常内容 -->
    <div v-else class="content">
      <!-- 头部信息 -->
      <div class="header-section">
        <div class="header-content">
          <div class="user-info">
            <h1 class="system-title">聂道人力动态驱动系统</h1>
            <p class="user-role">才佳林 · 课程顾问</p>
            <p class="user-campus"><i class="fas fa-building"></i>北京刘家窑</p>
          </div>
          <!-- <div class="user-avatar">
            <i class="fas fa-user-cog"></i>
          </div> -->
        </div>
      </div>

      <!-- 时间筛选模块 -->
      <TimeFilter @change="onTimeFilterChange" />

      <!-- 目标模块 -->
      <div class="target-section">
        <div class="section-header">
          <i class="fas fa-bullseye"></i>
          <span class="section-title">目标</span>
        </div>
        <div class="target-grid">
          <!-- 正课新招人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper blue">
                <i class="fas fa-users"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="normalNewData.value" :delay="200" :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">/ {{ normalNewData.target }}人</div>
              </div>
            </div>
            <div class="card-title">正课新招人数</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill blue" :style="{ width: normalNewData.percentage + '%' }"></div>
              </div>
              <span class="progress-text blue">{{ normalNewData.percentage }}%</span>
            </div>
          </div>

          <!-- 到店试听人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper green">
                <i class="fas fa-store"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="visitTryListenData.value" :delay="400" :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">/ {{ visitTryListenData.target }}人</div>
              </div>
            </div>
            <div class="card-title">到店试听人数</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill green" :style="{ width: visitTryListenData.percentage + '%' }"></div>
              </div>
              <span class="progress-text green">{{ visitTryListenData.percentage }}%</span>
            </div>
          </div>

          <!-- 当月试听转化率 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper purple">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="Math.round(advisorData?.try_listen_transfer_ratio || 0)" :delay="600"
                    :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">
                  / {{ Math.round(advisorData?.try_listen_transfer_ratio_target || 0) }}%
                </div>
              </div>
            </div>
            <div class="card-title">试听转化率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill purple" :style="{
                  width: Math.round(advisorData?.try_listen_transfer_finish_ratio || 0) + '%',
                }"></div>
              </div>
              <span class="progress-text purple">{{ Math.round(advisorData?.try_listen_transfer_finish_ratio || 0)
                }}%</span>
            </div>
          </div>

          <!-- 签单率 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper orange">
                <i class="fas fa-handshake"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="signOrderRateData.value" :delay="800" :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">/ {{ signOrderRateData.target }}%</div>
              </div>
            </div>
            <div class="card-title">签单率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill orange" :style="{ width: signOrderRateData.percentage + '%' }"></div>
              </div>
              <span class="progress-text orange">{{ signOrderRateData.percentage }}%</span>
            </div>
          </div>
        </div>

        <!-- 新签流水金额 -->
        <div class="revenue-card">
          <div class="card-header">
            <div class="icon-wrapper gradient">
              <i class="fas fa-yen-sign"></i>
            </div>
            <div class="card-value">
              <div class="value">
                <AnimatedNumber :value="signNewData.value" prefix="¥" :use-grouping="true" :delay="1000"
                  :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
              </div>
              <div class="target">/ ¥{{ signNewData.target.toLocaleString() }}</div>
            </div>
          </div>
          <div class="card-title">新签流水金额</div>
          <div class="progress-bar">
            <div class="progress-track large">
              <div class="progress-fill gradient" :style="{ width: signNewData.percentage + '%' }"></div>
            </div>
            <span class="progress-text gradient">{{ signNewData.percentage }}%</span>
          </div>
        </div>
      </div>

      <!-- 过程指标模块 -->
      <div class="process-section">
        <div class="section-header">
          <i class="fas fa-funnel-dollar"></i>
          <span class="section-title">过程指标</span>
        </div>

        <!-- ECharts 漏斗图 -->
        <div class="funnel-chart-container">
          <div ref="funnelChart" class="funnel-chart"></div>

          <!-- 转化率指标 -->
          <div class="conversion-metrics">
            <div class="metric-card blue">
              <div class="metric-header">
                <div class="metric-dot"></div>
                <div class="metric-name">邀约率</div>
              </div>
              <div class="metric-value">{{ inviteRateData.value }}%</div>
              <div class="metric-desc">试听/有效</div>
            </div>
            <div class="metric-card green">
              <div class="metric-header">
                <div class="metric-dot"></div>
                <div class="metric-name">试听转化率</div>
              </div>
              <div class="metric-value">
                {{ Math.round(advisorData?.try_listen_transfer_ratio || 0) }}%
              </div>
              <div class="metric-desc">新招/试听</div>
            </div>
            <div class="metric-card purple">
              <div class="metric-header">
                <div class="metric-dot"></div>
                <div class="metric-name">渠道转化率</div>
              </div>
              <div class="metric-value">
                {{ Math.round(advisorData?.channel_transfer_ratio || 0) }}%
              </div>
              <div class="metric-desc">新招/有效</div>
            </div>
            <div class="metric-card orange">
              <div class="metric-header">
                <div class="metric-dot"></div>
                <div class="metric-name">当月转化率</div>
              </div>
              <div class="metric-value">
                {{ Math.round(advisorData?.month_transfer_ratio || 0) }}%
              </div>
              <div class="metric-desc">当月/试听</div>
            </div>
          </div>
        </div>

        <!-- 过程指标子项 -->
        <div class="process-items">
          <!-- 电话沟通人数 -->
          <div class="process-item">
            <div class="item-content">
              <div class="item-icon blue">
                <i class="fas fa-phone"></i>
              </div>
              <div class="item-info">
                <div class="item-title">电话沟通人数</div>
                <div class="item-subtitle">
                  {{ processItemsData.communicationPeople.value }}/{{
                    processItemsData.communicationPeople.target
                  }}人
                </div>
              </div>
            </div>
            <div class="item-progress">
              <div class="progress-value blue">
                {{ processItemsData.communicationPeople.percentage }}%
              </div>
              <div class="progress-mini">
                <div class="progress-mini-fill blue"
                  :style="{ width: processItemsData.communicationPeople.percentage + '%' }"></div>
              </div>
            </div>
          </div>

          <!-- 电话沟通时长 -->
          <div class="process-item">
            <div class="item-content">
              <div class="item-icon green">
                <i class="fas fa-clock"></i>
              </div>
              <div class="item-info">
                <div class="item-title">电话沟通时长</div>
                <div class="item-subtitle">
                  {{ processItemsData.communicationDuration.value }}/{{
                    processItemsData.communicationDuration.target
                  }}小时
                </div>
              </div>
            </div>
            <div class="item-progress">
              <div class="progress-value green">
                {{ processItemsData.communicationDuration.percentage }}%
              </div>
              <div class="progress-mini">
                <div class="progress-mini-fill green"
                  :style="{ width: processItemsData.communicationDuration.percentage + '%' }"></div>
              </div>
            </div>
          </div>

          <!-- 客户跟进人数 -->
          <div class="process-item">
            <div class="item-content">
              <div class="item-icon purple">
                <i class="fas fa-user-check"></i>
              </div>
              <div class="item-info">
                <div class="item-title">客户跟进人数</div>
                <div class="item-subtitle">
                  {{ processItemsData.followPeople.value }}/{{
                    processItemsData.followPeople.target
                  }}人
                </div>
              </div>
            </div>
            <div class="item-progress">
              <div class="progress-value purple">
                {{ processItemsData.followPeople.percentage }}%
              </div>
              <div class="progress-mini">
                <div class="progress-mini-fill purple"
                  :style="{ width: processItemsData.followPeople.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热力值模块 -->
      <div class="heat-section">
        <div class="section-header">
          <i class="fas fa-coins"></i>
          <span class="section-title">热力值</span>
        </div>

        <!-- 本月实际热力值 -->
        <div class="main-heat-card">
          <div class="main-heat-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="main-heat-content">
            <div class="main-heat-title">本月实际热力值</div>
            <div class="main-heat-value">
              <AnimatedNumber :value="heatValueData.main" prefix="¥" :use-grouping="true" :delay="200"
                :use-intersection-observer="true" root-margin="0px 0px 200px 0px" :threshold="0" />
            </div>
          </div>
        </div>

        <!-- 热力值详情 -->
        <div class="heat-detail-grid">
          <!-- 到店试听热力值 -->
          <div class="heat-detail-card">
            <div class="heat-detail-title">到店试听热力值</div>
            <div class="heat-detail-value green">
              <AnimatedNumber :value="heatValueData.visitTryListen" prefix="¥" :use-grouping="true" :delay="400"
                :use-intersection-observer="true" root-margin="0px 0px 200px 0px" :threshold="0" />
            </div>
            <div class="heat-detail-rate">
              占比
              {{
                heatValueData.main > 0
                  ? Math.round((heatValueData.visitTryListen / heatValueData.main) * 100)
                  : 0
              }}%
            </div>
          </div>

          <!-- 新招热力值 -->
          <div class="heat-detail-card">
            <div class="heat-detail-title">新招热力值</div>
            <div class="heat-detail-value purple">
              <AnimatedNumber :value="heatValueData.normalNew" prefix="¥" :use-grouping="true" :delay="600"
                :use-intersection-observer="true" root-margin="0px 0px 200px 0px" :threshold="0" />
            </div>
            <div class="heat-detail-rate">
              占比
              {{
                heatValueData.main > 0
                  ? Math.round((heatValueData.normalNew / heatValueData.main) * 100)
                  : 0
              }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TimeFilter from './common/TimeFilter.vue';
import AnimatedNumber from './common/AnimatedNumber.vue';
import SkeletonLoader from './common/SkeletonLoader.vue';
import { getAdvisorList, getAdvisorVoipList } from '@/api/humanPoweredSys';
import dayjs from 'dayjs';

export default {
  name: 'CourseAdvisor',
  components: {
    TimeFilter,
    AnimatedNumber,
    SkeletonLoader,
  },
  data() {
    return {
      // 时间筛选相关
      defaultTimePeriod: '日',

      currentTimeFilter: {
        period: '日',
        startDate: '',
        endDate: '',
      },

      // 漏斗图实例
      funnelChart: null,

      // 动画定时器数组
      animationTimers: [],

      // 进度条视窗检测器
      progressObserver: null,

      // 页面状态
      skeletonLoading: true,

      // 是否首次加载
      isFirstLoad: true,

      // 防止重复调用的标志
      lastTimeFilter: null,

      // 接口数据
      advisorData: null,
      voipData: null,
    };
  },

  computed: {
    // 有效人数相关数据
    validPeopleData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: this.advisorData.valid_people || 0,
        target: this.advisorData.valid_people_target || 0,
        percentage: Math.round(this.advisorData.valid_people_finish_ratio || 0),
      };
    },

    // 邀约率相关数据
    inviteRateData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: Math.round(this.advisorData.invite_ratio || 0),
        target: Math.round(this.advisorData.invite_target || 0),
        percentage: Math.round(this.advisorData.invite_finish_ratio || 0),
      };
    },

    // 到店试听相关数据
    visitTryListenData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: this.advisorData.visit_try_listen_people || 0,
        target: this.advisorData.visit_try_listen_target || 0,
        percentage: Math.round(this.advisorData.visit_try_listen_finish_ratio || 0),
      };
    },

    // 签单率相关数据
    signOrderRateData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: Math.round(this.advisorData.sign_order_ratio || 0),
        target: Math.round(this.advisorData.sign_order_target || 0),
        percentage: Math.round(this.advisorData.sign_order_finish_ratio || 0),
      };
    },

    // 新签流水相关数据
    signNewData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: this.advisorData.sign_new_price || 0,
        target: this.advisorData.sign_new_target || 0,
        percentage: Math.round(this.advisorData.sign_new_finish_ratio || 0),
      };
    },

    // 正课新招相关数据
    normalNewData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: this.advisorData.normal_new_people || 0,
        target: this.advisorData.normal_new_target || 0,
        percentage: Math.round(this.advisorData.normal_new_finish_ratio || 0),
      };
    },

    // 当月试听转化相关数据
    monthTryListenTransferData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: this.advisorData.month_try_listen_transfer_people || 0,
        target: this.advisorData.month_try_listen_transfer_target || 0,
        percentage: Math.round((this.advisorData.month_try_listen_transfer_finish || 0) * 100),
      };
    },

    // 邀约率相关数据
    inviteRatePercentData() {
      if (!this.advisorData) return { value: 0, target: 0, percentage: 0 };
      return {
        value: Math.round((this.advisorData.invite_ratio || 0) * 100),
        target: Math.round((this.advisorData.invite_target || 0) * 100),
        percentage: Math.round((this.advisorData.invite_finish_ratio || 0) * 100),
      };
    },

    // 过程指标数据
    processMetricsData() {
      if (!this.advisorData)
        return {
          validPeople: 0,
          inviteRatio: 0,
          visitTryListen: 0,
          signOrderRatio: 0,
        };
      return {
        validPeople: this.advisorData.valid_people || 0,
        inviteRatio: Math.round((this.advisorData.invite_ratio || 0) * 100),
        visitTryListen: this.advisorData.visit_try_listen_people || 0,
        signOrderRatio: Math.round((this.advisorData.sign_order_ratio || 0) * 100),
      };
    },

    // 过程指标子项数据（使用通话数据）
    processItemsData() {
      if (!this.voipData)
        return {
          communicationPeople: { value: 0, target: 0, percentage: 0 },
          communicationDuration: { value: 0, target: 0, percentage: 0 },
          followPeople: { value: 0, target: 0, percentage: 0 },
        };
      return {
        // 电话沟通人数
        communicationPeople: {
          value: this.voipData.communication_people || 0,
          target: this.voipData.communication_people_target || 0,
          percentage: Math.round(this.voipData.communication_people_finish_ratio || 0),
        },
        // 电话沟通时长
        communicationDuration: {
          value: this.voipData.communication_duration || 0,
          target: this.voipData.communication_duration_target || 0,
          percentage: Math.round(this.voipData.communication_duration_finish || 0),
        },
        // 客户跟进人数
        followPeople: {
          value: this.voipData.follow_people || 0,
          target: this.voipData.follow_people_target || 0,
          percentage: Math.round(this.voipData.follow_people_finish_ratio || 0),
        },
      };
    },

    // 漏斗图数据（动态计算）
    funnelData() {
      if (!this.advisorData) {
        return [
          { name: '有效人数', value: 0, target: 0, percentage: 0 },
          { name: '到店试听', value: 0, target: 0, percentage: 0 },
          { name: '正课新招', value: 0, target: 0, percentage: 0 },
          { name: '签单成功', value: 0, target: 0, percentage: 0 },
        ];
      }

      return [
        {
          name: '有效人数',
          value: this.advisorData.valid_people || 0,
          target: this.advisorData.valid_people_target || 0,
          percentage: Math.round(this.advisorData.valid_people_finish_ratio || 0),
        },
        {
          name: '到店试听',
          value: this.advisorData.visit_try_listen_people || 0,
          target: this.advisorData.visit_try_listen_target || 0,
          percentage: Math.round(this.advisorData.visit_try_listen_finish_ratio || 0),
        },
        {
          name: '正课新招',
          value: this.advisorData.normal_new_people || 0,
          target: this.advisorData.normal_new_target || 0,
          percentage: Math.round(this.advisorData.normal_new_finish_ratio || 0),
        },
        {
          name: '签单成功',
          value: Math.round((this.advisorData.sign_new_price || 0) / 1000), // 转换为千元单位显示
          target: Math.round((this.advisorData.sign_new_target || 0) / 1000),
          percentage: Math.round(this.advisorData.sign_new_finish_ratio || 0),
        },
      ];
    },

    // 热力值相关数据
    heatValueData() {
      if (!this.advisorData)
        return {
          main: 0,
          visitTryListen: 0,
          normalNew: 0,
        };
      return {
        main:
          (this.advisorData.visit_try_listen_hot_value || 0) +
          (this.advisorData.normal_new_hot_value || 0),
        visitTryListen: this.advisorData.visit_try_listen_hot_value || 0,
        normalNew: this.advisorData.normal_new_hot_value || 0,
      };
    },
  },

  watch: {
    // 监听数据变化，重新渲染图表
    advisorData: {
      handler() {
        if (this.advisorData && this.funnelChart) {
          this.$nextTick(() => {
            this.updateFunnelChart();
          });
        }
      },
      deep: true,
    },

    // 监听通话数据变化
    voipData: {
      handler() {
        console.log('通话数据变化:', this.voipData);
      },
      deep: true,
    },
  },

  mounted() {
    this.currentTimeFilter.startDate = dayjs().format('YYYY-MM-DD');
    this.currentTimeFilter.endDate = dayjs().format('YYYY-MM-DD');
    this.initData();
  },

  beforeDestroy() {
    // 清除图表实例
    if (this.funnelChart) {
      this.funnelChart.dispose();
    }

    // 清除所有动画定时器
    this.clearAnimationTimers();

    // 清除进度条观察器
    this.destroyProgressObserver();
  },

  methods: {
    // 初始化数据加载
    async initData() {
      // 只有首次加载时才显示骨架屏
      if (this.isFirstLoad) {
        this.skeletonLoading = true;
      }

      await this.getData();
      await this.getVoipData();
    },

    // 获取顾问数据
    async getData() {
      try {
        const res = await getAdvisorList({
          search_start: this.currentTimeFilter.startDate,
          search_end: this.currentTimeFilter.endDate,
          corp_user_id: 1,
        });
        console.log('顾问数据:', res);

        // 处理接口返回的数据
        if (res && res.code === 0 && res.data) {
          this.advisorData = res.data;
          console.log('处理后的顾问数据:', this.advisorData);
        }

        // 只有首次加载时才关闭骨架屏
        if (this.isFirstLoad) {
          this.skeletonLoading = false;
          this.isFirstLoad = false; // 标记首次加载完成
        }

        this.$nextTick(() => {
          this.initFunnelChart();
          this.initProgressObserver();
        });
      } catch (error) {
        this.skeletonLoading = false;
        this.isFirstLoad = false;
        this.$toast({
          type: 'error',
          message: '获取顾问数据失败',
          forbidClick: true,
        });
        console.error('获取顾问数据失败:', error);
        throw error;
      }
    },

    // 获取通话数据
    async getVoipData() {
      try {
        const res = await getAdvisorVoipList({
          search_start: this.currentTimeFilter.startDate,
          search_end: this.currentTimeFilter.endDate,
          corp_user_id: 1,
        });
        console.log('通话数据:', res);

        // 处理接口返回的通话数据
        if (res && res.code === 0 && res.data) {
          this.voipData = res.data;
          console.log('处理后的通话数据:', this.voipData);
        }
      } catch (error) {
        console.error('获取语音数据失败:', error);
        throw error;
      }
    },

    // 时间筛选变更事件
    async onTimeFilterChange(timeFilter) {
      // 检查是否与上次的时间筛选相同，避免重复调用
      const timeFilterStr = JSON.stringify(timeFilter);
      if (this.lastTimeFilter === timeFilterStr) {
        console.log('时间筛选未变化，跳过重复调用');
        return;
      }
      // 如果开始时间或者结束时间为空 则不进行初始化
      if (!timeFilter.startDate || !timeFilter.endDate) {
        return;
      }
      if (this.isFirstLoad) {
        return;
      }
      console.log('时间筛选变更:', timeFilter);
      this.lastTimeFilter = timeFilterStr;
      this.currentTimeFilter = timeFilter;
      //   this.$toast({
      //     type: 'loading',
      //     message: '加载中...',
      //     forbidClick: true,
      //   });
      await this.initData();
      //   this.$toast.clear();
    },

    // 初始化漏斗图
    initFunnelChart() {
      if (!window.echarts) {
        console.error('ECharts 未加载');
        return;
      }

      const chartDom = this.$refs.funnelChart;
      if (!chartDom) {
        console.error('图表容器未找到');
        return;
      }

      this.funnelChart = window.echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `${params.name}<br/>当前完成: ${params.data.current}人<br/>目标: ${params.data.target}人<br/>完成率: ${params.data.percentage}%`;
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 12,
          },
        },
        series: [
          {
            name: '转化漏斗',
            type: 'funnel',
            left: '10%',
            top: 20,
            bottom: 20,
            width: '80%',
            min: 0,
            max: 100,
            minSize: '30%',
            maxSize: '100%',
            sort: 'descending',
            gap: 2,
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return `${params.name}\n${params.data.current}/${params.data.target}人\n${params.data.percentage}%`;
              },
              fontSize: 11,
              color: '#fff',
              fontWeight: 'bold',
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid',
              },
            },
            itemStyle: {
              borderColor: 'rgba(255, 255, 255, 0.3)',
              borderWidth: 1,
            },
            emphasis: {
              label: {
                fontSize: 12,
              },
              itemStyle: {
                shadowBlur: 20,
                shadowOffsetX: 0,
                shadowOffsetY: 10,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
              },
            },
            data: this.funnelData.map((item, index) => {
              const colors = [
                ['#6366f1', '#4f46e5'],
                ['#10b981', '#059669'],
                ['#8b5cf6', '#7c3aed'],
                ['#f97316', '#ea580c'],
              ];
              return {
                value: item.value,
                name: item.name,
                current: item.value,
                target: item.target,
                percentage: item.percentage,
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: colors[index][0],
                      },
                      {
                        offset: 1,
                        color: colors[index][1],
                      },
                    ],
                  },
                },
              };
            }),
          },
        ],
      };

      this.funnelChart.setOption(option);

      // 响应式处理
      window.addEventListener('resize', () => {
        if (this.funnelChart) {
          this.funnelChart.resize();
        }
      });
    },

    // 更新漏斗图数据
    updateFunnelChart() {
      if (!this.funnelChart) return;

      const option = {
        series: [
          {
            data: this.funnelData.map((item, index) => {
              const colors = [
                ['#6366f1', '#4f46e5'],
                ['#10b981', '#059669'],
                ['#8b5cf6', '#7c3aed'],
                ['#f97316', '#ea580c'],
              ];
              return {
                value: item.value,
                name: item.name,
                current: item.value,
                target: item.target,
                percentage: item.percentage,
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: colors[index][0],
                      },
                      {
                        offset: 1,
                        color: colors[index][1],
                      },
                    ],
                  },
                },
              };
            }),
          },
        ],
      };

      this.funnelChart.setOption(option);
    },

    // 清除动画定时器
    clearAnimationTimers() {
      this.animationTimers.forEach((timer) => {
        clearTimeout(timer);
      });
      this.animationTimers = [];
    },

    // 初始化进度条视窗检测器
    initProgressObserver() {
      // 检查浏览器是否支持 Intersection Observer
      if (!window.IntersectionObserver) {
        // 不支持则直接开始动画
        this.animateProgressBars();
        return;
      }

      const options = {
        root: null,
        rootMargin: '0px 0px -50px 0px', // 提前50px触发
        threshold: 0.1,
      };

      this.progressObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !entry.target.hasAttribute('data-animated')) {
            // 标记已动画，避免重复触发
            entry.target.setAttribute('data-animated', 'true');
            this.animateProgressBar(entry.target);
          }
        });
      }, options);

      const progressBars = this.$el.querySelectorAll('.progress-fill, .progress-mini-fill');
      progressBars.forEach((bar) => {
        // 初始化进度条状态
        const width = bar.style.width;
        bar.setAttribute('data-target-width', width);
        bar.style.width = '0%';
        bar.style.transition = 'none';

        // 开始观察
        this.progressObserver.observe(bar);
      });
    },

    // 销毁进度条观察器
    destroyProgressObserver() {
      if (this.progressObserver) {
        this.progressObserver.disconnect();
        this.progressObserver = null;
      }
    },

    // 单个进度条动画
    animateProgressBar(bar) {
      const targetWidth = bar.getAttribute('data-target-width');

      // 添加过渡效果
      bar.style.transition = 'width 1s ease-out';

      // 稍微延迟一下再开始动画，确保过渡效果生效
      setTimeout(() => {
        bar.style.width = targetWidth;
      }, 50);
    },

    // 进度条动画（备用方法，用于不支持 Intersection Observer 的浏览器）
    animateProgressBars() {
      // 先清除之前的定时器
      this.clearAnimationTimers();

      //   this.$nextTick(() => {
      const progressBars = this.$el.querySelectorAll('.progress-fill, .progress-mini-fill');
      progressBars.forEach((bar, index) => {
        const width = bar.style.width;
        bar.style.width = '0%';

        const timer = setTimeout(() => {
          bar.style.transition = 'width 1s ease-out';
          bar.style.width = width;
        }, index * 200 + 500);

        // 将定时器添加到数组中以便清除
        this.animationTimers.push(timer);
      });
      //   });
    },
  },
};
</script>

<style lang="less" scoped>
.course-advisor {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', sans-serif;
}

// 头部信息
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    flex: 1;

    .system-title {
      font-size: 40px;
      font-weight: bold;
      margin: 0 0 16px 0;
    }

    .user-role {
      font-size: 28px;
      opacity: 0.9;
      margin: 0 0 8px 0;
    }

    .user-campus {
      font-size: 22px;
      margin: 0;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 8px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      i {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .user-avatar {
    width: 96px;
    height: 96px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
  }
}

// 通用区块样式
.target-section,
.process-section,
.heat-section {
  background: white;
  margin-top: 1px;
  padding: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;

  i {
    font-size: 32px;

    &.fa-bullseye {
      color: #ef4444;
    }

    &.fa-funnel-dollar {
      color: #3b82f6;
    }

    &.fa-coins {
      color: #eab308;
    }
  }

  .section-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 32px;
  }
}

// 目标模块
.target-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.target-card,
.revenue-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 24px 80px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.icon-wrapper {
  width: 88px;
  height: 88px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;

  &.blue {
    background: #dbeafe;
    color: #2563eb;
  }

  &.green {
    background: #dcfce7;
    color: #16a34a;
  }

  &.purple {
    background: #f3e8ff;
    color: #9333ea;
  }

  &.orange {
    background: #fed7aa;
    color: #ea580c;
  }

  &.gradient {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    width: 80px;
    height: 80px;
  }
}

.card-value {
  text-align: right;

  .value {
    font-size: 48px;
    font-weight: bold;
    color: #1f2937;
    line-height: 1;
  }

  .target {
    font-size: 24px;
    color: #6b7280;
    margin-top: 4px;
  }
}

.card-title {
  font-size: 28px;
  color: #6b7280;
  margin-bottom: 16px;
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.progress-track {
  flex: 1;
  height: 16px;
  background: #e5e7eb;
  border-radius: 8px;
  overflow: hidden;

  &.large {
    height: 24px;
    border-radius: 12px;
  }
}

.progress-fill {
  height: 100%;
  border-radius: inherit;
  transition: width 1s ease-out;

  &.blue {
    background: #3b82f6;
  }

  &.green {
    background: #10b981;
  }

  &.purple {
    background: #8b5cf6;
  }

  &.orange {
    background: #f97316;
  }

  &.gradient {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
  }
}

.progress-text {
  font-size: 24px;
  font-weight: 600;

  &.blue {
    color: #2563eb;
  }

  &.green {
    color: #059669;
  }

  &.purple {
    color: #7c3aed;
  }

  &.orange {
    color: #ea580c;
  }

  &.gradient {
    color: #d97706;
  }
}

// 过程指标
.funnel-chart-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;

  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.03) 0%,
        rgba(147, 51, 234, 0.02) 50%,
        rgba(249, 115, 22, 0.03) 100%);
    border-radius: 12px;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    border-radius: 50%;
    filter: blur(20px);
    pointer-events: none;
  }
}

.funnel-chart {
  width: 100%;
  height: 400px;
  position: relative;
  z-index: 1;
}

.conversion-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 32px;
  position: relative;
  z-index: 1;
}

.metric-card {
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

  &.blue {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border-color: rgba(59, 130, 246, 0.2);
  }

  &.green {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    border-color: rgba(34, 197, 94, 0.2);
  }

  &.purple {
    background: linear-gradient(135deg, #faf5ff, #f3e8ff);
    border-color: rgba(147, 51, 234, 0.2);
  }

  &.orange {
    background: linear-gradient(135deg, #fff7ed, #fed7aa);
    border-color: rgba(249, 115, 22, 0.2);
  }
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.metric-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;

  .metric-card.blue & {
    background: #3b82f6;
  }

  .metric-card.green & {
    background: #10b981;
  }

  .metric-card.purple & {
    background: #8b5cf6;
  }

  .metric-card.orange & {
    background: #f97316;
  }
}

.metric-name {
  font-size: 24px;
  font-weight: 500;

  .metric-card.blue & {
    color: #1e40af;
  }

  .metric-card.green & {
    color: #047857;
  }

  .metric-card.purple & {
    color: #6b21a8;
  }

  .metric-card.orange & {
    color: #c2410c;
  }
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;

  .metric-card.blue & {
    color: #1e3a8a;
  }

  .metric-card.green & {
    color: #064e3b;
  }

  .metric-card.purple & {
    color: #581c87;
  }

  .metric-card.orange & {
    color: #9a3412;
  }
}

.metric-desc {
  font-size: 22px;
  opacity: 0.75;

  .metric-card.blue & {
    color: #2563eb;
  }

  .metric-card.green & {
    color: #059669;
  }

  .metric-card.purple & {
    color: #7c3aed;
  }

  .metric-card.orange & {
    color: #ea580c;
  }
}

// 过程指标子项
.process-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.process-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.item-icon {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;

  &.blue {
    background: #dbeafe;
    color: #2563eb;
  }

  &.green {
    background: #dcfce7;
    color: #16a34a;
  }

  &.purple {
    background: #f3e8ff;
    color: #9333ea;
  }
}

.item-info {
  .item-title {
    font-size: 32px;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
  }

  .item-subtitle {
    font-size: 28px;
    color: #6b7280;
  }
}

.item-progress {
  text-align: right;

  .progress-value {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 8px;

    &.blue {
      color: #2563eb;
    }

    &.green {
      color: #059669;
    }

    &.purple {
      color: #7c3aed;
    }
  }
}

.progress-mini {
  width: 128px;
  height: 16px;
  background: #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.progress-mini-fill {
  height: 100%;
  border-radius: 8px;
  transition: width 1s ease-out;

  &.blue {
    background: #3b82f6;
  }

  &.green {
    background: #10b981;
  }

  &.purple {
    background: #8b5cf6;
  }
}

// 热力值模块
.main-heat-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.main-heat-icon {
  width: 96px;
  height: 96px;
  border-radius: 24px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  flex-shrink: 0;
}

.main-heat-content {
  flex: 1;
}

.main-heat-title {
  font-size: 28px;
  color: #6b7280;
  margin-bottom: 8px;
}

.main-heat-value {
  font-size: 56px;
  font-weight: bold;
  color: #1f2937;
}

.heat-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.heat-detail-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 24px;
  padding: 32px;
  text-align: left;
}

.heat-detail-title {
  font-size: 24px;
  color: #6b7280;
  margin-bottom: 16px;
}

.heat-detail-value {
  font-size: 40px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 8px;

  &.green {
    color: #059669;
  }

  &.purple {
    color: #8b5cf6;
  }
}

.heat-detail-rate {
  font-size: 24px;
  color: #9ca3af;
}
</style>
