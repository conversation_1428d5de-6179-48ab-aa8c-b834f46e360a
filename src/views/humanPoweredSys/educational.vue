<template>
  <div class="educational">
    <!-- 加载状态 -->
    <SkeletonLoader v-if="skeletonLoading" />

    <!-- 正常内容 -->
    <div v-else class="content">
      <!-- 头部信息 -->
      <div class="header-section">
        <div class="header-content">
          <div class="user-info">
            <h1 class="system-title">聂道人力动态驱动系统</h1>
            <p class="user-role">才佳林·教务</p>
            <p class="user-campus">北京刘家窑</p>
          </div>
          <div class="user-avatar">
            <i class="fas fa-graduation-cap"></i>
          </div>
        </div>
      </div>

      <!-- 时间筛选模块 -->
      <TimeFilter @change="onTimeFilterChange" />

      <!-- 目标模块 -->
      <div class="target-section">
        <div class="section-header">
          <i class="fas fa-bullseye text-red-500"></i>
          <span class="section-title">目标</span>
        </div>
        <div class="target-grid">
          <!-- 新生报名人数 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper blue">
                <i class="fas fa-user-plus"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="sData.normal_renew_target" :delay="200" :use-intersection-observer="true"
                    root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">/ {{ sData.normal_renew_people }}人</div>
              </div>
            </div>
            <div class="card-title">正课续费总人数</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill blue" data-progress-id="newStudents"
                  :class="{ animate: showProgressAnimation }" :style="{
                    width: showProgressAnimation ? sData.normal_renew_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text blue">{{ sData.normal_renew_finish_ratio }}%</span>
            </div>
          </div>

          <!-- 课程完成率 -->
          <div class="target-card">
            <div class="card-header">
              <div class="icon-wrapper green">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="card-value">
                <div class="value">
                  <AnimatedNumber :value="sData.renew_old_price" :prefix="'¥'" :delay="400"
                    :use-intersection-observer="true" root-margin="0px 0px -50px 0px" />
                </div>
                <div class="target">/ ¥{{ sData.renew_old_price_target }}K</div>
              </div>
            </div>
            <div class="card-title">老生续费业绩</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill green" data-progress-id="courseCompletion"
                  :class="{ animate: showProgressAnimation }" :style="{
                    width: showProgressAnimation ? sData.renew_old_price_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text green">{{ sData.renew_old_price_finish_ratio }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 过程指标模块 -->
      <div class="process-section">
        <div class="section-header">
          <i class="fas fa-chart-pie text-purple-500"></i>
          <span class="section-title">过程指标</span>
        </div>
        <div class="process-grid">
          <!-- 流失率 -->
          <div class="metric-card">
            <div class="card-header">
              <div class="icon-wrapper red">
                <i class="fas fa-user-times"></i>
              </div>
              <div class="card-value">
                <div class="value">{{ sData.churn_ratio }}%</div>
                <div class="target">目标 ≤{{ sData.churn_ratio_target }}%</div>
              </div>
            </div>
            <div class="card-title">流失率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill" data-progress-id="churnRate"
                  :class="[processData.churnRate.status, { animate: showProgressAnimation }]" :style="{
                    width: showProgressAnimation ? sData.churn_ratio_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text" :class="processData.churnRate.status">{{ sData.churn_ratio_finish_ratio
              }}%</span>
            </div>
          </div>

          <!-- 活跃率 -->
          <div class="metric-card">
            <div class="card-header">
              <div class="icon-wrapper green">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="card-value">
                <div class="value">{{ sData.active_ratio }}%</div>
                <div class="target">目标 ≥{{ sData.active_ratio_target }}%</div>
              </div>
            </div>
            <div class="card-title">活跃率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill green" data-progress-id="activeRate"
                  :class="{ animate: showProgressAnimation }" :style="{
                    width: showProgressAnimation ? sData.active_ratio_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text green">{{ sData.active_ratio_finish_ratio }}%</span>
            </div>
          </div>

          <!-- 满班率 -->
          <div class="metric-card">
            <div class="card-header">
              <div class="icon-wrapper blue">
                <i class="fas fa-users"></i>
              </div>
              <div class="card-value">
                <div class="value">{{ sData.full_class_ratio }}%</div>
                <div class="target">目标 ≥{{ sData.full_class_ratio_target }}%</div>
              </div>
            </div>
            <div class="card-title">满班率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill blue" data-progress-id="classFullRate"
                  :class="{ animate: showProgressAnimation }" :style="{
                    width: showProgressAnimation ? sData.full_class_ratio_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text blue">{{ sData.full_class_ratio_finish_ratio }}%</span>
            </div>
          </div>

          <!-- 学员出勤率 -->
          <div class="metric-card">
            <div class="card-header">
              <div class="icon-wrapper purple">
                <i class="fas fa-calendar-check"></i>
              </div>
              <div class="card-value">
                <div class="value">{{ sData.attendance_ratio }}%</div>
                <div class="target">目标 ≥{{ sData.attendance_ratio_target }}%</div>
              </div>
            </div>
            <div class="card-title">学员出勤率</div>
            <div class="progress-bar">
              <div class="progress-track">
                <div class="progress-fill purple" data-progress-id="attendanceRate"
                  :class="{ animate: showProgressAnimation }" :style="{
                    width: showProgressAnimation ? sData.attendance_ratio_finish_ratio + '%' : '0%',
                  }"></div>
              </div>
              <span class="progress-text purple">{{ sData.attendance_ratio_finish_ratio }}%</span>
            </div>
          </div>
        </div>

        <!-- 小程序绑定 -->
        <div class="app-binding-card">
          <div class="card-header">
            <div class="icon-wrapper orange">
              <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="card-info">
              <div class="card-title">小程序绑定</div>
              <div class="card-subtitle">{{ sData.mini_program_bind_people }}人</div>
            </div>
            <div class="card-progress">
              <div class="target-text">目标: {{ sData.mini_program_bind_target }}人</div>
              <div class="progress-container">
                <div class="progress-track">
                  <div class="progress-fill orange" data-progress-id="appBinding"
                    :class="{ animate: showProgressAnimation }" :style="{
                      width: showProgressAnimation
                        ? sData.mini_program_bind_finish_ratio + '%'
                        : '0%',
                    }"></div>
                </div>
                <span class="progress-text orange">{{ sData.mini_program_bind_finish_ratio }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热力值模块 -->
      <div class="heat-section">
        <div class="section-header">
          <i class="fas fa-coins text-yellow-500"></i>
          <span class="section-title">热力值</span>
        </div>

        <!-- 基础指标 -->
        <div class="heat-basic-grid">
          <div class="heat-card">
            <div class="icon-wrapper blue-gradient">
              <i class="fas fa-percentage"></i>
            </div>
            <div class="card-title">续费率</div>
            <div class="card-value">{{ sData.renew_ratio_hot_value }}%</div>
          </div>

          <div class="heat-card">
            <div class="icon-wrapper green-gradient">
              <i class="fas fa-user-plus"></i>
            </div>
            <div class="card-title">续费人头</div>
            <div class="card-value">{{ sData.renew_people }}人</div>
          </div>
        </div>

        <!-- 续费课包情况 -->
        <div class="package-card">
          <div class="card-header">
            <div class="icon-wrapper purple-gradient">
              <i class="fas fa-box"></i>
            </div>
            <div class="card-info">
              <div class="card-title">续费课包情况</div>
              <div class="card-value">{{ heatData.totalPackages }}个</div>
            </div>
          </div>
          <div class="package-grid">
            <div v-for="pkg in heatData.packages" :key="pkg.type" class="package-item" :class="pkg.color">
              <div class="package-type">{{ pkg.type }}</div>
              <div class="package-count">{{ pkg.count }}个</div>
            </div>
          </div>
        </div>

        <!-- 实际续费业绩流水 -->
        <div class="revenue-card">
          <div class="card-header">
            <div style="display: flex; align-items: center">
              <div class="icon-wrapper yellow-gradient">
                <i class="fas fa-chart-bar"></i>
              </div>
              <div class="card-info" style="margin-left: 10px">
                <div class="card-title">实际续费业绩流水</div>
                <div class="card-value">￥{{ heatData.actualRevenue }}</div>
              </div>
            </div>

            <div class="achievement-info">
              <div class="achievement-label">月度目标达成</div>
              <div class="achievement-value">{{ heatData.achievementRate }}%</div>
            </div>
          </div>
        </div>

        <!-- 实际薪酬汇总 -->
        <div class="salary-summary-card">
          <div class="card-header">
            <div class="icon-wrapper emerald-gradient">
              <i class="fas fa-wallet"></i>
            </div>
            <div class="card-info">
              <div class="card-title">本月实际热力值</div>
              <div class="card-value">￥{{ heatData.totalHeatValue }}</div>
            </div>
          </div>

          <!-- 热力值组成明细 -->
          <div class="heat-breakdown">
            <div v-for="item in heatData.breakdown" :key="item.type" class="breakdown-item">
              <div class="breakdown-header">
                <span class="breakdown-label">{{ item.label }}</span>
                <span class="breakdown-value">{{ item.value }}</span>
              </div>
              <div class="breakdown-ratio">占比 {{ item.ratio }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TimeFilter from './common/TimeFilter.vue';
import AnimatedNumber from './common/AnimatedNumber.vue';
import SkeletonLoader from './common/SkeletonLoader.vue';
import { getBoardEducator } from '@/api/humanPoweredSys';
import dayjs from 'dayjs';
import { Toast } from 'vant';

export default {
  name: 'Educational',
  components: {
    TimeFilter,
    AnimatedNumber,
    SkeletonLoader,
  },
  data() {
    return {
      // 页面状态
      skeletonLoading: true,

      // 是否首次加载
      isFirstLoad: true,

      // 时间筛选相关
      defaultTimePeriod: '日',
      defaultStartDate: '',
      defaultEndDate: '',
      timeFilter: {
        startDate: '',
        endDate: '',
      },
      lastTimeFilter: '',

      // 动画相关
      animationTimers: [],
      showProgressAnimation: false,

      // 数据
      sData: {},
      // 目标数据
      targetData: {
        newStudents: {
          current: 85,
          target: 100,
          progress: 85,
        },
        courseCompletion: {
          current: 92.5,
          target: 90,
          progress: 102.8,
        },
        satisfaction: {
          current: 96.8,
          target: 95,
          progress: 101.9,
        },
        renewalRevenue: {
          current: 163.45,
          target: 200.8,
          progress: 81.4,
        },
      },

      // 过程指标数据
      processData: {
        churnRate: {
          current: 8.5,
          target: 10,
          progress: 85,
          achievement: 85,
          status: 'green',
        },
        activeRate: {
          current: 92.3,
          target: 90,
          progress: 92.3,
          achievement: 102.6,
        },
        classFullRate: {
          current: 88.7,
          target: 85,
          progress: 88.7,
          achievement: 104.4,
        },
        attendanceRate: {
          current: 94.2,
          target: 92,
          progress: 94.2,
          achievement: 102.4,
        },
        appBinding: {
          current: 485,
          target: 520,
          progress: 93.3,
        },
      },

      // 热力值数据
      heatData: {
        renewalRate: 34.56,
        renewalCount: 12,
        totalPackages: 298,
        packages: [
          { type: '30课包', count: 85, color: 'blue' },
          { type: '60课包', count: 96, color: 'green' },
          { type: '90课包', count: 72, color: 'purple' },
          { type: '120课包', count: 45, color: 'orange' },
        ],
        actualRevenue: '1,640.18',
        achievementRate: 81.4,
        totalHeatValue: '7,200',
        breakdown: [
          { type: 'renewal_rate', label: '续费率热力值', value: '￥2,850', ratio: 34.56 },
          { type: 'renewal_count', label: '续费人头热力值', value: '12人', ratio: 14.2 },
          { type: 'renewal_package', label: '续费课包热力值', value: '￥2,130', ratio: 29.6 },
          { type: 'renewal_revenue', label: '续费业绩热力值', value: '￥1,020', ratio: 14.2 },
        ],
      },
    };
  },

  mounted() {
    // 初始化时间筛选
    const today = dayjs().format('YYYY-MM-DD');
    this.timeFilter = {
      startDate: today,
      endDate: today,
    };
    this.initData();
    // 延迟显示进度条动画
    setTimeout(() => {
      this.showProgressAnimation = true;
    }, 800);
  },

  beforeDestroy() {
    this.destroyProgressObserver();
    this.clearAnimationTimers();
  },

  methods: {
    // 初始化数据
    async initData() {
      // 只有首次加载时才显示骨架屏
      if (this.isFirstLoad) {
        this.skeletonLoading = true;
      }

      await this.getData();
    },

    // 获取教务数据
    async getData() {
      try {
        const res = await getBoardEducator({
          search_start: this.timeFilter.startDate,
          search_end: this.timeFilter.endDate,
          corp_user_id: 3,
        });
        console.log('数据:', res);
        if (res.code === 0) {
          this.sData = res.data ?? {};
        } else {
          this.$message.error(res.message);
        }

        // 这里可以处理返回的数据，更新组件状态
      } catch (error) {

        console.error('获取教务数据失败:', error);
        Toast.fail('获取数据失败，请稍后重试');
      } finally {
        // 只有首次加载时才关闭骨架屏 
        if (this.isFirstLoad) {
          this.skeletonLoading = false;
          this.isFirstLoad = false;
        }
      }
    },

    // 时间筛选变更事件
    async onTimeFilterChange(timeFilter) {
      // 检查是否与上次的时间筛选相同，避免重复调用
      const timeFilterStr = JSON.stringify(timeFilter);
      if (this.lastTimeFilter === timeFilterStr) {
        console.log('时间筛选未变化，跳过重复调用');
        return;
      }

      // 如果开始时间或者结束时间为空 则不进行初始化
      if (!timeFilter.startDate || !timeFilter.endDate) {
        console.log('时间筛选参数不完整，跳过调用');
        return;
      }

      // 如果是首次加载，跳过时间筛选变更
      if (this.isFirstLoad) {
        return;
      }

      console.log('时间筛选变更:', timeFilter);
      this.lastTimeFilter = timeFilterStr;
      this.timeFilter = { ...timeFilter };

      // 重新加载数据（不显示骨架屏）
      await this.initData();
    },



    // 初始化进度条视窗检测器
    initProgressObserver() {
      // 检查浏览器是否支持 Intersection Observer
      if (!window.IntersectionObserver) {
        // 不支持则直接开始动画
        this.animateProgressBars();
        return;
      }

      const options = {
        root: null,
        rootMargin: '0px 0px -50px 0px', // 提前50px触发
        threshold: 0.1,
      };

      this.progressObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !entry.target.hasAttribute('data-animated')) {
            // 标记已动画，避免重复触发
            entry.target.setAttribute('data-animated', 'true');
            console.log(' entry.target', entry.target);
            this.animateProgressBar(entry.target);
          }
        });
      }, options);

      // 观察所有进度条元素
      this.$nextTick(() => {
        const progressBars = this.$el.querySelectorAll('.progress-fill, .progress-mini-fill');
        progressBars.forEach((bar) => {
          // 初始化进度条状态
          const width = bar.style.width;
          bar.setAttribute('data-target-width', width);
          bar.style.width = '0%';
          bar.style.transition = 'none';

          // 开始观察
          this.progressObserver.observe(bar);
        });
      });
    },

    // 销毁进度条观察器
    destroyProgressObserver() {
      if (this.progressObserver) {
        this.progressObserver.disconnect();
        this.progressObserver = null;
      }
    },

    // 单个进度条动画
    animateProgressBar(bar) {
      const targetWidth = bar.getAttribute('data-target-width');
      console.log(' targetWidth', targetWidth);
      // 添加过渡效果
      bar.style.transition = 'width 1s ease-out';

      // 稍微延迟一下再开始动画，确保过渡效果生效
      setTimeout(() => {
        bar.style.width = targetWidth;
      }, 50);
    },

    // 进度条动画（备用方法，用于不支持 Intersection Observer 的浏览器）
    animateProgressBars() {
      // 先清除之前的定时器
      this.clearAnimationTimers();

      this.$nextTick(() => {
        const progressBars = this.$el.querySelectorAll('.progress-fill');
        console.log('width', progressBars);
        progressBars.forEach((bar, index) => {
          const width = bar.style.width;

          bar.style.width = '0%';

          const timer = setTimeout(() => {
            bar.style.transition = 'width 1s ease-out';
            bar.style.width = width;
          }, index * 200 + 500);

          // 将定时器添加到数组中以便清除
          this.animationTimers.push(timer);
        });
      });
    },

    // 清除动画定时器
    clearAnimationTimers() {
      this.animationTimers.forEach((timer) => clearTimeout(timer));
      this.animationTimers = [];
    },

    // 重新启动进度条动画
    restartProgressAnimation() {
      // 先隐藏动画
      this.showProgressAnimation = false;
      // 延迟重新显示动画
      this.$nextTick(() => {
        setTimeout(() => {
          this.showProgressAnimation = true;
        }, 100);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.educational {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    background: #f8fafc;
    min-height: 100vh;
  }
}

// 头部样式
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  color: white;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    .system-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .user-role {
      font-size: 28px;
      opacity: 0.9;
      margin: 0 0 8px 0;
    }

    .user-campus {
      font-size: 22px;
      margin: 0;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 8px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      i {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
      }

      &::before {
        content: '\f3c5';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        font-size: 18px;
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .user-avatar {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
  }
}

// 通用卡片样式
.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

// 图标包装器样式
.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;

  &.blue {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
  }

  &.green {
    background: linear-gradient(135deg, #10b981, #059669);
  }

  &.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  }

  &.orange {
    background: linear-gradient(135deg, #f97316, #ea580c);
  }

  &.red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
  }

  &.blue-gradient {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
  }

  &.green-gradient {
    background: linear-gradient(135deg, #34d399, #10b981);
  }

  &.purple-gradient {
    background: linear-gradient(135deg, #a78bfa, #8b5cf6);
  }

  &.yellow-gradient {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
  }

  &.emerald-gradient {
    background: linear-gradient(135deg, #34d399, #059669);
  }
}

// 进度条样式
.progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;

  .progress-track {
    flex: 1;
    height: 16px;
    background: #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;

    &.blue {
      background: #3b82f6;
    }

    &.green {
      background: #10b981;
    }

    &.purple {
      background: #8b5cf6;
    }

    &.orange {
      background: #f97316;
    }

    &.red {
      background: #ef4444;
    }
  }

  .progress-text {
    font-size: 24px;
    font-weight: 600;

    &.blue {
      color: #2563eb;
    }

    &.green {
      color: #16a34a;
    }

    &.purple {
      color: #7c3aed;
    }

    &.orange {
      color: #ea580c;
    }

    &.red {
      color: #dc2626;
    }
  }
}

// 模块通用样式
.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;

  i {
    font-size: 26px;
  }

  .section-title {
    font-weight: 600;
    color: #374151;
  }
}

// 目标模块
.target-section {
  padding: 16px;

  .target-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .target-card {
    .glass-card();
    padding: 16px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover::before {
      transform: translateX(100%);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .card-value {
      text-align: right;

      .value {
        font-size: 28px;
        font-weight: bold;
        color: #374151;
      }

      .target {
        font-size: 20px;
        color: #6b7280;
      }
    }

    .card-title {
      font-size: 24px;
      color: #6b7280;
      margin-bottom: 8px;
      font-weight: 500;
    }
  }
}

// 过程指标模块
.process-section {
  padding: 16px;

  .process-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 16px;
  }

  .metric-card {
    .glass-card();
    padding: 16px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    &:hover::before {
      transform: translateX(100%);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
    }

    .card-value {
      text-align: right;

      .value {
        font-size: 28px;
        font-weight: bold;
        color: #374151;
      }

      .target {
        /* px-to-viewport-ignore */
        font-size: 20px;
        color: #6b7280;
      }
    }

    .card-title {
      font-size: 24px;
      color: #6b7280;
      margin-bottom: 8px;
      font-weight: 500;
    }
  }

  .app-binding-card {
    .glass-card();
    padding: 16px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .card-info {
      flex: 1;

      .card-title {
        font-size: 24px;
        font-weight: bold;
        color: #374151;
      }

      .card-subtitle {
        font-size: 24px;
        color: #6b7280;
      }
    }

    .card-progress {
      text-align: right;

      .target-text {
        font-size: 22px;
        color: #6b7280;
        margin-bottom: 4px;
      }

      .progress-container {
        display: flex;
        align-items: center;
        gap: 8px;

        .progress-track {
          width: 80px;
        }
      }
    }
  }
}

// 热力值模块
.heat-section {
  padding: 16px 16px 24px;

  .heat-basic-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 12px;
  }

  .heat-card {
    .glass-card();
    padding: 16px;

    .card-title {
      font-size: 24px;
      color: #6b7280;
      margin: 12px 0 4px;
    }

    .card-value {
      font-size: 26px;
      font-weight: bold;
      color: #374151;
    }
  }

  .package-card {
    .glass-card();
    padding: 16px;
    margin-bottom: 12px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }

    .card-info {
      .card-title {
        font-size: 24px;
        color: #6b7280;
      }

      .card-value {
        font-size: 26px;
        font-weight: bold;
        color: #374151;
      }
    }

    .package-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .package-item {
      padding: 8px;
      border-radius: 8px;
      text-align: center;

      &.blue {
        background: #dbeafe;

        .package-type {
          color: #1d4ed8;
        }

        .package-count {
          color: #2563eb;
        }
      }

      &.green {
        background: #dcfce7;

        .package-type {
          color: #166534;
        }

        .package-count {
          color: #16a34a;
        }
      }

      &.purple {
        background: #f3e8ff;

        .package-type {
          color: #7c3aed;
        }

        .package-count {
          color: #8b5cf6;
        }
      }

      &.orange {
        background: #fed7aa;

        .package-type {
          color: #ea580c;
        }

        .package-count {
          color: #f97316;
        }
      }

      .package-type {
        font-size: 18px;
        font-weight: 500;
      }

      .package-count {
        font-size: 18px;
      }
    }
  }

  .revenue-card {
    .glass-card();
    padding: 16px;
    margin-bottom: 12px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .card-info {
      .card-title {
        font-size: 24px;
        color: #6b7280;
      }

      .card-value {
        font-size: 26px;
        font-weight: bold;
        color: #374151;
      }
    }

    .achievement-info {
      text-align: right;

      .achievement-label {
        font-size: 22px;
        color: #6b7280;
      }

      .achievement-value {
        font-size: 18px;
        font-weight: bold;
        color: #16a34a;
      }
    }
  }

  .salary-summary-card {
    .glass-card();
    padding: 16px;
    position: relative;
    overflow: hidden;

    // 背景装饰
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
      border-radius: 16px;
    }

    &::after {
      content: '';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
      border-radius: 50%;
      filter: blur(32px);
    }

    .card-header {
      position: relative;
      z-index: 10;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
    }

    .card-info {
      .card-title {
        font-size: 24px;
        color: #6b7280;
      }

      .card-value {
        font-size: 30px;
        font-weight: bold;
        color: #374151;
      }
    }

    .heat-breakdown {
      position: relative;
      z-index: 10;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .breakdown-item {
      background: rgba(255, 255, 255, 0.6);
      padding: 12px;
      border-radius: 8px;
      border: 1px solid rgba(16, 185, 129, 0.1);

      .breakdown-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;

        .breakdown-label {
          font-size: 22px;
          color: #6b7280;
          font-weight: 500;
        }

        .breakdown-value {
          font-size: 22px;
          font-weight: bold;
          color: #374151;
        }
      }

      .breakdown-ratio {
        font-size: 22px;
        color: #6b7280;
      }
    }
  }
}

// 移动端适配
@media (max-width: 480px) {
  .educational {
    .header-section {
      padding: 16px;

      .user-info .system-title {
        font-size: 18px;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }
    }

    .target-section,
    .process-section {
      padding: 12px;
    }

    .target-card,
    .metric-card {
      padding: 12px;

      .card-value .value {
        font-size: 16px;
      }

      .target {
        font-size: 20px;
      }

      .icon-wrapper {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }
    }

    .app-binding-card {
      padding: 12px;

      .card-info .card-title {
        font-size: 24px;
      }
    }

    .heat-section {
      padding: 12px;
    }

    .heat-card {
      padding: 12px;

      .card-value {
        font-size: 18px;
      }
    }

    .package-card,
    .revenue-card,
    .salary-summary-card {
      padding: 12px;
    }

    .salary-summary-card .card-info .card-value {
      font-size: 24px;
    }
  }
}

@media (max-width: 360px) {
  .educational {

    .target-grid,
    .process-grid {
      gap: 8px;
    }

    .target-card,
    .metric-card {
      padding: 10px;

      .card-value .value {
        font-size: 14px;
      }

      .card-title {
        font-size: 24px;
      }
    }

    .heat-card {
      padding: 10px;

      .card-value {
        font-size: 26px;
      }
    }

    .salary-summary-card {
      .heat-breakdown {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .breakdown-item {
        padding: 8px;
      }

      .card-info .card-value {
        font-size: 26px;
      }
    }
  }
}
</style>
