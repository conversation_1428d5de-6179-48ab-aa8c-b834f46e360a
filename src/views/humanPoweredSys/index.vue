<!-- 企业微信跳转页 -->
<template>
  <div></div>
</template>

<script>
import { getWechatUserInfo } from '@/api/user';
export default {
  name: 'HumanPoweredSys',
  data() {
    return {
      appid: 'wwfa42fc9a08707f2b',
      redirect_uri: 'https://tg-h5-dev.estar-go.com/humanPoweredSys/index',
      state: 'STATE',
      agentid: '',
      name: 'HumanPoweredSys',
    };
  },
  mounted() {
    const encodeRedirectUri = encodeURIComponent(this.redirect_uri);
    // https://open.weixin.qq.com/connect/oauth2/authorize?appid=CORPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE&agentid=AGENTID#wechat_redirect
    const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.appid}&redirect_uri=${encodeRedirectUri}&response_type=code&scope=snsapi_base&state=${this.state}&agentid=${this.agentid}#wechat_redirect`;
    window.location.href = url;
    this.getWechatUserInfo();
  },
  methods: {
    getWechatUserInfo() {
      if (this.$route.query.code) {
        getWechatUserInfo({
          code: this.$route.query.code,
        }).then((res) => {
          console.log(res);
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
