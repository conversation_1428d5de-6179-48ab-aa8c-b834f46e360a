<template>
  <div class="login-container">
    <div class="login-header">
      <h1>H5中台系统</h1>
      <p>移动端H5页面统一管理平台</p>
    </div>

    <van-form @submit="onSubmit" class="login-form">
      <van-field
        v-model="loginForm.username"
        name="username"
        label="用户名"
        placeholder="请输入用户名"
        :rules="[{ required: true, message: '请输入用户名' }]"
      />
      <van-field
        v-model="loginForm.password"
        type="password"
        name="password"
        label="密码"
        placeholder="请输入密码"
        :rules="[{ required: true, message: '请输入密码' }]"
      />
      <div class="submit-btn">
        <van-button round block type="primary" native-type="submit"> 登录 </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
      },
    };
  },
  methods: {
    async onSubmit(values) {
      try {
        await this.$store.dispatch('user/login', values);
        const redirect = this.$route.query.redirect || '/';
        this.$router.replace(redirect);
      } catch (error) {
        console.error('登录失败:', error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.login-container {
  min-height: 100vh;
  padding: 0 20px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.login-header {
  padding: 60px 0 40px;
  text-align: center;

  h1 {
    font-size: 28px;
    color: @text-color;
    margin: 0 0 12px;
  }

  p {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

.login-form {
  margin-top: 20px;
}

.submit-btn {
  margin-top: 40px;
  padding: 0 16px;
}

.login-tips {
  margin-top: 40px;
  text-align: center;
  color: #666;
  font-size: 14px;

  p {
    margin: 8px 0;
  }
}
</style>
