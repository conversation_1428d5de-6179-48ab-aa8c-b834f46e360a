// 页面切换动画
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  will-change: transform;
  transition: transform @animation-duration-base @animation-timing-function-enter;
}

// 右滑动画
.slide-right-enter {
  transform: translate3d(-100%, 0, 0);
}

.slide-right-leave-active {
  transform: translate3d(100%, 0, 0);
}

// 左滑动画
.slide-left-enter {
  transform: translate3d(100%, 0, 0);
}

.slide-left-leave-active {
  transform: translate3d(-100%, 0, 0);
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity @animation-duration-base ease;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}
