import { getToken, setToken, removeToken } from '@/utils/auth';
import { login, getUserInfo } from '@/api/user';
import { resetRouter } from '@/router';

const state = {
  token: getToken(),
  userInfo: {},
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_USER_INFO: (state, info) => {
    state.userInfo = info;
  },
  RESET_STATE: (state) => {
    state.token = '';
    state.userInfo = {};
  },
};

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { username, password } = userInfo;
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password, auth_type: 'password', login_type: 'admin' })
        .then((response) => {
          const { token } = response;
          console.log('token-->', token);
          commit('SET_TOKEN', token);
          setToken(token);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // 获取用户信息
  getUserInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getUserInfo(state.token)
        .then((response) => {
          const { data } = response;
          if (!data) {
            reject('验证失败，请重新登录');
          }
          commit('SET_USER_INFO', data);
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // 用户登出
  logout({ commit }) {
    return new Promise((resolve) => {
      removeToken();
      resetRouter();
      commit('RESET_STATE');
      resolve();
    });
  },

  // 重置 token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken();
      commit('RESET_STATE');
      resolve();
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
