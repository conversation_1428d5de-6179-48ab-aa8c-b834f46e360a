import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';

Vue.use(Vuex);

// 自动导入所有模块
const modulesFiles = require.context('./modules', true, /\.js$/);

const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  const value = modulesFiles(modulePath);
  modules[moduleName] = value.default;
  return modules;
}, {});

const store = new Vuex.Store({
  state: {
    appTitle: process.env.VUE_APP_TITLE,
    appVersion: '1.0.0',
  },
  mutations: {
    SET_APP_TITLE: (state, title) => {
      state.appTitle = title;
    },
  },
  actions: {
    setAppTitle({ commit }, title) {
      commit('SET_APP_TITLE', title);
    },
  },
  modules,
  getters,
});

export default store;
