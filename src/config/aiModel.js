export default {
  // 模型配置
  model: {
    // name: 'deepseek-chat', // 模型名称
    // name: 'deepseek-reasoner', // 模型名称
    // name: 'Pro/deepseek-ai/DeepSeek-R1',
    // name: 'Qwen/Qwen3-235B-A22B',
    name: 'deepseek-ai/DeepSeek-V3',

    // deepseek
    // apiUrl: 'https://api.deepseek.com/v1/chat/completions', // API地址
    // apiKey: 'sk-4ec38a33a64f474eade92f741bd2c7c2', // 请替换为实际的API密钥

    // 硅基流动
    apiUrl: 'https://api.siliconflow.cn/v1/chat/completions', // API地址
    apiKey: 'sk-pogmvmahultstymlsivfjlvnteybaacxhemwzoxxotzdqrgt', // 请替换为实际的API密钥
  },
  // 系统提示词现在通过 systemPrompts.js 动态选择
  // 请求参数
  requestParams: {
    temperature: 0.7,
    top_p: 0.7,
    max_tokens: 8192,
    stream: true, // 启用流式输出
    enable_thinking: false, // 是否开启思考模式
    stop: null, // 不设置停止词，确保内容完整
  },
  // 上下文截取的消息数量
  contextMessages: 10,
};
