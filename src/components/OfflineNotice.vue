<template>
  <transition name="slide-down">
    <div v-if="isOffline" class="offline-notice">
      <van-empty image="network" description="网络连接已断开">
        <template #description>
          <p class="offline-desc">当前网络不可用，请检查网络设置</p>
        </template>
        <van-button round type="primary" size="small" @click="retry">重试</van-button>
      </van-empty>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'OfflineNotice',
  data() {
    return {
      isOffline: !navigator.onLine,
    };
  },
  methods: {
    retry() {
      // 尝试重新加载页面
      window.location.reload();
    },
    checkOnlineStatus() {
      this.isOffline = !navigator.onLine;
    },
  },
  mounted() {
    // 监听网络状态变化
    window.addEventListener('online', this.checkOnlineStatus);
    window.addEventListener('offline', this.checkOnlineStatus);
  },
  beforeDestroy() {
    // 清除监听器
    window.removeEventListener('online', this.checkOnlineStatus);
    window.removeEventListener('offline', this.checkOnlineStatus);
  },
};
</script>

<style lang="less" scoped>
.offline-notice {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.offline-desc {
  margin: 0 0 16px;
  color: #969799;
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter,
.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}
</style>
