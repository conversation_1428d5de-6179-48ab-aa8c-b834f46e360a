{"name": "tg-h5", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint", "preview": "node build/preview.js"}, "dependencies": {"axios": "^1.8.4", "core-js": "^3.6.5", "dayjs": "^1.11.13", "hammerjs": "^2.0.8", "js-cookie": "^3.0.5", "less": "^4.3.0", "less-loader": "^7.3.0", "postcss-px-to-viewport": "^1.1.1", "vant": "^2.13.6", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-photo-preview": "^1.1.3", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-service": "~4.5.19", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.8", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^6.2.2", "image-webpack-loader": "^8.1.0", "prettier": "^2.8.8", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["Android >= 4.0", "iOS >= 8", "Chrome >= 51", "last 2 versions"]}